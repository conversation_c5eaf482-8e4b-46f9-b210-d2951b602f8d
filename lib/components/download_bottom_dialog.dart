import 'package:dlyz_flutter/components/cache_image.dart';
import 'package:dlyz_flutter/manager/channel_manager.dart';
import 'package:dlyz_flutter/providers/download_provider.dart';
import 'package:dlyz_flutter/services/download/ALDownloader.dart';
import 'package:dlyz_flutter/services/download/internal/ALDownloaderStringExtension.dart';
import 'package:flutter/material.dart';

import '../beans/download_bean.dart';
import '../services/download/ALDownloaderStatus.dart';
import '../services/download/internal/ALDownloaderFileManagerDefault.dart';

class DownloadBottomDialog extends StatelessWidget {
  final DownloadBean game;

  const DownloadBottomDialog({
    super.key,
    required this.game
  });

  // 显示底部弹窗的静态方法，接收动态参数
  static void show({
    required BuildContext context,
    required DownloadBean game
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Stack(
        alignment: Alignment.topCenter,
        children: [
          DownloadBottomDialog(
            game: game
          ),
          // 顶部突出的游戏图标
          Positioned(
            top: 0, // 向上突出一半
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: CachedImage(
                  width: 80,
                  height: 80,
                  imageUrl: game.imageUrl
              ),
            ),
          ),
          // 关闭按钮(左侧)
          Positioned(
            left: 0,
            top: 35,
            child: IconButton(
              icon: const Icon(Icons.keyboard_arrow_down, size: 20),
              onPressed: () => Navigator.pop(context),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300,
      margin: const EdgeInsets.only(top: 40), // 顶部留出空间放置突出图标
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.fromLTRB(16, 50, 16, 16),
            child: Stack(
              children: [
                Center(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        game.title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // 按钮区域
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ValueListenableBuilder<String>(
                      valueListenable: game.downloadStatusNotifier,
                      builder: (context, value, child) {
                        if (value.isEqualTo("下载")) {
                          return _normalDownloadBtn();
                        } else {
                          return _downloadActionBtn();
                        }
                      }
                  ),
                  const SizedBox(height: 12),
                  // 打开按钮
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: Colors.black,
                        side: const BorderSide(color: Colors.grey),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      onPressed: () async {
                        await ChannelManager().openInstalledGame(packageName: game.packageName);
                      },
                      child: const Text(
                        '已安装游戏，直接打开',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 普通下载按钮
  Widget _normalDownloadBtn() {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.orange,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        onPressed: () async {
          DownloadProvider().handleDownload(game);
        },
        child: const Text(
          '下载官方包',
          style: TextStyle(
            fontSize: 16,
            color: Colors.black,
            fontWeight: FontWeight.w500,
          ),
        ),
      )
    );
  }

  /// 下载进度条
  Widget _downloadActionBtn() {
    bool isPaused = false;
    
    return Container(
      width: double.infinity,
      height: 52,
      decoration: BoxDecoration(
        color: Colors.orange,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            StatefulBuilder(
              builder: (context, setState) {
                WidgetsBinding.instance.addPostFrameCallback((_) async {
                  final status = await ALDownloader.getStatusForUrl(game.url);
                  setState(() {
                    isPaused = status == ALDownloaderStatus.paused;
                  });
                });
                 
                return IconButton(
                  icon: Icon(
                    isPaused ? Icons.play_arrow_rounded : Icons.pause,
                    color: Colors.white,
                  ),
                  onPressed: () async {
                    final status = await ALDownloader.getStatusForUrl(game.url);
                    
                    if (status == ALDownloaderStatus.downloading) {
                      ALDownloader.pause(game.url);
                      setState(() => isPaused = true);
                    } else {
                      ALDownloader.download(game.url);
                      setState(() => isPaused = false);
                    }
                  },
                );
              },
            ),
            const SizedBox(width: 8),
            // 动态进度显示
            Expanded(
              child: ValueListenableBuilder<String>(
                valueListenable: game.progressNotifier,
                builder: (context, progressValue, child) {
                  final progress = double.tryParse(progressValue.replaceAll('%', '')) ?? 0;

                  if (progress > 0) {
                    game.cacheProgress = progress;
                  }

                  final displayProgress = progress > 0 ? progress : game.cacheProgress ?? 0;
                  
                  return Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      LinearProgressIndicator(
                        value: displayProgress / 100,
                        backgroundColor: Colors.orange[300],
                        valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                        minHeight: 4.0,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children:[
                          Text(
                            "下载中 ${displayProgress.toStringAsFixed(2)}%",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          ValueListenableBuilder<String>(
                              valueListenable: game.downloadSpeedNotifier,
                              builder: (context, value, child) {
                                return Text(
                                    value,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                    )
                                );
                              }
                          )
                        ],
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}