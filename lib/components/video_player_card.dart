import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../manager/network_manager.dart';
import '../manager/channel_manager.dart';
import '../model/connectivity_result.dart';

class VideoPlayerCard extends StatefulWidget {
  final String videoUrl;
  final String? coverUrl;
  final bool autoPlay;
  final double height;

  const VideoPlayerCard({
    super.key,
    required this.videoUrl,
    this.coverUrl,
    this.autoPlay = true,
    this.height = 200,
  });

  @override
  State<VideoPlayerCard> createState() => _VideoPlayerCardState();
}

class _VideoPlayerCardState extends State<VideoPlayerCard> {
  VideoPlayerController? _controller;
  bool _isInitialized = false;
  bool _isBuffering = false;
  bool _isLoading = false;
  bool _isWifi = false;
  bool _isMobile = false;
  bool _showNonWifiPrompt = false;
  bool _allowMobilePlay = false;
  bool _pausedByNonWifi = false;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    _controller?.removeListener(_videoListener);
    _controller?.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _initConnectivity();
  }

  Future<void> _initConnectivity() async {
    try {
      final results = await NetworkManager().checkNetworkConnectivity();
      _updateNetworkFlags(results);

      // WiFi下支持自动播放；移动网络下显示提示
      if (_isWifi && widget.autoPlay) {
        // 延迟到下一帧，避免在首帧布局期间初始化视频导致抖动
        WidgetsBinding.instance.addPostFrameCallback((_) => _initializeInline());
      } else if (_isMobile) {
        setState(() {
          _showNonWifiPrompt = true;
          _allowMobilePlay = false;
        });
      }

      // 监听网络变化（直接监听 channel，避免全局 NetworkManager 订阅冲突）
      _connectivitySubscription = ChannelManager()
          .onConnectivityChanged
          .listen((results) {
        _handleConnectivityChanged(results);
      });
    } catch (_) {
      // 忽略异常，保持默认状态
    }
  }

  void _updateNetworkFlags(List<ConnectivityResult> results) {
    final bool wifi = results.contains(ConnectivityResult.wifi);
    final bool mobile = results.contains(ConnectivityResult.mobile);
    setState(() {
      _isWifi = wifi;
      _isMobile = mobile;
    });
  }

  void _handleConnectivityChanged(List<ConnectivityResult> results) {
    final wasWifi = _isWifi;
    final wasMobile = _isMobile;
    final nowWifi = results.contains(ConnectivityResult.wifi);
    final nowMobile = results.contains(ConnectivityResult.mobile);

    setState(() {
      _isWifi = nowWifi;
      _isMobile = nowMobile;
    });

    // 切到移动网络：暂停播放并弹提示
    if (!wasMobile && nowMobile) {
      if (_controller != null && _controller!.value.isPlaying) {
        _controller!.pause();
        _pausedByNonWifi = true;
      }
      setState(() {
        _allowMobilePlay = false;
        _showNonWifiPrompt = true;
      });
    }

    // 切回WiFi：关闭提示并在需要时恢复播放或按需自动播放
    if (!wasWifi && nowWifi) {
      setState(() {
        _showNonWifiPrompt = false;
      });
      if (_pausedByNonWifi && _controller != null && !_controller!.value.isPlaying) {
        _controller!.play();
        _pausedByNonWifi = false;
      } else if (!_isInitialized && widget.autoPlay) {
        _initializeInline();
      }
    }
  }

  Future<void> _initializeInline() async {
    // 非WiFi且未授权播放时，显示提示并阻止初始化
    if (!_isWifi && !_allowMobilePlay) {
      setState(() {
        _showNonWifiPrompt = true;
      });
      return;
    }
    if (_controller != null) return;
    setState(() {
      _isLoading = true;
    });
    try {
      final controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl));
      _controller = controller;
      controller.addListener(_videoListener);
      await controller.initialize();
      if (!mounted) return;
      setState(() {
        _isInitialized = true;
        _isLoading = false;
      });
      if (widget.autoPlay) {
        controller.play();
      }
    } catch (_) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
      _openFullscreen();
    }
  }

  void _videoListener() {
    if (!mounted || _controller == null) return;
    final buffering = _controller!.value.isBuffering;
    if (_isBuffering != buffering) {
      setState(() {
        _isBuffering = buffering;
      });
    }
  }

  Future<void> _openFullscreen() async {
    final Duration initial = _controller?.value.position ?? Duration.zero;
    final bool wasPlaying = _controller?.value.isPlaying ?? false;

    if (_controller != null && wasPlaying) {
      _controller!.pause();
    }

    final result = await Navigator.of(context).push<Map<String, dynamic>>(
      MaterialPageRoute(
        builder: (_) => VideoPlayerFullScreenPage(
          videoUrl: widget.videoUrl,
          initialPosition: initial,
          autoPlay: wasPlaying,
        ),
      ),
    );

    if (!mounted) return;
    if (result != null) {
      final Duration? returnedPos = result['position'] as Duration?;
      final bool playing = (result['isPlaying'] as bool?) ?? false;

      if (returnedPos != null) {
        if (_isInitialized) {
          await _controller!.seekTo(returnedPos);
        } else {
          _allowMobilePlay = true; // 全屏已播放过，视为允许移动播放
          await _initializeInline();
          if (_controller != null) {
            await _controller!.seekTo(returnedPos);
          }
        }
      }

      if (playing) {
        _allowMobilePlay = true;
        _controller?.play();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final controller = _controller;
    final isInitialized = _isInitialized && controller != null;

    return Container(
      width: double.infinity,
      height: widget.height,
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[200],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: isInitialized ? _buildInlinePlayer(controller) : _buildCover(),
      ),
    );
  }

  Widget _buildCover() {
    return GestureDetector(
      onTap: _initializeInline,
      child: Stack(
        alignment: Alignment.center,
        children: [
          if (widget.coverUrl != null && widget.coverUrl!.isNotEmpty)
            CachedNetworkImage(
              imageUrl: widget.coverUrl!,
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
            )
          else
            Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.grey[300],
              child: Icon(
                Icons.videocam,
                size: 48,
                color: Colors.grey[600],
              ),
            ),
          if (_isLoading)
            Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.black54,
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(color: Colors.white, strokeWidth: 3),
                    SizedBox(height: 8),
                    Text('视频加载中...', style: TextStyle(color: Colors.white, fontSize: 14)),
                  ],
                ),
              ),
            )
          else
            Container(
              width: 60,
              height: 60,
              decoration: const BoxDecoration(color: Colors.black54, shape: BoxShape.circle),
              child: const Icon(Icons.play_arrow, color: Colors.white, size: 30),
            ),
          if (!_isLoading)
            Positioned(
              bottom: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(color: Colors.black54, borderRadius: BorderRadius.circular(4)),
                child: const Text('视频', style: TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.w500)),
              ),
            ),

          // 非WiFi播放确认弹层（覆盖在封面之上）
          if (_showNonWifiPrompt) _buildNonWifiOverlay(),
        ],
      ),
    );
  }

  Widget _buildInlinePlayer(VideoPlayerController controller) {
    final Size videoSize = controller.value.size;
    final bool hasValidSize = videoSize.width > 0 && videoSize.height > 0;

    return Stack(
      children: [
        // 确保视频按照 cover 方式铺满容器，避免底部或上下留白
        Positioned.fill(
          child: hasValidSize
              ? FittedBox(
                  fit: BoxFit.cover,
                  child: SizedBox(
                    width: videoSize.width,
                    height: videoSize.height,
                    child: VideoPlayer(controller),
                  ),
                )
              : Center(
                  child: AspectRatio(
                    aspectRatio: controller.value.aspectRatio == 0
                        ? 16 / 9
                        : controller.value.aspectRatio,
                    child: VideoPlayer(controller),
                  ),
                ),
        ),
        if (_isBuffering)
          const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(color: Colors.white, strokeWidth: 3),
                SizedBox(height: 8),
                Text('缓冲中...', style: TextStyle(color: Colors.white, fontSize: 14)),
              ],
            ),
          ),
        _InlineControls(
          controller: controller,
          isBuffering: _isBuffering,
          onFullscreen: _openFullscreen,
        ),
      ],
    );
  }

  Widget _buildNonWifiOverlay() {
    return Positioned.fill(
      child: Container(
        color: Colors.black54,
        child: Center(
          child: Container(
            width: 280,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '当前为非Wi‑Fi网络',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 8),
                const Text(
                  '播放视频将消耗移动数据，是否继续播放？',
                  style: TextStyle(fontSize: 14, color: Colors.black87),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () {
                        // 取消：保持提示并不执行任何操作
                      },
                      child: const Text('取消'),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _allowMobilePlay = true;
                          _showNonWifiPrompt = false;
                          _pausedByNonWifi = false;
                        });
                        if (_isInitialized) {
                          _controller?.play();
                        } else {
                          _initializeInline();
                        }
                      },
                      child: const Text('继续播放'),
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _InlineControls extends StatefulWidget {
  final VideoPlayerController controller;
  final bool isBuffering;
  final VoidCallback onFullscreen;

  const _InlineControls({
    required this.controller,
    required this.isBuffering,
    required this.onFullscreen,
  });

  @override
  State<_InlineControls> createState() => _InlineControlsState();
}

class _InlineControlsState extends State<_InlineControls> {
  bool _showControls = true;
  Timer? _positionTicker;

  @override
  void initState() {
    super.initState();
    _hideLater();
    _positionTicker = Timer.periodic(const Duration(milliseconds: 500), (_) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  void _hideLater() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggle() {
    setState(() {
      _showControls = !_showControls;
    });
    if (_showControls) _hideLater();
  }

  String _fmt(Duration d) {
    final m = d.inMinutes;
    final s = d.inSeconds % 60;
    return '${m.toString().padLeft(2, '0')}:${s.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggle,
      child: Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            AnimatedOpacity(
              opacity: _showControls ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 300),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withOpacity(0.7),
                      Colors.transparent,
                      Colors.transparent,
                      Colors.black.withOpacity(0.7),
                    ],
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        IconButton(
                          onPressed: widget.onFullscreen,
                          icon: const Icon(Icons.fullscreen, color: Colors.white, size: 24),
                        ),
                      ],
                    ),
                    const Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton(
                          onPressed: () {
                            setState(() {
                              if (widget.controller.value.isPlaying) {
                                widget.controller.pause();
                              } else {
                                widget.controller.play();
                              }
                            });
                          },
                          icon: Icon(
                            widget.controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          Text(_fmt(widget.controller.value.position), style: const TextStyle(color: Colors.white, fontSize: 12)),
                          const SizedBox(width: 8),
                          Expanded(
                            child: VideoProgressIndicator(
                              widget.controller,
                              allowScrubbing: true,
                              colors: const VideoProgressColors(
                                playedColor: Colors.white,
                                bufferedColor: Colors.grey,
                                backgroundColor: Colors.black54,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(_fmt(widget.controller.value.duration), style: const TextStyle(color: Colors.white, fontSize: 12)),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _positionTicker?.cancel();
    super.dispose();
  }
}

class VideoPlayerFullScreenPage extends StatefulWidget {
  final String videoUrl;
  final Duration? initialPosition;
  final bool autoPlay;

  const VideoPlayerFullScreenPage({
    super.key,
    required this.videoUrl,
    this.initialPosition,
    this.autoPlay = true,
  });

  @override
  State<VideoPlayerFullScreenPage> createState() => _VideoPlayerFullScreenPageState();
}

class _VideoPlayerFullScreenPageState extends State<VideoPlayerFullScreenPage> {
  late VideoPlayerController _controller;
  bool _isInitialized = false;
  bool _hasError = false;
  String _errorMessage = '';
  bool _isBuffering = false;
  bool _isLandscape = false;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    try {
      _controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl));
      _controller.addListener(_videoListener);
      await _controller.initialize();
      if (!mounted) return;
      if (widget.initialPosition != null) {
        await _controller.seekTo(widget.initialPosition!);
      }
      final aspectRatio = _controller.value.aspectRatio;
      _isLandscape = aspectRatio > 1.0;
      if (_isLandscape) {
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight,
        ]);
      } else {
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
        ]);
      }
      setState(() {
        _isInitialized = true;
      });
      if (widget.autoPlay) {
        _controller.play();
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _hasError = true;
        _errorMessage = '视频加载失败: $e';
      });
    }
  }

  void _videoListener() {
    if (!mounted) return;
    final buffering = _controller.value.isBuffering;
    if (_isBuffering != buffering) {
      setState(() {
        _isBuffering = buffering;
      });
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_videoListener);
    _controller.dispose();
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    super.dispose();
  }

  void _returnWithResult() {
    final pos = _controller.value.position;
    final playing = _controller.value.isPlaying;
    Navigator.of(context).pop({'position': pos, 'isPlaying': playing});
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        _returnWithResult();
        return false;
      },
      child: Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: _hasError
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error_outline, color: Colors.white, size: 64),
                    const SizedBox(height: 16),
                    Text(_errorMessage, style: const TextStyle(color: Colors.white, fontSize: 16), textAlign: TextAlign.center),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _hasError = false;
                          _isInitialized = false;
                        });
                        _initializePlayer();
                      },
                      child: const Text('重试'),
                    ),
                  ],
                ),
              )
            : _isInitialized
                ? Stack(
                    children: [
                      Center(
                        child: AspectRatio(
                          aspectRatio: _controller.value.aspectRatio,
                          child: VideoPlayer(_controller),
                        ),
                      ),
                      _FullScreenControls(controller: _controller, isBuffering: _isBuffering),
                      Positioned(
                        top: 16,
                        left: 16,
                        child: GestureDetector(
                          onTap: _returnWithResult,
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: const BoxDecoration(color: Colors.black54, shape: BoxShape.circle),
                            child: const Icon(Icons.arrow_back, color: Colors.white, size: 24),
                          ),
                        ),
                      ),
                    ],
                  )
                : const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(color: Colors.white, strokeWidth: 3),
                        SizedBox(height: 16),
                        Text('正在加载视频...', style: TextStyle(color: Colors.white, fontSize: 16)),
                        SizedBox(height: 8),
                        Text('请稍候', style: TextStyle(color: Colors.white70, fontSize: 14)),
                      ],
                    ),
                  ),
        ),
      ),
    );
  }
}


class _FullScreenControls extends StatefulWidget {
  final VideoPlayerController controller;
  final bool isBuffering;

  const _FullScreenControls({required this.controller, required this.isBuffering});

  @override
  State<_FullScreenControls> createState() => _FullScreenControlsState();
}

class _FullScreenControlsState extends State<_FullScreenControls> {
  bool _showControls = true;
  Timer? _positionTicker;

  @override
  void initState() {
    super.initState();
    _hideLater();
    _positionTicker = Timer.periodic(const Duration(milliseconds: 500), (_) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  void _hideLater() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggle() {
    setState(() {
      _showControls = !_showControls;
    });
    if (_showControls) _hideLater();
  }

  String _fmt(Duration d) {
    final m = d.inMinutes;
    final s = d.inSeconds % 60;
    return '${m.toString().padLeft(2, '0')}:${s.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggle,
      child: Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            if (widget.isBuffering)
              const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(color: Colors.white, strokeWidth: 3),
                    SizedBox(height: 8),
                    Text('缓冲中...', style: TextStyle(color: Colors.white, fontSize: 14)),
                  ],
                ),
              ),
            AnimatedOpacity(
              opacity: _showControls ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 300),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withOpacity(0.7),
                      Colors.transparent,
                      Colors.transparent,
                      Colors.black.withOpacity(0.7),
                    ],
                  ),
                ),
                child: Column(
                  children: [
                    const Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton(
                          onPressed: () {
                            setState(() {
                              if (widget.controller.value.isPlaying) {
                                widget.controller.pause();
                              } else {
                                widget.controller.play();
                              }
                            });
                          },
                          icon: Icon(
                            widget.controller.value.isPlaying ? Icons.pause : Icons.play_arrow,
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          Text(_fmt(widget.controller.value.position), style: const TextStyle(color: Colors.white, fontSize: 12)),
                          const SizedBox(width: 8),
                          Expanded(
                            child: VideoProgressIndicator(
                              widget.controller,
                              allowScrubbing: true,
                              colors: const VideoProgressColors(
                                playedColor: Colors.white,
                                bufferedColor: Colors.grey,
                                backgroundColor: Colors.black54,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(_fmt(widget.controller.value.duration), style: const TextStyle(color: Colors.white, fontSize: 12)),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _positionTicker?.cancel();
    super.dispose();
  }
}


