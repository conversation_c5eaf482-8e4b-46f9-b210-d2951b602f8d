import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// 通用缓存图片组件
class CachedImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Duration fadeInDuration;
  final Duration fadeOutDuration;
  final Widget? placeholder;
  final Widget? errorWidget;

  const CachedImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit,
    this.fadeInDuration = const Duration(milliseconds: 500),
    this.fadeOutDuration = const Duration(milliseconds: 500),
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? double.infinity,
      height: height ?? double.infinity,
      child: CachedNetworkImage(
        imageUrl: imageUrl,
        height: height ?? double.infinity,
        fit: fit ?? BoxFit.cover,
        fadeInDuration: fadeInDuration,
        fadeOutDuration: fadeOutDuration,
        placeholder: (context, url) => placeholder ?? _defaultPlaceholder,
        errorWidget: (context, url, error) => errorWidget ?? _defaultErrorWidget,
      ),
    );
  }

  /// 默认占位符 - 居中的圆形进度指示器
  Widget get _defaultPlaceholder {
    return SizedBox(
      width: width ?? double.infinity,
      height: height ?? double.infinity,
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  /// 默认错误组件 - 错误图标
  Widget get _defaultErrorWidget {
    return SizedBox(
      width: width ?? double.infinity,
      height: height ?? double.infinity,
      child: const Icon(Icons.image),
    );
  }
}