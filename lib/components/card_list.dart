import 'package:dlyz_flutter/components/cache_image.dart';
import 'package:flutter/material.dart';

// 列表数据模型
class InformationList {
  final String title;
  final String subTitle;
  final String tap;
  final List<InformationItem> list;

  InformationList({
    required this.title,
    required this.subTitle,
    required this.tap,
    required this.list,
  });
}

// 卡片数据模型
class InformationItem {
  final String imageUrl;
  final String content;

  InformationItem({required this.imageUrl, required this.content});
}

class InformationCardList extends StatefulWidget {
  const InformationCardList({super.key, required this.informationList});

  final InformationList informationList;

  @override
  State<InformationCardList> createState() => _InformationCardListState();
}

class _InformationCardListState extends State<InformationCardList> {
  int _selectedCategoryIndex = 0;
  bool _hasContent = false;

  @override
  void initState() {
    super.initState();
    // 初始化时检查内容状态
    _updateHasContentStatus();
  }

  @override
  void didUpdateWidget(covariant InformationCardList oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当信息列表更新时重新检查内容状态
    if (widget.informationList != oldWidget.informationList) {
      _updateHasContentStatus();
    }
  }

  // 更新内容状态的方法
  void _updateHasContentStatus() {
    // 检查是否有任何item的content不为空
    final hasContent = widget.informationList.list.any(
      (item) => item.content.trim().isNotEmpty,
    );
    setState(() {
      _hasContent = hasContent;
    });
  }

  @override
  Widget build(BuildContext context) {
    return _buildInformationList(widget.informationList);
  }

  // 构建资讯列表
  Widget _buildInformationList(InformationList infoList) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              infoList.title,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
          if (infoList.subTitle.isNotEmpty) ...[
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: Row(
                children: [
                  // 当tap不为空时才显示分类标签
                  if (infoList.tap.trim().isNotEmpty) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.orange, width: 1),
                        borderRadius: BorderRadius.circular(4),
                        // 背景设为透明
                        color: Colors.transparent,
                      ),
                      child: Text(
                        infoList.tap,
                        style: TextStyle(
                          color: Colors.orange,
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 6),
                  ],
                  Expanded(
                    child: Text(
                      infoList.subTitle,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              // 根据_hasContent动态设置宽高比
              childAspectRatio: _hasContent ? 1.15 : 1.25,
            ),
            itemCount: infoList.list.length,
            itemBuilder: (context, index) {
              final item = infoList.list[index];
              return _buildInformationCard(item);
            },
          ),
        ],
      ),
    );
  }

  // 构建资讯卡片
  Widget _buildInformationCard(InformationItem item) {
    return InformationCard(
      imageUrl: item.imageUrl,
      content: item.content,
      onTap: () {
        // 点击事件处理
        debugPrint("点击了${item.content}");
      },
    );
  }
}

// 卡片组件
class InformationCard extends StatelessWidget {
  final String imageUrl;
  final String content;
  final VoidCallback? onTap;

  const InformationCard({
    super.key,
    required this.imageUrl,
    required this.content,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 资讯图片
            ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(8)),
              child: CachedImage(height: 100, imageUrl: imageUrl),
            ),
            const SizedBox(height: 6),
            // 资讯标题
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 1),
              child: Text(
                content,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(fontSize: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
