import 'package:dlyz_flutter/components/irregular_button.dart';
import 'package:flutter/material.dart';
import '../common/dl_color.dart';
import '../model/game_package_info.dart';
import 'cache_image.dart';

// 选择游戏包体底部弹窗
class SelectGamePackageBottomDialog extends StatefulWidget {
  final List<GamePackageInfo> packages;
  final ValueChanged<GamePackageInfo>? onConfirm;
  final VoidCallback? onCancel;

  const SelectGamePackageBottomDialog({
    super.key,
    required this.packages,
    this.onConfirm,
    this.onCancel,
  });

  // 显示底部弹窗的静态方法
  static void show({
    required BuildContext context,
    required List<GamePackageInfo> packages,
    ValueChanged<GamePackageInfo>? onConfirm,
    VoidCallback? onCancel,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => SelectGamePackageBottomDialog(
        packages: packages,
        onConfirm: onConfirm,
        onCancel: onCancel,
      ),
    );
  }

  @override
  State<SelectGamePackageBottomDialog> createState() => _SelectGamePackageBottomDialogState();
}

class _SelectGamePackageBottomDialogState extends State<SelectGamePackageBottomDialog> {
  late GamePackageInfo _selectedPackage;

  @override
  void initState() {
    super.initState();
    // 默认选中第一个包体
    _selectedPackage = widget.packages.first;
  }

  @override
  Widget build(BuildContext context) {
    // 计算弹窗高度：当包体数量超过6个时增加高度
    final bool isMoreThan6Packages = widget.packages.length > 6;
    final double dialogHeight = isMoreThan6Packages
        ? MediaQuery.of(context).size.height * 0.5
        : MediaQuery.of(context).size.height * 0.3;

    return Container(
      height: dialogHeight,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // 顶部标题栏
          _buildHeader(),

          // 包体选择列表
          Expanded(
            child: _buildPackageList(isMoreThan6Packages),
          ),

          // 底部按钮
          _buildBottomButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
      child: Center(
        child: Text(
          '选择游戏包体',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ),
    );
  }

  Widget _buildPackageList(bool isScrollable) {
    return GridView.builder(
      padding: const EdgeInsets.all(20),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 0.9,
      ),
      // 当包体数量超过6个时启用滚动
      physics: isScrollable ? const AlwaysScrollableScrollPhysics() : const NeverScrollableScrollPhysics(),
      // 收缩包裹内容
      shrinkWrap: true,
      itemCount: widget.packages.length,
      itemBuilder: (context, index) {
        final package = widget.packages[index];
        final isSelected = package.id == _selectedPackage.id;

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedPackage = package;
            });
          },
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 包体图标
              Stack(
                alignment: Alignment.topRight,
                clipBehavior: Clip.none,
                children: [
                  // 添加边框效果
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: isSelected
                              ? Border.all(color: DLColor.primary, width: 2.0)
                              : Border.all(color: Colors.transparent),
                    ),
                    padding: const EdgeInsets.all(2),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: CachedImage(
                          width: 76,
                          height: 76,
                          imageUrl: package.iconUrl
                      ),
                    ),
                  ),
  
                  // 官方标签
                  if (package.isOfficial) ...[
                    Positioned(
                      top: -5,
                      right: -5,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: DLColor.tagBackground,
                          borderRadius: BorderRadius.only(topLeft: Radius.circular(10.0), topRight: Radius.circular(10.0), bottomRight: Radius.circular(10.0)),
                        ),
                        child: const Text(
                          '官方',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    )
                  ]
                ],
              ),
              // 添加tag文字，如果不为空
              if (package.tag.isNotEmpty) ...[
                SizedBox(
                  width: 86,
                  child: Text(
                    package.tag,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.black54,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // 取消按钮
          Expanded(
            child: IrregularButton(
                text: '取消',
                onPressed: () {
                  if (widget.onCancel != null) {
                    widget.onCancel!();
                  }
                  Navigator.pop(context);
                }
            )
          ),

          const SizedBox(width: 16),

          // 打开按钮
          Expanded(
            child: IrregularButton(
                text: '打开',
                textColor: Colors.white,
                backgroundColor: DLColor.primary,
                onPressed: () {
                  if (widget.onConfirm != null) {
                    widget.onConfirm!(_selectedPackage);
                  }
                  Navigator.pop(context);
                }
            )
          ),
        ],
      ),
    );
  }
}