import 'package:flutter/material.dart';
import 'cache_image.dart';

// 游戏包体数据模型
class GamePackage {
  final String id;
  final String name;
  final String iconUrl;
  final String packageName;
  final bool isOfficial;

  const GamePackage({
    required this.id,
    required this.name,
    required this.iconUrl,
    required this.packageName,
    required this.isOfficial,
  });
}

// 选择游戏包体底部弹窗
class SelectGamePackageBottomDialog extends StatefulWidget {
  final List<GamePackage> packages;
  final ValueChanged<GamePackage>? onConfirm;
  final VoidCallback? onCancel;

  const SelectGamePackageBottomDialog({
    super.key,
    required this.packages,
    this.onConfirm,
    this.onCancel,
  });

  // 显示底部弹窗的静态方法
  static void show({
    required BuildContext context,
    required List<GamePackage> packages,
    ValueChanged<GamePackage>? onConfirm,
    VoidCallback? onCancel,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => SelectGamePackageBottomDialog(
        packages: packages,
        onConfirm: onConfirm,
        onCancel: onCancel,
      ),
    );
  }

  @override
  State<SelectGamePackageBottomDialog> createState() => _SelectGamePackageBottomDialogState();
}

class _SelectGamePackageBottomDialogState extends State<SelectGamePackageBottomDialog> {
  late GamePackage _selectedPackage;

  @override
  void initState() {
    super.initState();
    // 默认选中第一个包体
    _selectedPackage = widget.packages.first;
  }

  @override
  Widget build(BuildContext context) {
    // 计算弹窗高度：当包体数量超过6个时增加高度
    final bool isMoreThan6Packages = widget.packages.length > 6;
    final double dialogHeight = isMoreThan6Packages
        ? MediaQuery.of(context).size.height * 0.5
        : MediaQuery.of(context).size.height * 0.3;

    return Container(
      height: dialogHeight,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // 顶部标题栏
          _buildHeader(),

          // 包体选择列表
          Expanded(
            child: _buildPackageList(isMoreThan6Packages),
          ),

          // 底部按钮
          _buildBottomButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
      child: Center(
        child: Text(
          '选择游戏包体',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
      ),
    );
  }

  Widget _buildPackageList(bool isScrollable) {
    return GridView.builder(
      padding: const EdgeInsets.all(20),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        mainAxisSpacing: 20,
        crossAxisSpacing: 20,
      ),
      // 当包体数量超过6个时启用滚动
      physics: isScrollable ? const AlwaysScrollableScrollPhysics() : const NeverScrollableScrollPhysics(),
      // 收缩包裹内容
      shrinkWrap: true,
      itemCount: widget.packages.length,
      itemBuilder: (context, index) {
        final package = widget.packages[index];
        final isSelected = package.id == _selectedPackage.id;

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedPackage = package;
            });
          },
          child: Column(
            children: [
              // 包体图标
              Stack(
                alignment: Alignment.topRight,
                children: [

                  ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: CachedImage(
                        width: 80,
                        height: 80,
                        imageUrl: package.iconUrl
                    ),
                  ),

                  // 官方标签
                  if (package.isOfficial)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Text(
                        '官方',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                  // 选中图标
                  if (isSelected)
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          color: Colors.orange,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Icon(
                          Icons.check,
                          size: 14,
                          color: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // 取消按钮
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                if (widget.onCancel != null) {
                  widget.onCancel!();
                }
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[200],
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
                minimumSize: const Size(double.infinity, 44),
              ),
              child: const Text(
                '取消',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.black87,
                ),
              ),
            ),
          ),

          const SizedBox(width: 16),

          // 打开按钮
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                if (widget.onConfirm != null) {
                  widget.onConfirm!(_selectedPackage);
                }
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
                minimumSize: const Size(double.infinity, 44),
              ),
              child: const Text(
                '打开',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}