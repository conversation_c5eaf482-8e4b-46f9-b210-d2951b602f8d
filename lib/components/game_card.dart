
import 'package:dlyz_flutter/services/download/internal/ALDownloaderStringExtension.dart';
import 'package:flutter/material.dart';
import '../beans/download_bean.dart';
import '../manager/channel_manager.dart';
import 'bottom_sheet_dialog.dart';
import 'cache_image.dart';

class GameCard extends StatefulWidget {
  final DownloadBean game;
  final VoidCallback? onTap;
  final VoidCallback? onDownload;

  const GameCard({
    super.key,
    required this.game,
    this.onTap,
    this.onDownload,
  });

  @override
  State<GameCard> createState() => _GameCardState();
}

class _GameCardState extends State<GameCard> {
  bool _isInstalled = false;

  @override
  void initState() {
    super.initState();
    _checkInstallation();
  }

  Future<void> _checkInstallation() async {
    if (widget.game.packageName == null) return;

    try {
      final isInstalled = await ChannelManager().checkGameInstalled(packageName: widget.game.packageName);
      if (mounted) {
        setState(() {
          _isInstalled = isInstalled!;
        });
      }
    } catch (e) {
      print("检查游戏是否安装失败: $e");
    }
  }

  void _openGame() {
    ChannelManager().openInstalledGame(packageName: widget.game.packageName);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.0),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // 游戏宣传图
              Stack(
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(12.0)),
                    child: CachedImage(
                      imageUrl: widget.game.imageUrl,
                      height: 180,
                    ),
                  ),
                  if (widget.game.tag != null) ...[
                    Positioned(
                      top: 10,
                      left: 10,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          widget.game.tag!,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                  if (widget.game.promotionText != null) ...[
                    Positioned(
                      bottom: 20,
                      left: 0,
                      right: 0,
                      child: Center(
                        child: Text(
                          widget.game.promotionText!,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            shadows: [
                              Shadow(
                                color: Colors.black54,
                                offset: Offset(1, 1),
                                blurRadius: 3,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),

              // 游戏信息和下载按钮
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.game.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          widget.game.subtitle,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    _isInstalled ? ElevatedButton(
                        onPressed: () => _openGame(),
                        style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6),
                            ),
                            padding: EdgeInsets.zero,
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            side: BorderSide.none,
                            minimumSize: Size(90, 30),
                            maximumSize: Size(90, 30)
                        ),
                        child: Text(
                            "打开游戏",
                            style: TextStyle(
                              color: Colors.black
                            ),
                        )
                    ) : ValueListenableBuilder<String>(
                        valueListenable: widget.game.downloadStatusNotifier,
                        builder: (context, value, child) {
                          if (value.isEqualTo("下载中")) {
                            return ValueListenableBuilder<String>(
                                valueListenable: widget.game.progressNotifier,
                                builder: (context, progressValue, child) {
                                  final progress = double.tryParse(progressValue.replaceAll('%', '')) ?? 0;

                                  if (progress > 0) {
                                    widget.game.cacheProgress = progress;
                                  }

                                  final displayProgress = progress > 0 ? progress : widget.game.cacheProgress ?? 0;
                                  return ElevatedButton(
                                      onPressed: widget.onDownload,
                                      style: ElevatedButton.styleFrom(
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(6),
                                          ),
                                          padding: EdgeInsets.zero,
                                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                          side: BorderSide.none,
                                          minimumSize: Size(90, 30),
                                          maximumSize: Size(90, 30)
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(6),
                                        child: Stack(
                                          children: [
                                            LinearProgressIndicator(
                                              value: displayProgress / 100,
                                              backgroundColor: Colors.white,
                                              valueColor: const AlwaysStoppedAnimation<Color>(Colors.orange),
                                              minHeight: 30.0,
                                            ),
                                            Center(
                                              child: Text(
                                                progressValue,
                                                style: TextStyle(
                                                  color: Colors.black,
                                                ),
                                              ),
                                            )
                                          ],
                                        ),
                                      )
                                  );
                                }
                            );
                          } else if (value.isEqualTo("继续")) {
                            return ElevatedButton(
                                onPressed: widget.onDownload,
                                style: ElevatedButton.styleFrom(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    padding: EdgeInsets.zero,
                                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                    side: BorderSide.none,
                                    minimumSize: Size(90, 30),
                                    maximumSize: Size(90, 30)
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(6),
                                  child: Stack(
                                    children: [
                                      LinearProgressIndicator(
                                        value: widget.game.cacheProgress! / 100,
                                        backgroundColor: Colors.white,
                                        valueColor: const AlwaysStoppedAnimation<Color>(Colors.orange),
                                        minHeight: 30.0,
                                      ),
                                      Center(
                                        child: Text(
                                          value,
                                          style: TextStyle(
                                            color: Colors.black,
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                )
                            );
                          } else {
                            return ElevatedButton(
                                onPressed: widget.onDownload,
                                style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.orange,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    padding: EdgeInsets.zero,
                                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                    side: BorderSide.none,
                                    minimumSize: Size(90, 30),
                                    maximumSize: Size(90, 30)
                                ),
                                child: Text(
                                    value,
                                    style: TextStyle(
                                        color: Colors.black,
                                    ),
                                )
                            );
                          }
                        }
                    ),
                  ],
                ),
              ),
            ],
          ),
        )
      ),
    );
  }
}