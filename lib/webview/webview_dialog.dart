import 'package:flutter/material.dart';
import 'webview_wrapper.dart';
import 'js_bridge.dart';

/// WebView弹窗组件
class WebViewDialog extends StatefulWidget {
  final String url;
  final String title;
  final bool showToolBar;
  final VoidCallback? onClose;
  final JSBridge? jsBridge;

  const WebViewDialog({
    super.key,
    required this.url,
    required this.title,
    this.showToolBar = false,
    this.onClose,
    this.jsBridge,
  });

  @override
  State<WebViewDialog> createState() => _WebViewDialogState();
}

class _WebViewDialogState extends State<WebViewDialog> {
  late WebViewWrapperController _webViewController;

  @override
  void initState() {
    super.initState();
    _webViewController = WebViewWrapperController();
  }



    @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: SizedBox(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: Container(
          color: Colors.transparent,
          child: WebViewWrapper(
            controller: _webViewController,
            initialUrl: widget.url,
            jsBridge: widget.jsBridge,
            backgroundColor: Colors.transparent,
            onPageFinished: () {
              
            },
          ),
        ),
      ),
    );
  }
} 