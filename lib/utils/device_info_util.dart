import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';

import 'package:package_info_plus/package_info_plus.dart';

import '../model/connectivity_result.dart';

/// 设备信息工具类
class DeviceInfoUtil {
  static DeviceInfoPlugin? _deviceInfoPlugin;
  static PackageInfo? _packageInfo;
  
  /// 获取设备信息
  static Future<Map<String, dynamic>> getDeviceInfo() async {
    _deviceInfoPlugin ??= DeviceInfoPlugin();
    _packageInfo ??= await PackageInfo.fromPlatform();
    
    final Map<String, dynamic> deviceInfo = {};
    
    if (Platform.isAndroid) {
      deviceInfo.addAll({
        'os': 'android',
        'dev': 'a4bc1a02bb27a18870806fff77ebe5d6', //todo 后续生成
        'oaid': '', // Android 广告ID，需要通过专门的插件获取
      });
    } else if (Platform.isIOS) {
      final iosInfo = await _deviceInfoPlugin!.iosInfo;
      deviceInfo.addAll({
        'os': 'ios',
        'dev': '${iosInfo.name}_${iosInfo.model}',
        'caid': '', // iOS 广告ID，需要通过专门的插件获取
      });
    }
    
    return deviceInfo;
  }

  /// 解析网络状态
  static List<ConnectivityResult> parseConnectivityResults(List<String> states) {
    return states.map((state) {
      switch (state.trim()) {
        case 'bluetooth':
          return ConnectivityResult.bluetooth;
        case 'wifi':
          return ConnectivityResult.wifi;
        case 'ethernet':
          return ConnectivityResult.ethernet;
        case 'mobile':
          return ConnectivityResult.mobile;
        case 'vpn':
          return ConnectivityResult.vpn;
        case 'other':
          return ConnectivityResult.other;
        default:
          return ConnectivityResult.none;
      }
    }).toList();
  }
}