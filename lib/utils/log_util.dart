import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:logging/logging.dart';

/// 日志级别
enum LogLevel {
  debug,
  info,
  warning,
  error,
}

/// 日志工具类
class LogUtil {
  static bool _isEnabled = kDebugMode; // 默认在Debug模式下启用
  static LogLevel _minLevel = LogLevel.debug;
  static String _tag = 'DLYZ';
  static late Logger _logger;
  static bool _isInitialized = false;

  /// 初始化日志系统
  static void init() {
    if (_isInitialized) return;
    
    _logger = Logger(_tag);
    
    // 配置日志级别
    Logger.root.level = Level.ALL;
    
    // 配置日志监听器，直接输出到系统日志
    Logger.root.onRecord.listen((record) {
      if (Platform.isAndroid) {
        // 在Android上使用系统日志
        developer.log(
          record.message,
          time: record.time,
          level: _convertToSystemLevel(record.level),
          name: record.loggerName,
          error: record.error,
          stackTrace: record.stackTrace,
        );
      } else {
        // 在其他平台使用标准输出
        print('${record.time} [${record.level.name}] ${record.loggerName}: ${record.message}');
      }
    });
    
    _isInitialized = true;
  }

  /// 转换日志级别到系统级别
  static int _convertToSystemLevel(Level level) {
    if (level.value >= Level.SEVERE.value) return 1000; // ERROR
    if (level.value >= Level.WARNING.value) return 900; // WARNING  
    if (level.value >= Level.INFO.value) return 800;    // INFO
    return 500; // DEBUG
  }

  /// 设置是否启用日志
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }

  /// 设置最低日志级别
  static void setMinLevel(LogLevel level) {
    _minLevel = level;
  }

  /// 设置全局标签
  static void setTag(String tag) {
    _tag = tag;
  }

  /// Debug日志
  static void d(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    if (!_isInitialized) init();
    _logWithLevel(Level.FINE, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Info日志
  static void i(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    if (!_isInitialized) init();
    _logWithLevel(Level.INFO, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Warning日志
  static void w(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    if (!_isInitialized) init();
    _logWithLevel(Level.WARNING, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Error日志
  static void e(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    if (!_isInitialized) init();
    _logWithLevel(Level.SEVERE, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// 使用指定级别记录日志
  static void _logWithLevel(Level level, String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    if (!_isEnabled) return;
    
    final actualTag = tag ?? _tag;
    final logger = Logger(actualTag);
    
    String fullMessage = message;
    if (error != null) {
      fullMessage += '\nError: $error';
    }
    if (stackTrace != null) {
      fullMessage += '\nStackTrace: $stackTrace';
    }
    
    logger.log(level, fullMessage, error, stackTrace);
  }

  /// 网络请求日志
  static void request(String url, {String method = 'GET', Map<String, dynamic>? params, Map<String, dynamic>? headers}) {
    if (!_isInitialized) init();
    if (!_isEnabled) return;
    
    final buffer = StringBuffer();
    buffer.writeln('🌐 HTTP Request');
    buffer.writeln('Method: $method');
    buffer.writeln('URL: $url');
    
    if (headers != null && headers.isNotEmpty) {
      buffer.writeln('Headers: $headers');
    }
    
    if (params != null && params.isNotEmpty) {
      buffer.writeln('Params: $params');
    }
    
    _logWithLevel(Level.FINE, buffer.toString(), tag: '📤 NETWORK');
  }

  /// 网络响应日志
  static void response(String url, int statusCode, dynamic data, {String method = 'GET', int? duration}) {
    if (!_isInitialized) init();
    if (!_isEnabled) return;
    
    final buffer = StringBuffer();
    buffer.writeln('🌐 HTTP Response');
    buffer.writeln('Method: $method');
    buffer.writeln('URL: $url');
    buffer.writeln('Status Code: $statusCode');
    
    if (duration != null) {
      buffer.writeln('Duration: ${duration}ms');
    }
    
    if (data != null) {
      final dataStr = data.toString();
      if (dataStr.length > 1000) {
        buffer.writeln('Response: ${dataStr.substring(0, 1000)}...[truncated]');
      } else {
        buffer.writeln('Response: $dataStr');
      }
    }
    
    _logWithLevel(Level.FINE, buffer.toString(), tag: '📥 NETWORK');
  }

  /// 页面生命周期日志
  static void lifecycle(String pageName, String lifecycle) {
    if (!_isInitialized) init();
    if (!_isEnabled) return;
    
    _logWithLevel(Level.FINE, '$pageName - $lifecycle', tag: '🔄 LIFECYCLE');
  }

  /// 用户行为日志
  static void action(String action, {Map<String, dynamic>? params}) {
    if (!_isInitialized) init();
    if (!_isEnabled) return;
    
    final message = params != null ? '$action: $params' : action;
    _logWithLevel(Level.INFO, message, tag: '👆 ACTION');
  }

  /// 性能日志
  static void performance(String operation, int duration) {
    if (!_isInitialized) init();
    if (!_isEnabled) return;
    
    final level = duration > 1000 ? Level.WARNING : Level.INFO;
    final emoji = duration > 1000 ? '🐌' : '⚡';
    _logWithLevel(level, '$operation took ${duration}ms', tag: '$emoji PERFORMANCE');
  }

  /// 代码块执行时间测量
  static Future<T> measureTime<T>(String operation, Future<T> Function() block) async {
    if (!_isInitialized) init();
    if (!_isEnabled) {
      return await block();
    }
    
    final stopwatch = Stopwatch()..start();
    try {
      final result = await block();
      stopwatch.stop();
      performance(operation, stopwatch.elapsedMilliseconds);
      return result;
    } catch (error) {
      stopwatch.stop();
      e(operation, error: error);
      rethrow;
    }
  }

  /// 同步代码块执行时间测量
  static T measureTimeSync<T>(String operation, T Function() block) {
    if (!_isInitialized) init();
    if (!_isEnabled) {
      return block();
    }
    
    final stopwatch = Stopwatch()..start();
    try {
      final result = block();
      stopwatch.stop();
      performance(operation, stopwatch.elapsedMilliseconds);
      return result;
    } catch (error) {
      stopwatch.stop();
      e('$operation failed after ${stopwatch.elapsedMilliseconds}ms', error: error);
      rethrow;
    }
  }
}