import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// 日志级别
enum LogLevel {
  debug,
  info,
  warning,
  error,
}

/// 日志工具类
class LogUtil {
  static bool _isEnabled = kDebugMode; // 默认在Debug模式下启用
  static LogLevel _minLevel = LogLevel.debug;
  static String _tag = 'DLYZ';

  /// 设置是否启用日志
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }

  /// 设置最低日志级别
  static void setMinLevel(LogLevel level) {
    _minLevel = level;
  }

  /// 设置全局标签
  static void setTag(String tag) {
    _tag = tag;
  }

  /// Debug日志
  static void d(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.debug, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Info日志
  static void i(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.info, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Warning日志
  static void w(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.warning, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Error日志
  static void e(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.error, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// 网络请求日志
  static void request(String url, {String method = 'GET', Map<String, dynamic>? params, Map<String, dynamic>? headers}) {
    if (!_isEnabled || _minLevel.index > LogLevel.debug.index) return;
    
    final buffer = StringBuffer();
    buffer.writeln('🌐 HTTP Request');
    buffer.writeln('Method: $method');
    buffer.writeln('URL: $url');
    
    if (headers != null && headers.isNotEmpty) {
      buffer.writeln('Headers: $headers');
    }
    
    if (params != null && params.isNotEmpty) {
      buffer.writeln('Params: $params');
    }
    
    _printLog('📤 NETWORK', buffer.toString(), LogLevel.debug);
  }

  /// 网络响应日志
  static void response(String url, int statusCode, dynamic data, {String method = 'GET', int? duration}) {
    if (!_isEnabled || _minLevel.index > LogLevel.debug.index) return;
    
    final buffer = StringBuffer();
    buffer.writeln('🌐 HTTP Response');
    buffer.writeln('Method: $method');
    buffer.writeln('URL: $url');
    buffer.writeln('Status Code: $statusCode');
    
    if (duration != null) {
      buffer.writeln('Duration: ${duration}ms');
    }
    
    if (data != null) {
      final dataStr = data.toString();
      if (dataStr.length > 1000) {
        buffer.writeln('Response: ${dataStr.substring(0, 1000)}...[truncated]');
      } else {
        buffer.writeln('Response: $dataStr');
      }
    }
    
    _printLog('📥 NETWORK', buffer.toString(), LogLevel.debug);
  }

  /// 页面生命周期日志
  static void lifecycle(String pageName, String lifecycle) {
    if (!_isEnabled || _minLevel.index > LogLevel.debug.index) return;
    
    _printLog('🔄 LIFECYCLE', '$pageName - $lifecycle', LogLevel.debug);
  }

  /// 用户行为日志
  static void action(String action, {Map<String, dynamic>? params}) {
    if (!_isEnabled || _minLevel.index > LogLevel.info.index) return;
    
    final message = params != null ? '$action: $params' : action;
    _printLog('👆 ACTION', message, LogLevel.info);
  }

  /// 性能日志
  static void performance(String operation, int duration) {
    if (!_isEnabled || _minLevel.index > LogLevel.info.index) return;
    
    final level = duration > 1000 ? LogLevel.warning : LogLevel.info;
    final emoji = duration > 1000 ? '🐌' : '⚡';
    _printLog('$emoji PERFORMANCE', '$operation took ${duration}ms', level);
  }

  /// 私有日志方法
  static void _log(LogLevel level, String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    if (!_isEnabled || level.index < _minLevel.index) return;
    
    final actualTag = tag ?? _tag;
    
    String fullMessage = message;
    if (error != null) {
      fullMessage += '\nError: $error';
    }
    if (stackTrace != null) {
      fullMessage += '\nStackTrace: $stackTrace';
    }
    
    _printLog(actualTag, fullMessage, level);
  }

  /// 打印日志
  static void _printLog(String tag, String message, LogLevel level) {
    final timestamp = DateTime.now().toString().substring(11, 23); // HH:mm:ss.SSS
    final levelEmoji = _getLevelEmoji(level);
    final levelName = level.name.toUpperCase().padRight(7);
    
    final formattedMessage = '[$timestamp] $levelEmoji $levelName [$tag] $message';
    
    // 使用 developer.log 替代 print
    developer.log(
      formattedMessage,
      time: DateTime.now(),
      level: _getLogLevel(level),
      name: tag,
    );
  }

  /// 获取日志级别对应的emoji
  static String _getLevelEmoji(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return '🔍';
      case LogLevel.info:
        return 'ℹ️';
      case LogLevel.warning:
        return '⚠️';
      case LogLevel.error:
        return '❌';
    }
  }

  /// 获取系统日志级别
  static int _getLogLevel(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return 500;
      case LogLevel.info:
        return 800;
      case LogLevel.warning:
        return 900;
      case LogLevel.error:
        return 1000;
    }
  }

  /// 代码块执行时间测量
  static Future<T> measureTime<T>(String operation, Future<T> Function() block) async {
    if (!_isEnabled) {
      return await block();
    }
    
    final stopwatch = Stopwatch()..start();
    try {
      final result = await block();
      stopwatch.stop();
      performance(operation, stopwatch.elapsedMilliseconds);
      return result;
    } catch (e) {
      stopwatch.stop();
      e;
      rethrow;
    }
  }

  /// 同步代码块执行时间测量
  static T measureTimeSync<T>(String operation, T Function() block) {
    if (!_isEnabled) {
      return block();
    }
    
    final stopwatch = Stopwatch()..start();
    try {
      final result = block();
      stopwatch.stop();
      performance(operation, stopwatch.elapsedMilliseconds);
      return result;
    } catch (e) {
      stopwatch.stop();
      LogUtil.e('$operation failed after ${stopwatch.elapsedMilliseconds}ms', error: e);
      rethrow;
    }
  }
}