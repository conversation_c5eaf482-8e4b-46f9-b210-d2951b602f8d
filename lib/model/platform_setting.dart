/// 平台设置
class PlatformSetting {
  final int key;
  final String desc;
  final bool value;

  PlatformSetting({
    required this.key,
    required this.desc,
    required this.value,
  });

  factory PlatformSetting.fromJson(Map<String, dynamic> json) {
    return PlatformSetting(
      key: json['key'] ?? 0,
      desc: json['desc'] ?? '',
      value: json['value'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'desc': desc,
      'value': value,
    };
  }
}