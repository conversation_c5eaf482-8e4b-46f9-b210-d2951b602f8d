import 'platform_setting.dart';

/// 网站设置
class SetSite {
  final String? siteManage;
  final String? apiFreq;
  final String siteUrl;
  final String siteInstall;
  final String siteCover;
  final String siteMinimumAmount;
  final List<bool> loginaes;
  final List<String> aesKey;
  final List<String> aesIv;
  final bool usernameLoginIsdisplay;
  final String openApiLog;
  final int threadTab;
  final String version;
  final String siteName;
  final String siteTitle;
  final int siteFabu;
  final String siteKeywords;
  final String siteIntroduction;
  final String siteMode;
  final String openExtFields;
  final bool siteClose;
  final String siteCloseMsg;
  final String siteFavicon;
  final String siteLogo;
  final String siteHeaderLogo;
  final String siteBackgroundImage;
  final String siteUserBackgroundImage;
  final int siteShareSwitch;
  final String siteSearchSwitch;
  final String siteSignInSwitch;
  final int siteSpaceCollectionPageSwitch;
  final String siteAuthSwitch;
  final String siteLoginType;
  final String sitePcMixThreadSwitch;
  final String siteWechatLoginPid;
  final String siteWechatLoginGid;
  final String siteSignInUrl;
  final String siteSystemMaintenance;
  final String siteActEntranceSwitch;
  final String siteActEntranceUrl;
  final String siteStat;
  final String? siteAuthor;
  final String siteRecord;
  final String siteRecordCode;
  final String siteMasterScale;
  final String sitePayGroupClose;
  final int siteOpenSort;
  final String openViewCount;
  final int siteCharge;
  final int siteTitles;
  final List<PlatformSetting> siteRewards;
  final List<PlatformSetting> siteAreward;
  final List<PlatformSetting> siteRedpacket;
  final List<PlatformSetting> siteAnonymous;
  final List<PlatformSetting> sitePersonalletter;
  final List<PlatformSetting> siteShop;
  final List<PlatformSetting> sitePay;
  final List<PlatformSetting> siteUsergroup;
  final List<PlatformSetting> siteRecharges;
  final List<PlatformSetting> siteWithdrawal;
  final List<PlatformSetting> siteComment;

  SetSite({
    this.siteManage,
    this.apiFreq,
    required this.siteUrl,
    required this.siteInstall,
    required this.siteCover,
    required this.siteMinimumAmount,
    required this.loginaes,
    required this.aesKey,
    required this.aesIv,
    required this.usernameLoginIsdisplay,
    required this.openApiLog,
    required this.threadTab,
    required this.version,
    required this.siteName,
    required this.siteTitle,
    required this.siteFabu,
    required this.siteKeywords,
    required this.siteIntroduction,
    required this.siteMode,
    required this.openExtFields,
    required this.siteClose,
    required this.siteCloseMsg,
    required this.siteFavicon,
    required this.siteLogo,
    required this.siteHeaderLogo,
    required this.siteBackgroundImage,
    required this.siteUserBackgroundImage,
    required this.siteShareSwitch,
    required this.siteSearchSwitch,
    required this.siteSignInSwitch,
    required this.siteSpaceCollectionPageSwitch,
    required this.siteAuthSwitch,
    required this.siteLoginType,
    required this.sitePcMixThreadSwitch,
    required this.siteWechatLoginPid,
    required this.siteWechatLoginGid,
    required this.siteSignInUrl,
    required this.siteSystemMaintenance,
    required this.siteActEntranceSwitch,
    required this.siteActEntranceUrl,
    required this.siteStat,
    this.siteAuthor,
    required this.siteRecord,
    required this.siteRecordCode,
    required this.siteMasterScale,
    required this.sitePayGroupClose,
    required this.siteOpenSort,
    required this.openViewCount,
    required this.siteCharge,
    required this.siteTitles,
    required this.siteRewards,
    required this.siteAreward,
    required this.siteRedpacket,
    required this.siteAnonymous,
    required this.sitePersonalletter,
    required this.siteShop,
    required this.sitePay,
    required this.siteUsergroup,
    required this.siteRecharges,
    required this.siteWithdrawal,
    required this.siteComment,
  });

  factory SetSite.fromJson(Map<String, dynamic> json) {
    return SetSite(
      siteManage: json['siteManage'],
      apiFreq: json['apiFreq'],
      siteUrl: json['siteUrl'] ?? '',
      siteInstall: json['siteInstall'] ?? '',
      siteCover: json['siteCover'] ?? '',
      siteMinimumAmount: json['siteMinimumAmount'] ?? '',
      loginaes: List<bool>.from(json['loginaes'] ?? []),
      aesKey: List<String>.from(json['aesKey'] ?? []),
      aesIv: List<String>.from(json['aesIv'] ?? []),
      usernameLoginIsdisplay: json['usernameLoginIsdisplay'] ?? false,
      openApiLog: json['openApiLog']?.toString() ?? '0',
      threadTab: json['threadTab'] is String ? int.tryParse(json['threadTab']) ?? 0 : json['threadTab'] ?? 0,
      version: json['version'] ?? '',
      siteName: json['siteName'] ?? '',
      siteTitle: json['siteTitle'] ?? '',
      siteFabu: json['siteFabu'] is String ? int.tryParse(json['siteFabu']) ?? 0 : json['siteFabu'] ?? 0,
      siteKeywords: json['siteKeywords'] ?? '',
      siteIntroduction: json['siteIntroduction'] ?? '',
      siteMode: json['siteMode'] ?? '',
      openExtFields: json['openExtFields'] ?? '',
      siteClose: json['siteClose'] ?? false,
      siteCloseMsg: json['siteCloseMsg'] ?? '',
      siteFavicon: json['siteFavicon'] ?? '',
      siteLogo: json['siteLogo'] ?? '',
      siteHeaderLogo: json['siteHeaderLogo'] ?? '',
      siteBackgroundImage: json['siteBackgroundImage'] ?? '',
      siteUserBackgroundImage: json['siteUserBackgroundImage'] ?? '',
      siteShareSwitch: json['siteShareSwitch'] is String ? int.tryParse(json['siteShareSwitch']) ?? 0 : json['siteShareSwitch'] ?? 0,
      siteSearchSwitch: json['siteSearchSwitch']?.toString() ?? '0',
      siteSignInSwitch: json['siteSignInSwitch']?.toString() ?? '0',
      siteSpaceCollectionPageSwitch: json['siteSpaceCollectionPageSwitch'] is String ? int.tryParse(json['siteSpaceCollectionPageSwitch']) ?? 0 : json['siteSpaceCollectionPageSwitch'] ?? 0,
      siteAuthSwitch: json['siteAuthSwitch']?.toString() ?? '0',
      siteLoginType: json['siteLoginType']?.toString() ?? '0',
      sitePcMixThreadSwitch: json['sitePcMixThreadSwitch']?.toString() ?? '0',
      siteWechatLoginPid: json['siteWechatLoginPid']?.toString() ?? '',
      siteWechatLoginGid: json['siteWechatLoginGid']?.toString() ?? '',
      siteSignInUrl: json['siteSignInUrl'] ?? '',
      siteSystemMaintenance: json['siteSystemMaintenance']?.toString() ?? '0',
      siteActEntranceSwitch: json['siteActEntranceSwitch']?.toString() ?? '0',
      siteActEntranceUrl: json['siteActEntranceUrl'] ?? '',
      siteStat: json['siteStat'] ?? '',
      siteAuthor: json['siteAuthor'],
      siteRecord: json['siteRecord'] ?? '',
      siteRecordCode: json['siteRecordCode'] ?? '',
      siteMasterScale: json['siteMasterScale'] ?? '',
      sitePayGroupClose: json['sitePayGroupClose'] ?? '',
      siteOpenSort: json['siteOpenSort'] is String ? int.tryParse(json['siteOpenSort']) ?? 0 : json['siteOpenSort'] ?? 0,
      openViewCount: json['openViewCount']?.toString() ?? '0',
      siteCharge: json['siteCharge'] is String ? int.tryParse(json['siteCharge']) ?? 0 : json['siteCharge'] ?? 0,
      siteTitles: json['siteTitles'] is String ? int.tryParse(json['siteTitles']) ?? 0 : json['siteTitles'] ?? 0,
      siteRewards: (json['siteRewards'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(item))
          .toList(),
      siteAreward: (json['siteAreward'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(item))
          .toList(),
      siteRedpacket: (json['siteRedpacket'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(item))
          .toList(),
      siteAnonymous: (json['siteAnonymous'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(item))
          .toList(),
      sitePersonalletter: (json['sitePersonalletter'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(item))
          .toList(),
      siteShop: (json['siteShop'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(item))
          .toList(),
      sitePay: (json['sitePay'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(item))
          .toList(),
      siteUsergroup: (json['siteUsergroup'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(item))
          .toList(),
      siteRecharges: (json['siteRecharges'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(item))
          .toList(),
      siteWithdrawal: (json['siteWithdrawal'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(item))
          .toList(),
      siteComment: (json['siteComment'] as List<dynamic>? ?? [])
          .map((item) => PlatformSetting.fromJson(item))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'siteManage': siteManage,
      'apiFreq': apiFreq,
      'siteUrl': siteUrl,
      'siteInstall': siteInstall,
      'siteCover': siteCover,
      'siteMinimumAmount': siteMinimumAmount,
      'loginaes': loginaes,
      'aesKey': aesKey,
      'aesIv': aesIv,
      'usernameLoginIsdisplay': usernameLoginIsdisplay,
      'openApiLog': openApiLog,
      'threadTab': threadTab,
      'version': version,
      'siteName': siteName,
      'siteTitle': siteTitle,
      'siteFabu': siteFabu,
      'siteKeywords': siteKeywords,
      'siteIntroduction': siteIntroduction,
      'siteMode': siteMode,
      'openExtFields': openExtFields,
      'siteClose': siteClose,
      'siteCloseMsg': siteCloseMsg,
      'siteFavicon': siteFavicon,
      'siteLogo': siteLogo,
      'siteHeaderLogo': siteHeaderLogo,
      'siteBackgroundImage': siteBackgroundImage,
      'siteUserBackgroundImage': siteUserBackgroundImage,
      'siteShareSwitch': siteShareSwitch,
      'siteSearchSwitch': siteSearchSwitch,
      'siteSignInSwitch': siteSignInSwitch,
      'siteSpaceCollectionPageSwitch': siteSpaceCollectionPageSwitch,
      'siteAuthSwitch': siteAuthSwitch,
      'siteLoginType': siteLoginType,
      'sitePcMixThreadSwitch': sitePcMixThreadSwitch,
      'siteWechatLoginPid': siteWechatLoginPid,
      'siteWechatLoginGid': siteWechatLoginGid,
      'siteSignInUrl': siteSignInUrl,
      'siteSystemMaintenance': siteSystemMaintenance,
      'siteActEntranceSwitch': siteActEntranceSwitch,
      'siteActEntranceUrl': siteActEntranceUrl,
      'siteStat': siteStat,
      'siteAuthor': siteAuthor,
      'siteRecord': siteRecord,
      'siteRecordCode': siteRecordCode,
      'siteMasterScale': siteMasterScale,
      'sitePayGroupClose': sitePayGroupClose,
      'siteOpenSort': siteOpenSort,
      'openViewCount': openViewCount,
      'siteCharge': siteCharge,
      'siteTitles': siteTitles,
      'siteRewards': siteRewards.map((item) => item.toJson()).toList(),
      'siteAreward': siteAreward.map((item) => item.toJson()).toList(),
      'siteRedpacket': siteRedpacket.map((item) => item.toJson()).toList(),
      'siteAnonymous': siteAnonymous.map((item) => item.toJson()).toList(),
      'sitePersonalletter': sitePersonalletter.map((item) => item.toJson()).toList(),
      'siteShop': siteShop.map((item) => item.toJson()).toList(),
      'sitePay': sitePay.map((item) => item.toJson()).toList(),
      'siteUsergroup': siteUsergroup.map((item) => item.toJson()).toList(),
      'siteRecharges': siteRecharges.map((item) => item.toJson()).toList(),
      'siteWithdrawal': siteWithdrawal.map((item) => item.toJson()).toList(),
      'siteComment': siteComment.map((item) => item.toJson()).toList(),
    };
  }
}