import 'set_site.dart';
import 'set_reg.dart';
import 'passport.dart';
import 'pay_center.dart';
import 'set_attach.dart';
import 'qcloud.dart';
import 'set_cash.dart';
import 'other.dart';
import 'lbs.dart';
import 'ucenter.dart';
import 'set_chatgpt.dart';
import 'forum_user.dart';
import 'agreement.dart';
import 'tgid_info.dart';
import 'emoji.dart';
import 'act_module.dart';

/// 论坛配置数据模型
class ForumConfig {
  final SetSite setSite;
  final SetReg setReg;
  final Passport passport;
  final PayCenter payCenter;
  final SetAttach setAttach;
  final QCloud qcloud;
  final SetCash setCash;
  final Other other;
  final Lbs lbs;
  final UCenter ucenter;
  final SetChatgpt setChatgpt;
  final User user;
  final Agreement agreement;
  final List<TgidInfo> tgidInfo;
  final String? publishTime;
  final List<Emoji> emojis;
  final List<ActModule> actModule;
  final List<dynamic> popups;

  ForumConfig({
    required this.setSite,
    required this.setReg,
    required this.passport,
    required this.payCenter,
    required this.setAttach,
    required this.qcloud,
    required this.setCash,
    required this.other,
    required this.lbs,
    required this.ucenter,
    required this.setChatgpt,
    required this.user,
    required this.agreement,
    required this.tgidInfo,
    this.publishTime,
    required this.emojis,
    required this.actModule,
    required this.popups,
  });

  factory ForumConfig.fromJson(Map<String, dynamic> json) {
    return ForumConfig(
      setSite: SetSite.fromJson(json['setSite'] ?? {}),
      setReg: SetReg.fromJson(json['setReg'] ?? {}),
      passport: Passport.fromJson(json['passport'] ?? {}),
      payCenter: PayCenter.fromJson(json['paycenter'] ?? {}),
      setAttach: SetAttach.fromJson(json['setAttach'] ?? {}),
      qcloud: QCloud.fromJson(json['qcloud'] ?? {}),
      setCash: SetCash.fromJson(json['setCash'] ?? {}),
      other: Other.fromJson(json['other'] ?? {}),
      lbs: Lbs.fromJson(json['lbs'] ?? {}),
      ucenter: UCenter.fromJson(json['ucenter'] ?? {}),
      setChatgpt: SetChatgpt.fromJson(json['setChatgpt'] ?? {}),
      user: User.fromJson(json['user'] ?? {}),
      agreement: Agreement.fromJson(json['agreement'] ?? {}),
      tgidInfo: (json['tgidInfo'] as List<dynamic>? ?? [])
          .map((item) => TgidInfo.fromJson(item))
          .toList(),
      publishTime: json['publishTime'],
      emojis: (json['emojis'] as List<dynamic>? ?? [])
          .map((item) => Emoji.fromJson(item))
          .toList(),
      actModule: (json['actModule'] as List<dynamic>? ?? [])
          .map((item) => ActModule.fromJson(item))
          .toList(),
      popups: json['popups'] ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'setSite': setSite.toJson(),
      'setReg': setReg.toJson(),
      'passport': passport.toJson(),
      'paycenter': payCenter.toJson(),
      'setAttach': setAttach.toJson(),
      'qcloud': qcloud.toJson(),
      'setCash': setCash.toJson(),
      'other': other.toJson(),
      'lbs': lbs.toJson(),
      'ucenter': ucenter.toJson(),
      'setChatgpt': setChatgpt.toJson(),
      'user': user.toJson(),
      'agreement': agreement.toJson(),
      'tgidInfo': tgidInfo.map((item) => item.toJson()).toList(),
      'publishTime': publishTime,
      'emojis': emojis.map((item) => item.toJson()).toList(),
      'actModule': actModule.map((item) => item.toJson()).toList(),
      'popups': popups,
    };
  }
}