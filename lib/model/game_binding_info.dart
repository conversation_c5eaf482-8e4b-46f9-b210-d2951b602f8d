import 'package:json_annotation/json_annotation.dart';
part 'game_binding_info.g.dart';

@JsonSerializable()
class GameBindingInfo {
  @J<PERSON><PERSON><PERSON>(name: "app_ticket")
  final String appTicket;
  @<PERSON><PERSON><PERSON><PERSON>(name: "appid")
  final String appId;
  @<PERSON><PERSON><PERSON><PERSON>(name: "app_pid")
  final String appPid;
  @<PERSON>son<PERSON>ey(name: "app_gid")
  final String appGid;

  GameBindingInfo({
    required this.appTicket,
    required this.appId,
    required this.appPid,
    required this.appGid,
  });

  factory GameBindingInfo.fromJson(Map<String, dynamic> json) => _$GameBindingInfoFromJson(json);

  Map<String, dynamic> toJson() => _$GameBindingInfoToJson(this);
}
