/// 注册设置
class SetReg {
  final int registerType;
  final bool registerClose;
  final bool registerValidate;
  final bool registerCaptcha;
  final int passwordLength;
  final List<dynamic> passwordStrength;
  final bool isNeedTransition;

  SetReg({
    required this.registerType,
    required this.registerClose,
    required this.registerValidate,
    required this.registerCaptcha,
    required this.passwordLength,
    required this.passwordStrength,
    required this.isNeedTransition,
  });

  factory SetReg.fromJson(Map<String, dynamic> json) {
    return SetReg(
      registerType: json['registerType'] ?? 0,
      registerClose: json['registerClose'] ?? false,
      registerValidate: json['registerValidate'] ?? false,
      registerCaptcha: json['registerCaptcha'] ?? false,
      passwordLength: json['passwordLength'] ?? 0,
      passwordStrength: json['passwordStrength'] ?? [],
      isNeedTransition: json['isNeedTransition'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'registerType': registerType,
      'registerClose': registerClose,
      'registerValidate': registerValidate,
      'registerCaptcha': registerCaptcha,
      'passwordLength': passwordLength,
      'passwordStrength': passwordStrength,
      'isNeedTransition': isNeedTransition,
    };
  }
}