import 'package:dlyz_flutter/model/game_post_detail.dart';

/// 游戏中心数据模型
class GamePostList {
  final int offset;
  final List<GamePostDetail> record;

  GamePostList({
    required this.offset,
    required this.record,
  });

  factory GamePostList.fromJson(Map<String, dynamic> json) {
    return GamePostList(
      offset: json['offset'] ?? 0,
      record: (json['record'] as List<dynamic>? ?? [])
          .map((item) => GamePostDetail.fromJson(item))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'offset': offset,
      'record': record.map((post) => post.toJson()).toList(),
    };
  }
}