/// 游戏中心详情数据模型
class GamePostDetail {
  final String tGid;
  final String tGidName;
  final String tGidDesc;
  final String detailUrl;
  final GameFloatConfig floatConfig;

  GamePostDetail({
    required this.tGid,
    required this.tGidName,
    required this.tGidDesc,
    required this.detailUrl,
    required this.floatConfig
  });

  factory GamePostDetail.fromJson(Map<String, dynamic> json) {
    return GamePostDetail(
      tGid: json['tgid'] ?? '',
      tGidName: json['tgid_name'] ?? '',
      tGidDesc: json['tgid_desc'] ?? '',
      detailUrl: json['detail_url'] ?? '',
      floatConfig: GameFloatConfig.fromJson(json['float_config'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'tGid': tGid,
      'tGidName': tGidName,
      'tGidDesc': tGidDesc,
      'detailUrl': detailUrl,
      'floatConfig': floatConfig,
    };
  }
}


class GameFloatConfig {
  final String floatMsg;

  GameFloatConfig({
    required this.floatMsg
  });

  factory GameFloatConfig.fromJson(Map<String, dynamic> json) {
    return GameFloatConfig(
        floatMsg: json['float_msg'] ?? ''
    );
  }

}