/// 护照设置
class Passport {
  final bool offiaccountOpen;
  final bool miniprogramOpen;

  Passport({
    required this.offiaccountOpen,
    required this.miniprogramOpen,
  });

  factory Passport.fromJson(Map<String, dynamic> json) {
    return Passport(
      offiaccountOpen: json['offiaccountOpen'] ?? false,
      miniprogramOpen: json['miniprogramOpen'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'offiaccountOpen': offiaccountOpen,
      'miniprogramOpen': miniprogramOpen,
    };
  }
}