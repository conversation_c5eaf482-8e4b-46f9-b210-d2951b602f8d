/// 登录API返回的数据对象
class LoginInfo {
  /// 用户名
  final String? muname;
  
  /// 用户ID
  final String? muid;
  
  /// 登录类型
  final String loginType;
  
  /// 票据
  final String ticket;
  
  /// 刷新票据
  final String refreshTicket;
  
  /// 操作类型
  final String actionType;

  const LoginInfo({
    this.muname,
    this.muid,
    this.loginType = '',
    this.ticket = '',
    this.refreshTicket = '',
    this.actionType = '',
  });

  factory LoginInfo.fromJson(Map<String, dynamic> json) {
    return LoginInfo(
      muname: json['muname'] as String?,
      muid: json['muid']?.toString(),
      loginType: json['login_type'] as String? ?? '',
      ticket: json['app_ticket'] as String? ?? '',
      refreshTicket: json['app_refresh_ticket'] as String? ?? '',
      actionType: json['action_type'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'muname': muname,
      'muid': muid,
      'login_type': loginType,
      'app_ticket': ticket,
      'app_refresh_ticket': refreshTicket,
      'action_type': actionType,
    };
  }
}