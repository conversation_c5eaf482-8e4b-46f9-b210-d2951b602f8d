import 'dart:async';
import 'package:flutter/cupertino.dart';

class DownloadBean {
  final String title;
  final String subtitle;
  final String imageUrl;
  final String packageName;
  final String url;
  final String? tag;
  final String? promotionText;

  // 添加进度通知器
  final ValueNotifier<String> progressNotifier;
  final ValueNotifier<String> downloadSpeedNotifier;
  final ValueNotifier<String> downloadStatusNotifier;
  double? cacheProgress;
  Timer? progressTimer;

  DownloadBean({
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    required this.packageName,
    required this.url,
    this.tag,
    this.promotionText,
    String progress = "0",
    String downloadStatus = "下载",
    String downloadSpeed = "0",
  }) : progressNotifier = ValueNotifier(progress),
        downloadStatusNotifier = ValueNotifier(downloadStatus),
        downloadSpeedNotifier = ValueNotifier(downloadSpeed);
}