import 'dart:convert';

import 'package:dlyz_flutter/model/connectivity_result.dart';
import 'package:dlyz_flutter/model/game_binding_info.dart';
import 'package:dlyz_flutter/utils/device_info_util.dart';
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../model/mini_program_info.dart';

/// 与原生通信的管理类
class ChannelManager {
  /// 单例实例
  static final ChannelManager _instance = ChannelManager._internal();

  /// 通信通道名称，必须与原生端保持一致
  static const String _channelName = 'channel.control/dlyz';
  static const String _eventName = 'channel.control/dlyz_status';

  /// 方法通道实例
  final MethodChannel _methodChannel = const MethodChannel(_channelName);
  final EventChannel _eventChannel = const EventChannel(_eventName);

  /// 闪验UI事件回调
  Function(String event)? _fastLoginUIEventCallback;

  /// 游戏绑定成功回调
  Function()? _bindingSuccessCallback;

  /// 网络状态监听
  Stream<List<ConnectivityResult>>? _onConnectivityChanged;

  /// 工厂构造函数，返回单例
  factory ChannelManager() {
    return _instance;
  }

  /// 私有构造函数
  ChannelManager._internal() {
    // 设置方法调用处理器，监听来自原生端的事件
    _methodChannel.setMethodCallHandler(_handleMethodCall);
  }

  /// 监听网络状态变化
  Stream<List<ConnectivityResult>> get onConnectivityChanged {
    _onConnectivityChanged ??= _eventChannel
        .receiveBroadcastStream()
        .map((dynamic result) => List<String>.from(result))
        .map(DeviceInfoUtil.parseConnectivityResults);
    return _onConnectivityChanged!;
  }

  Future<List<ConnectivityResult>> checkNetworkConnectivity() {
    return _methodChannel
        .invokeListMethod<String>('checkNetworkType')
        .then((value) => DeviceInfoUtil.parseConnectivityResults(value ?? []));
  }

  /// 获取平台版本
  Future<String?> getPlatformVersion() async {
    try {
      final String? version = await _methodChannel.invokeMethod('getPlatformVersion');
      return version;
    } on PlatformException catch (e) {
      print('获取平台版本失败: ${e.message}');
      return null;
    }
  }

  /// 检查游戏是否已安装
  /// [packageName] 游戏包名
  Future<bool> checkGameInstalled({required String packageName}) async {
    try {
      final bool isInstalled = await _methodChannel.invokeMethod(
        'checkGameInstalled',
        {'packageName': packageName},
      );
      return isInstalled;
    } on PlatformException catch (e) {
      print('检查游戏安装状态失败: ${e.code} - ${e.message}');
      return false;
    }
  }

  /// 打开已安装的游戏
  /// [packageName] 游戏包名
  Future<bool?> openInstalledGame({required String packageName}) async {
    try {
      final bool? result = await _methodChannel.invokeMethod(
        'openInstalledGame',
        {'packageName': packageName},
      );
      return result;
    } on PlatformException catch (e) {
      print('打开游戏失败: ${e.code} - ${e.message}');
      return null;
    }
  }

  /// 安装APK文件
  /// [filePath] APK文件路径
  Future<void> installApk(String filePath) async {
    try {
      await _methodChannel.invokeMethod('installApk', {'filePath': filePath});
    } on PlatformException catch (e) {
      print('Failed to install APK: ${e.message}');
      throw Exception('安装失败: ${e.message}');
    }
  }

  /// 打开指定URL
  Future<bool?> openUrl({required String url}) async {
    try {
      final bool? result = await _methodChannel.invokeMethod(
        'openUrl',
        {'url': url},
      );
      return result;
    } on PlatformException catch (e) {
      print('打开URL失败: ${e.code} - ${e.message}');
      return false;
    }
  }

  /// 打开小程序
  Future<void> jumpToMiniProgram({required MiniProgramInfo info}) async {
    try {
      await _methodChannel.invokeMethod(
          'jumpToMiniProgram',
          {
            'skip_type': info.skipType,
            'mini_program_id': info.miniProgramId,
            'mini_program_path': info.miniProgramPath,
            'scheme_url': info.schemeUrl
          }
      );
    } on PlatformException catch (e) {
      print('Failed to install APK: ${e.message}');
      throw Exception('跳转失败: ${e.message}');
    }
  }

  /// 打开已安装的游戏，走绑定流程
  /// [packageName] 游戏包名
  /// [gameBindingInfo] api接口参数
  /// [gameExtParams] sdk需要的扩展参数
  Future<bool?> bindingGame({
    required String packageName,
    required GameBindingInfo gameBindingInfo,
    required Map<String, dynamic> gameExtParams
  }) async {
    try {
      PackageInfo info = await PackageInfo.fromPlatform();
      Map<String, dynamic> apiParams = gameBindingInfo.toJson();
      Map<String, dynamic> extParams = {...gameExtParams, 'callingPackageName': info.packageName};
      Map<String, dynamic> bindingParams = {
        'apiParams': apiParams,
        'extParams': extParams
      };
      String paramsJson = jsonEncode(bindingParams);
      final bool? result = await _methodChannel.invokeMethod(
        'bindingGame',
        {
          'packageName': packageName,
          'bindingParams': paramsJson
        },
      );
      return result;
    } on PlatformException catch (e) {
      print('打开游戏失败: ${e.code} - ${e.message}');
      return null;
    }
  }

  /// 设置闪验配置
  Future<bool> setFastLoginConfig(Map<String, dynamic> config, {
    String? userAgreementUrl,
    String? privacyPolicyUrl,
  }) async {
    try {
      // 将协议地址添加到配置中
      final Map<String, dynamic> fullConfig = Map.from(config);
      if (userAgreementUrl != null) {
        fullConfig['userAgreementUrl'] = userAgreementUrl;
      }
      if (privacyPolicyUrl != null) {
        fullConfig['privacyPolicyUrl'] = privacyPolicyUrl;
      }

      final bool? success = await _methodChannel.invokeMethod(
        'setFastLoginConfig',
        fullConfig,
      );
      return success ?? false;
    } on PlatformException catch (e) {
      print('设置闪验配置失败: ${e.code} - ${e.message}');
      return false;
    }
  }

  /// 检查闪验环境是否支持
  Future<bool> checkFastLoginEnvironment() async {
    try {
      final bool? supported = await _methodChannel.invokeMethod('checkFastLoginEnvironment');
      return supported ?? false;
    } on PlatformException catch (e) {
      print('检查闪验环境失败: ${e.code} - ${e.message}');
      return false;
    }
  }

  /// 初始化闪验SDK
  Future<bool> initializeFastLogin() async {
    try {
      final bool? success = await _methodChannel.invokeMethod('initializeFastLogin');
      return success ?? false;
    } on PlatformException catch (e) {
      print('初始化闪验失败: ${e.code} - ${e.message}');
      throw FastLoginException(e.code, e.message ?? 'Unknown error');
    }
  }

  /// 执行闪验登录
  Future<Map<String, dynamic>> doFastLogin() async {
    try {
      final result = await _methodChannel.invokeMethod('doFastLogin');
      return Map<String, dynamic>.from(result);
    } on PlatformException catch (e) {
      if (e.code == 'CANCEL') {
        throw FastLoginCancelException();
      }
      throw FastLoginException(e.code, e.message ?? 'Unknown error');
    }
  }

  /// 设置闪验UI事件监听器
  void setFastLoginUIEventListener(Function(String event)? callback) {
    _fastLoginUIEventCallback = callback;
  }

  /// 设置游戏绑定成功回调
  void setBindingSuccessCallback(Function()? callback) {
    _bindingSuccessCallback = callback;
  }

  /// 处理来自原生端的方法调用
  Future<void> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onFastLoginUIEvent':
        _handleFastLoginUIEvent(call.arguments);
        break;
      case 'onBindingSuccess':
        _handlerBindingSuccess(call.arguments);
        break;
      default:
        print('未处理的方法调用: ${call.method}');
    }
  }

  /// 处理闪验UI事件
  void _handleFastLoginUIEvent(dynamic arguments) {
    if (arguments is Map) {
      final String? event = arguments['event'];
      switch (event) {
        case 'close_clicked':
          print('Flutter: 用户点击了关闭按钮 - 弹窗已关闭');
          _fastLoginUIEventCallback?.call('close_clicked');
          break;
        case 'back_clicked':
          print('Flutter: 用户点击了返回按钮 - 弹窗已关闭');
          _fastLoginUIEventCallback?.call('back_clicked');
          break;
        case 'wechat_clicked':
          print('Flutter: 用户选择了微信登录');
          _fastLoginUIEventCallback?.call('wechat_clicked');
          break;
        case 'account_login_clicked':
          print('Flutter: 用户选择了账号密码登录');
          _fastLoginUIEventCallback?.call('account_login_clicked');
          break;
        case 'other_phone_clicked':
          print('Flutter: 用户选择了其他手机号登录');
          _fastLoginUIEventCallback?.call('other_phone_clicked');
          break;
        case 'forget_password_clicked':
          print('Flutter: 用户点击了忘记密码');
          break;
        default:
          print('Flutter: 未知的闪验UI事件: $event');
          if (event is String) {
            _fastLoginUIEventCallback?.call(event);
          }
      }
    }
  }

  ///处理sdk绑定成功事件
  void _handlerBindingSuccess(dynamic arguments) {
    _bindingSuccessCallback?.call();
  }
}

/// 闪验异常类
class FastLoginException implements Exception {
  final String code;
  final String message;

  FastLoginException(this.code, this.message);

  @override
  String toString() => 'FastLoginException($code): $message';
}

/// 闪验取消异常
class FastLoginCancelException extends FastLoginException {
  FastLoginCancelException() : super('CANCEL', '用户取消登录');
}