import 'package:flutter/services.dart';

/// 与原生通信的管理类
class ChannelManager {
  /// 单例实例
  static final ChannelManager _instance = ChannelManager._internal();

  /// 通信通道名称，必须与原生端保持一致
  static const String _channelName = 'channel.control/dlyz';

  /// 方法通道实例
  final MethodChannel _methodChannel = const MethodChannel(_channelName);

  /// 工厂构造函数，返回单例
  factory ChannelManager() {
    return _instance;
  }

  /// 私有构造函数
  ChannelManager._internal();

  /// 获取平台版本
  Future<String?> getPlatformVersion() async {
    try {
      final String? version = await _methodChannel.invokeMethod('getPlatformVersion');
      return version;
    } on PlatformException catch (e) {
      print('获取平台版本失败: \${e.message}');
      return null;
    }
  }

  /// 检查游戏是否已安装
  /// [packageName] 游戏包名
  Future<bool?> checkGameInstalled({required String packageName}) async {
    try {
      final bool? isInstalled = await _methodChannel.invokeMethod(
        'checkGameInstalled',
        {'packageName': packageName},
      );
      return isInstalled;
    } on PlatformException catch (e) {
      print('检查游戏安装状态失败: \${e.code} - \${e.message}');
      return null;
    }
  }

  /// 打开已安装的游戏
  /// [packageName] 游戏包名
  Future<bool?> openInstalledGame({required String packageName}) async {
    try {
      final bool? result = await _methodChannel.invokeMethod(
        'openInstalledGame',
        {'packageName': packageName},
      );
      return result;
    } on PlatformException catch (e) {
      print('打开游戏失败: \${e.code} - \${e.message}');
      return null;
    }
  }

  /// 安装APK文件
  /// [filePath] APK文件路径
  Future<void> installApk(String filePath) async {
    try {
      await _methodChannel.invokeMethod('installApk', {'filePath': filePath});
    } on PlatformException catch (e) {
      print('Failed to install APK: \${e.message}');
      throw Exception('安装失败: \${e.message}');
    }
  }

  /// 打开已安装的游戏，走绑定流程
  /// [packageName] 游戏包名
  Future<bool?> bindingGame({required String packageName, String? params}) async {
    try {
      final bool? result = await _methodChannel.invokeMethod(
        'bindingGame',
        {
          'packageName': packageName,
          'params': params,
        },
      );
      return result;
    } on PlatformException catch (e) {
      print('打开游戏失败: \${e.code} - \${e.message}');
      return null;
    }
  }
}