import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// 与原生通信的管理类
class ChannelManager {
  /// 单例实例
  static final ChannelManager _instance = ChannelManager._internal();

  /// 通信通道名称，必须与原生端保持一致
  static const String _channelName = 'channel.control/dlyz';

  /// 方法通道实例
  final MethodChannel _methodChannel = const MethodChannel(_channelName);

  /// 工厂构造函数，返回单例
  factory ChannelManager() {
    return _instance;
  }

  /// 私有构造函数
  ChannelManager._internal() {
    // 设置方法调用处理器，监听来自原生端的事件
    _methodChannel.setMethodCallHandler(_handleMethodCall);
  }

  /// 获取平台版本
  Future<String?> getPlatformVersion() async {
    try {
      final String? version = await _methodChannel.invokeMethod('getPlatformVersion');
      return version;
    } on PlatformException catch (e) {
      print('获取平台版本失败: \${e.message}');
      return null;
    }
  }

  /// 检查游戏是否已安装
  /// [packageName] 游戏包名
  Future<bool?> checkGameInstalled({required String packageName}) async {
    try {
      final bool? isInstalled = await _methodChannel.invokeMethod(
        'checkGameInstalled',
        {'packageName': packageName},
      );
      return isInstalled;
    } on PlatformException catch (e) {
      print('检查游戏安装状态失败: \${e.code} - \${e.message}');
      return null;
    }
  }

  /// 打开已安装的游戏
  /// [packageName] 游戏包名
  Future<bool?> openInstalledGame({required String packageName}) async {
    try {
      final bool? result = await _methodChannel.invokeMethod(
        'openInstalledGame',
        {'packageName': packageName},
      );
      return result;
    } on PlatformException catch (e) {
      print('打开游戏失败: \${e.code} - \${e.message}');
      return null;
    }
  }

  /// 安装APK文件
  /// [filePath] APK文件路径
  Future<void> installApk(String filePath) async {
    try {
      await _methodChannel.invokeMethod('installApk', {'filePath': filePath});
    } on PlatformException catch (e) {
      print('Failed to install APK: \${e.message}');
      throw Exception('安装失败: \${e.message}');
    }
  }

  /// 打开已安装的游戏，走绑定流程
  /// [packageName] 游戏包名
  Future<bool?> bindingGame({required String packageName, required Map<String, String> bindingParams}) async {
    try {
      PackageInfo info = await PackageInfo.fromPlatform();
      Map params = {...bindingParams, 'callingPackageName': info.packageName};
      String paramsJson = jsonEncode(params);
      final bool? result = await _methodChannel.invokeMethod(
        'bindingGame',
        {
          'packageName': packageName,
          'bindingParams': paramsJson
        },
      );
      return result;
    } on PlatformException catch (e) {
      print('打开游戏失败: \${e.code} - \${e.message}');
      return null;
    }
  }

  /// 设置闪验配置
  Future<bool> setFastLoginConfig(Map<String, dynamic> config, {
    String? userAgreementUrl,
    String? privacyPolicyUrl,
  }) async {
    try {
      // 将协议地址添加到配置中
      final Map<String, dynamic> fullConfig = Map.from(config);
      if (userAgreementUrl != null) {
        fullConfig['userAgreementUrl'] = userAgreementUrl;
      }
      if (privacyPolicyUrl != null) {
        fullConfig['privacyPolicyUrl'] = privacyPolicyUrl;
      }

      final bool? success = await _methodChannel.invokeMethod(
        'setFastLoginConfig',
        fullConfig,
      );
      return success ?? false;
    } on PlatformException catch (e) {
      print('设置闪验配置失败: ${e.code} - ${e.message}');
      return false;
    }
  }

  /// 检查闪验环境是否支持
  Future<bool> checkFastLoginEnvironment() async {
    try {
      final bool? supported = await _methodChannel.invokeMethod('checkFastLoginEnvironment');
      return supported ?? false;
    } on PlatformException catch (e) {
      print('检查闪验环境失败: \${e.code} - \${e.message}');
      return false;
    }
  }

  /// 初始化闪验SDK
  Future<bool> initializeFastLogin() async {
    try {
      final bool? success = await _methodChannel.invokeMethod('initializeFastLogin');
      return success ?? false;
    } on PlatformException catch (e) {
      print('初始化闪验失败: \${e.code} - \${e.message}');
      throw FastLoginException(e.code, e.message ?? 'Unknown error');
    }
  }

  /// 执行闪验登录
  Future<Map<String, dynamic>> doFastLogin() async {
    try {
      final result = await _methodChannel.invokeMethod('doFastLogin');
      return Map<String, dynamic>.from(result);
    } on PlatformException catch (e) {
      if (e.code == 'CANCEL') {
        throw FastLoginCancelException();
      }
      throw FastLoginException(e.code, e.message ?? 'Unknown error');
    }
  }

  /// 处理来自原生端的方法调用
  Future<void> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onFastLoginUIEvent':
        _handleFastLoginUIEvent(call.arguments);
        break;
      default:
        print('未处理的方法调用: ${call.method}');
    }
  }

  /// 处理闪验UI事件
  void _handleFastLoginUIEvent(dynamic arguments) {
    if (arguments is Map) {
      final String? event = arguments['event'];
      switch (event) {
        case 'close_clicked':
          print('Flutter: 用户点击了关闭按钮 - 弹窗已关闭');
          // 可以在此处添加额外的业务逻辑，如通知登录页面更新UI状态
          break;
        case 'back_clicked':
          print('Flutter: 用户点击了返回按钮 - 弹窗已关闭');
          // 可以在此处添加额外的业务逻辑，如通知登录页面更新UI状态
          break;
        case 'forget_password_clicked':
          print('Flutter: 用户点击了忘记密码');
          break;
        case 'switch_method_clicked':
          print('Flutter: 用户选择了其他登录方式');
          break;
        default:
          print('Flutter: 未知的闪验UI事件: $event');
      }
    }
  }
}

/// 闪验异常类
class FastLoginException implements Exception {
  final String code;
  final String message;

  FastLoginException(this.code, this.message);

  @override
  String toString() => 'FastLoginException(\$code): \$message';
}

/// 闪验取消异常
class FastLoginCancelException extends FastLoginException {
  FastLoginCancelException() : super('CANCEL', '用户取消登录');
}