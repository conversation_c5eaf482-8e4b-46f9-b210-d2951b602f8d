import 'package:flutter/material.dart';

class DLColor {
  // 主色调 - 应用的主要品牌颜色
  static const Color primary = Color(0xFF4571FB);
  static const Color primaryAccent = Color.fromRGBO(29, 111, 233, 0.1);

  // 中性色 - 背景和边框
  static const Color background = Color(0xFF4571FB);
  static const Color textBackground = Color(0xFFF6F7F9); // 文本背景色
  static const Color pressBackground = Color.fromRGBO(69, 113, 251, 0.8);
  static const Color disableBackground = Color.fromRGBO(69, 113, 251, 0.6);
  static const Color downloadBackground = Color(0xFFD8DADF);
  static const Color tagBackground = Color(0xFFFF5946);

  // 文本颜色
  static const Color textPrimary = Color(0xFF303133);
  static const Color textSecondary = Color(0xFF606266);
  static const Color textThird = Color(0xFF909399);
  static const Color textFourth = Color(0xFFA8ABB2);

  // 边框和分隔线
  static const Color border = Color(0xFFEEEEEE);
  static const Color divider = Color(0xFFEAECF1);
  static const Color strongDivider = Color(0xFFD8DADF);

  // 功能色 - 状态指示
  static const Color success = Color(0xFF46DB7A);
  static const Color warning = Color(0xFFF5894E);
  static const Color info = Color(0xFF4EBBF5);
  static const Color error = Color(0xFFF55F4E);

  // 透明度颜色
  static const Color transparent = Colors.transparent;
  static const Color black50 = Color(0x80000000);
  static const Color black20 = Color(0x33000000);
  static const Color white50 = Color(0x80FFFFFF);
}