import 'package:flutter/material.dart';
import '../webview/webview_dialog.dart';

/// 协议服务类，管理用户协议和隐私政策的显示
class AgreementService {
  static final AgreementService _instance = AgreementService._internal();
  factory AgreementService() => _instance;
  AgreementService._internal();

  /// 用户协议URL
  String _userProtocolUrl = 'https://37.com.cn/user-agreement/content/shell.html?gid=1000000&gwversion=4.6.4&pid=1&url=https%3A%2F%2F37.com.cn%2Fuser-agreement%2Fcontent%2F%3FAID%3D31030&isAgree=true';
  
  /// 隐私政策URL
  String _privacyPolicyUrl = 'https://37.com.cn/user-agreement/content/shell.html?gid=1000000&gwversion=4.6.4&pid=1&url=https%3A%2F%2F37.com.cn%2Fuser-agreement%2Fcontent%2F%3FAID%3D31036&isAgree=true';

  /// 设置用户协议URL
  void setUserProtocolUrl(String url) {
    _userProtocolUrl = url;
  }

  /// 设置隐私政策URL
  void setPrivacyPolicyUrl(String url) {
    _privacyPolicyUrl = url;
  }

  /// 显示用户协议
  void showUserProtocol(BuildContext context) {
    Navigator.of(context).push(
      PageRouteBuilder(
        opaque: false,
        barrierDismissible: true,
        barrierColor: Colors.transparent,
        pageBuilder: (context, animation, secondaryAnimation) {
          return WebViewDialog(
            url: _userProtocolUrl,
            title: '用户协议',
            showToolBar: false,
          );
        },
      ),
    );
  }



  /// 显示隐私政策
  void showPrivacyPolicy(BuildContext context) {
    Navigator.of(context).push(
      PageRouteBuilder(
        opaque: false,
        barrierDismissible: true,
        barrierColor: Colors.transparent,
        pageBuilder: (context, animation, secondaryAnimation) {
          return WebViewDialog(
            url: _privacyPolicyUrl,
            title: '隐私政策',
            showToolBar: false,
          );
        },
      ),
    );
  }


} 