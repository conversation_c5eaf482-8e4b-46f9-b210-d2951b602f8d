import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../model/user_info.dart';

/// 用户数据持久化服务
class UserStorageService {
  static const String _userListKey = 'user_list';
  static const String _selectedUserKey = 'selected_user';
  
  static final UserStorageService _instance = UserStorageService._internal();
  factory UserStorageService() => _instance;
  UserStorageService._internal();

  /// 保存用户列表
  Future<void> saveUserList(List<UserInfo> userList) async {
    final prefs = await SharedPreferences.getInstance();
    final userListJson = userList.map((user) => user.toJson()).toList();
    await prefs.setString(_userListKey, jsonEncode(userListJson));
  }

  /// 读取用户列表
  Future<List<UserInfo>> getUserList() async {
    final prefs = await SharedPreferences.getInstance();
    final userListString = prefs.getString(_userListKey);
    
    if (userListString == null || userListString.isEmpty) {
      return [];
    }
    
    try {
      final userListJson = jsonDecode(userListString) as List;
      return userListJson.map((json) => UserInfo.fromJson(json)).toList();
    } catch (e) {
      print('解析用户列表失败: $e');
      return [];
    }
  }

  /// 保存选中的用户
  Future<void> saveSelectedUser(UserInfo? user) async {
    final prefs = await SharedPreferences.getInstance();
    if (user == null) {
      await prefs.remove(_selectedUserKey);
    } else {
      await prefs.setString(_selectedUserKey, jsonEncode(user.toJson()));
    }
  }

  /// 读取选中的用户
  Future<UserInfo?> getSelectedUser() async {
    final prefs = await SharedPreferences.getInstance();
    final selectedUserString = prefs.getString(_selectedUserKey);
    
    if (selectedUserString == null || selectedUserString.isEmpty) {
      return null;
    }
    
    try {
      final userJson = jsonDecode(selectedUserString);
      return UserInfo.fromJson(userJson);
    } catch (e) {
      print('解析选中用户失败: $e');
      return null;
    }
  }

  /// 添加用户
  Future<void> addUser(UserInfo user) async {
    final userList = await getUserList();
    
    // 检查是否已存在相同用户ID的用户
    final existingIndex = userList.indexWhere((u) => u.userId == user.userId);
    if (existingIndex != -1) {
      // 更新现有用户
      userList[existingIndex] = user;
    } else {
      // 添加新用户
      userList.add(user);
    }
    
    await saveUserList(userList);
  }

  /// 删除用户
  Future<void> deleteUser(UserInfo user) async {
    final userList = await getUserList();
    userList.removeWhere((u) => u.userId == user.userId);
    await saveUserList(userList);
    
    // 如果删除的是选中的用户，清除选中状态
    final selectedUser = await getSelectedUser();
    if (selectedUser?.userId == user.userId) {
      await saveSelectedUser(null);
    }
  }

  /// 清除所有用户数据
  Future<void> clearAllUsers() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userListKey);
    await prefs.remove(_selectedUserKey);
  }

  /// 获取用户数量
  Future<int> getUserCount() async {
    final userList = await getUserList();
    return userList.length;
  }

  /// 检查是否存在用户
  Future<bool> hasUsers() async {
    final userList = await getUserList();
    return userList.isNotEmpty;
  }
} 