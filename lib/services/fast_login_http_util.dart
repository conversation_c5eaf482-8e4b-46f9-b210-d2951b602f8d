import 'dart:convert';
import 'package:dio/dio.dart';
import '../net/gateway_encrypt_interceptor.dart';
import '../net/sign_v3_interceptor.dart';
import '../config/app_config.dart';

/// 闪验配置HTTP工具类
class FastLoginHttpUtil {
  // static const String _baseUrl = 'http://s-api-secure.37.com.cn';
  static const String _baseUrl = 'http://s-api.37.com.cn';
  static const String _configPath = '/go/cfg/shan_yan';
  
  static Dio? _dio;
  
  /// 获取配置了加密网关的Dio实例
  static Dio _getDio() {
    if (_dio != null) return _dio!;
    
    // 创建37游戏专用网关提供者
    final provider = Game37GateWayProvider(
      key: 'soC2GAr8jN2fsbry', // 37游戏专用密钥
      version: '', // 空版本号
    );
    
    _dio = Dio(
      BaseOptions(
        baseUrl: _baseUrl,
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
        responseType: ResponseType.plain, // 使用plain类型避免JSON解析错误
        validateStatus: (status) {
          return status != null && status < 500;
        },
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      ),
    );
    // 添加SignV3签名拦截器
    _dio!.interceptors.add(
      SignV3Interceptor(appKey: AppConfig.appKey),
    );

    // 添加日志拦截器
    _dio!.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => print('FastLogin Dio: $obj'),
    ));

    // 添加网关加密拦截器
    // _dio!.interceptors.add(
    //   GateWayEncryptInterceptor(
    //     provider: provider,
    //     exceptionReporter: (title, code, exception, data) {
    //       print('网关加密异常: $title - $exception');
    //     },
    //   ),
    // );
    

    

    
    return _dio!;
  }
  
  /// 请求闪验配置
  static Future<Map<String, dynamic>> requestFastConfig() async {
    try {
      print('开始请求闪验配置');
      // 构建请求参数 - 使用真实参数
      final params = _buildCommonParams();
      params['package_name'] = 'com.sy.ljxszdb.sy';
      
      print('发送闪验配置请求到: $_baseUrl$_configPath');
      print('请求参数: $params');
      
      // 使用加密网关发送请求
      final response = await _getDio().post(
        _configPath,
        data: params,
      );
      
      print('闪验配置请求成功: 状态码 ${response.statusCode}');
      print('响应数据: ${response.data}');
      
      // 解析真实的服务器响应
      final responseData = response.data;
      if (responseData != null && responseData is String) {
        try {
          // 服务器可能返回加密的数据，需要根据实际情况解析
          // 如果响应是JSON格式
          final parsedData = json.decode(responseData);
          if (parsedData is Map<String, dynamic>) {
            // 检查响应结构：{"state":1,"msg":"成功","data":{"access_key":"..."}}
            final data = parsedData['data'] as Map<String, dynamic>?;
            final accessKey = data?['access_key'] as String?;
            print("access_key: ${accessKey ?? ''}");
            return {
              'access_key': accessKey ?? '',
              'app_key': AppConfig.appKey, // 从配置获取解密密钥
              'environment': data?['environment'] as String? ?? 'production',
            };
          }
        } catch (e) {
          print('解析服务器响应失败: $e');
        }
      }
      
      // 如果解析失败，使用测试配置
      print('使用测试闪验配置');
      return {
        'access_key': 'sbsHyF7I3UiVLU3Hf3diSzoDcjcgOM/5PSbAwIeJWrWgmIc/o8uctUDiHzj7UPmVq3aDb55k5qaQKpN7ub/NOX1qBk2FWUykVA9YywVuzycydyMZczITcibFe49I/1Vy5uxH9RO2suxgHyxAmgnaxFd+8kXTI2ZGjM1tU/6IO5yy77YXgG2ybLVtinfR2BLaJX4KSYH+5ly5fKT5zQTGKsImaEkXH5oxj/jwFk2vQkh2N7gDF4+1dPuNZ9dN99BHUu3fXMr0g6fhW1jcBaYw1C8NWJGexHHO0IeIhsgsctnQ2xDCjx0BF2g55LLW9rZeObtRnSE6MQRA1IVF66g+jTp/6L/U2OSDPgyNBkpBROjXWAa/V75tURyjkUaR2mp6SSrTjhS/W2X3Tu3i80ZYvd2ToXKDnnlqwVXcZDz+eNY=',
        'app_key': AppConfig.appKey, // 从配置获取解密密钥
        'environment': 'production',
      };
    } catch (e) {
      print('FastLoginHttpUtil.requestFastConfig error: $e');
      if (e is DioException) {
        print('错误类型: ${e.type}');
        print('错误响应: ${e.response?.data}');
        print('错误状态码: ${e.response?.statusCode}');
      }
      rethrow;
    }
  }
  
  /// 构建真实的通用参数
  static Map<String, dynamic> _buildCommonParams() {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return {
      'over': '11',
      'gwversion': AppConfig.gwversion,
      'host_sdk_version': AppConfig.sversion,
      'gid': AppConfig.gid,
      'os': 'android',
      'pid': AppConfig.pid,
      'is_root': '0',
      'version': AppConfig.sversion,
      'mac': '020000000000',
      'is_simulator': '0',
      'dev': 'ededd6da713b66c46cf0777aa198c759',
      'refer': 'sy_00001',
      'sversion': AppConfig.sversion,
      'imei': '99937734161624367',
      'time': now.toString(),
      'android_id': '100a059eb38a2d9d',
    };
  }
}