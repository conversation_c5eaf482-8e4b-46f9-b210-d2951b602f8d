import 'package:dlyz_flutter/config/app_config.dart';
import 'package:dlyz_flutter/utils/aes_utils.dart';

import '../net/http_service.dart';
import '../net/http_base_response.dart';
import '../model/login_info.dart';

/// 登录API服务类
class LoginApiService {
  static final LoginApiService _instance = LoginApiService._internal();
  factory LoginApiService() => _instance;
  LoginApiService._internal();

  final HttpService _httpService = HttpService();
  
  // API接口配置
  static const String _baseUrl = 'http://gamehub-api.37.com.cn';
  static const String _loginPath = '/api/gamehub-api/v1/login/account/pwd';
  static const String _fastLoginPath = '/api/gamehub-api/v1/login/flash/token';
  
  /// 账号密码登录
  /// 
  /// [username] 用户名
  /// [password] 密码
  /// 返回 [Future<BaseResponse<LoginInfo>>] 登录结果
  Future<BaseResponse<LoginInfo>> loginWithPassword({
    required String username,
    required String password,
  }) async {
    try {
      // 构建请求参数（只需要用户名和密码，其他参数由拦截器自动添加）
      final Map<String, dynamic> params = {
        'muname': username,
        'upwd': AesUtil.encrypt(password, AppConfig.appKey),
      };

      // 发起请求（默认使用 StateMsgData 解析策略）
      return await _httpService.post<LoginInfo>(
        _loginPath,
        baseUrl: _baseUrl,
        data: params,
        contentType: ContentType.form,
        fromJsonT: (data) => LoginInfo.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }

  /// 闪验token换票据登录
  Future<BaseResponse<LoginInfo>> loginWithFastToken({
    required String token,
  }) async {
    try {
      final Map<String, dynamic> params = {
        'fast_token': token,
      };

      return await _httpService.post<LoginInfo>(
        _fastLoginPath,
        baseUrl: _baseUrl,
        data: params,
        contentType: ContentType.form,
        fromJsonT: (data) => LoginInfo.fromJson(data),
      );
    } catch (e) {
      return BaseResponse.error('网络错误：$e');
    }
  }
}