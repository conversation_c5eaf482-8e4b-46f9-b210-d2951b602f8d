import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../main.dart';
import '../pages/login/unified_login_page.dart';
import '../providers/user_provider.dart';

/// 应用路由管理器
/// 负责基于登录状态的导航与登录相关的统一入口
class AppRouteManager {
  /// 冷启动：检查登录状态并跳转到相应页面
  static Future<void> navigateBasedOnLoginState(BuildContext context) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    
    if (!userProvider.isInitialized) {
      await userProvider.initialize();
    }
    
    if (context.mounted) {
      if (userProvider.isLoggedIn) {
        navigateToHome(context);
      } else {
        navigateToLogin(context);
      }
    }
  }

  /// 跳转到登录页
  static void navigateToLogin(BuildContext context) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const UnifiedLoginPage(),
      ),
    );
  }

  /// 跳转到主页
  static void navigateToHome(BuildContext context) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => const MyHomePage()),
    );
  }

  /// 登录成功后的跳转（清栈）
  static void navigateAfterLogin(BuildContext context) {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const MyHomePage()),
      (route) => false,
    );
  }

  /// 退出登录后的跳转（清栈）
  static Future<void> navigateAfterLogout(BuildContext context) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    await userProvider.logout();
    
    if (context.mounted) {
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => const UnifiedLoginPage(),
        ),
        (route) => false,
      );
    }
  }

  /// 显示登录弹层
  static Future<bool?> showLoginModal(
    BuildContext context, {
    VoidCallback? onLoginSuccess,
  }) async {
    return showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: UnifiedLoginPage(onLoginSuccess: onLoginSuccess),
      ),
    );
  }

  ///路由跳转 - 根据用户状态决定跳转逻辑
  static Future<void> checkLoginNavigate(BuildContext context) async {

    // navigateToHome(context);

    final userProvider = Provider.of<UserProvider>(context, listen: false);

    if (!userProvider.isInitialized) {
      await userProvider.initialize();
    }

    // if (userProvider.isLoggedIn && userProvider.needsTokenRefresh) {
    //   final refreshSuccess = await userProvider.refreshUserToken();
    //   if (!refreshSuccess && context.mounted) {
    //     await navigateAfterLogout(context);
    //     return;
    //   }
    // }

    if (context.mounted) {
      if (userProvider.isLoggedIn) {
        navigateToHome(context);
      } else {
        navigateToLogin(context);
      }
    }
  }

  /// 需要登录时的处理逻辑
  static Future<bool> requireLogin(
    BuildContext context, {
    String? message,
    VoidCallback? onLoginSuccess,
  }) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    
    if (userProvider.isLoggedIn) {
      return true;
    }
    
    final shouldLogin = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('需要登录'),
        content: Text(message ?? '此功能需要登录后使用，是否立即登录？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('登录'),
          ),
        ],
      ),
    );
    
    if (shouldLogin == true && context.mounted) {
      final loginResult = await showLoginModal(
        context,
        onLoginSuccess: onLoginSuccess,
      );
      return loginResult == true;
    }
    
    return false;
  }

  /// 安全的路由跳转 - 带错误处理
  static Future<void> safeNavigate(
    BuildContext context,
    Widget page, {
    bool replace = false,
    bool clearStack = false,
  }) async {
    try {
      final route = MaterialPageRoute(builder: (context) => page);
      
      if (clearStack) {
        Navigator.of(context).pushAndRemoveUntil(route, (route) => false);
      } else if (replace) {
        Navigator.of(context).pushReplacement(route);
      } else {
        Navigator.of(context).push(route);
      }
    } catch (e) {
      debugPrint('路由跳转失败: $e');
    }
  }

  /// 返回到指定页面
  static void popToPage(BuildContext context, String routeName) {
    Navigator.of(context).popUntil((route) {
      return route.settings.name == routeName || route.isFirst;
    });
  }

  /// 检查路由栈中是否存在指定页面
  static bool hasRouteInStack(BuildContext context, String routeName) {
    bool hasRoute = false;
    Navigator.of(context).popUntil((route) {
      if (route.settings.name == routeName) {
        hasRoute = true;
      }
      return true;
    });
    return hasRoute;
  }
}


