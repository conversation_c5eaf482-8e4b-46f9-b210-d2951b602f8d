import 'package:dlyz_flutter/model/game_post_list.dart';
import '../http_service.dart';
import '../http_base_response.dart';

class GameListService {
  static final GameListService _instance = GameListService._internal();
  factory GameListService() => _instance;
  GameListService._internal();

  final HttpService _httpService = HttpService();

  /// 获取内容分类
  Future<BaseResponse<GamePostList>> getGameList({
    required String baseUrl,
    String? keyword,
    String pageSize = '',
    String offset = '',
  }) async {
    final postParams = <String, dynamic>{
      'keyword': keyword ?? '',
      'pageSize': pageSize,
      'offset': offset
    };
    return await _httpService.post<GamePostList>(
      '/api/gamehub-api/v1/get-game-list',
      baseUrl: baseUrl,
      data: postParams,
      fromJsonT: (data) {
        // 处理服务器返回的数据结构 {"Code":0,"Message":"","Data":{...}}
        if (data is Map<String, dynamic>) {
          final responseData = data['Data'];
          if (responseData != null) {
            return GamePostList.fromJson(responseData);
          }
        }
        return GamePostList.fromJson(data);
      },
    );
  }
}

