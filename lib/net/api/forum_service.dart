import '../http_service.dart';
import '../http_base_response.dart';
import '../../model/forum_category.dart';
import '../../model/forum_post_list.dart';
import '../../model/forum_post_detail.dart';

class ForumService {
  static final ForumService _instance = ForumService._internal();
  factory ForumService() => _instance;
  ForumService._internal();

  final HttpService _httpService = HttpService();
  
  // 论坛统一headers
  static const Map<String, dynamic> _forumHeaders = {
    'authorization': 'Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    'forum-tgid': '147',
    'accept': '*/*',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'cache-control': 'no-cache',
    'content-type': 'application/json',
    'pragma': 'no-cache',
    'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
  };

  /// 获取内容分类
  Future<BaseResponse<List<ForumCategory>>> getCategories({
    required String baseUrl,
  }) async {
    return await _httpService.get<List<ForumCategory>>(
      '/api/v3/categories',
      baseUrl: baseUrl,
      headers: _forumHeaders,
      fromJsonT: (data) {
        // 处理服务器返回的数据结构 {"Code":0,"Message":"","Data":[...]}
        if (data is Map<String, dynamic>) {
          final dataList = data['Data'] as List?;
          if (dataList != null) {
            return dataList
                .map((item) => ForumCategory.fromJson(item))
                .toList();
          }
        }
        // 如果直接是数组格式
        if (data is List) {
          return data
              .map((item) => ForumCategory.fromJson(item))
              .toList();
        }
        return <ForumCategory>[];
      },
    );
  }

  /// 获取帖子列表
  Future<BaseResponse<ForumPostList>> getPostList({
    required String baseUrl,
    int? categoryId,
    int page = 1,
    int pageSize = 20,
  }) async {
    final queryParams = <String, dynamic>{
      'page': page,
      'perPage': pageSize,
    };
    
    if (categoryId != null) {
      queryParams['filter[categoryId]'] = categoryId;
    }

    return await _httpService.get<ForumPostList>(
      '/api/v3/thread.list',
      baseUrl: baseUrl,
      queryParameters: queryParams,
      headers: _forumHeaders,
      fromJsonT: (data) => ForumPostList.fromJson(data),
    );
  }

  /// 获取帖子详情
  Future<BaseResponse<ForumPostDetail>> getPostDetail({
    required String baseUrl,
    required int threadId,
  }) async {
    return await _httpService.get<ForumPostDetail>(
      '/api/v3/thread/$threadId',
      baseUrl: baseUrl,
      headers: _forumHeaders,
      fromJsonT: (data) => ForumPostDetail.fromJson(data),
    );
  }
}