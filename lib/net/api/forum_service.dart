import '../http_service.dart';
import '../http_base_response.dart';
import '../transformers/response_transformers.dart';
import '../../model/forum_category.dart';
import '../../model/forum_post_list.dart';
import '../../model/forum_config.dart';

class ForumService {
  static final ForumService _instance = ForumService._internal();
  factory ForumService() => _instance;
  ForumService._internal();

  final HttpService _httpService = HttpService();
  
  // 论坛统一headers
  static const Map<String, dynamic> _forumHeaders = {
    'authorization': 'Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    'forum-tgid': '147',
    'accept': '*/*',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'cache-control': 'no-cache',
    'content-type': 'application/json',
    'pragma': 'no-cache',
    'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
  };

  /// 获取内容分类
  Future<BaseResponse<List<ForumCategory>>> getCategories({
    required String baseUrl,
  }) async {
    return await _httpService.get<List<ForumCategory>>(
      '/api/v3/categories',
      baseUrl: baseUrl,
      headers: _forumHeaders,
      parseStrategy: ResponseTransformers.codeData<List<ForumCategory>>(),
      fromJsonT: (data) {
        // 直接解析 Data 字段中的数组
        if (data is List) {
          return data
              .map((item) => ForumCategory.fromJson(item))
              .toList();
        }
        return <ForumCategory>[];
      },
    );
  }

  /// 获取帖子列表
  Future<BaseResponse<ForumPostList>> getPostList({
    required String baseUrl,
    int? categoryId,
    String? categoryName,
    int page = 1,
    int pageSize = 5,
  }) async {
    // 根据分类名称设置scope参数：发现分类使用scope=1，其他分类使用scope=0
    final int scope = (categoryName == '发现') ? 1 : 0;
    
    final queryParams = <String, dynamic>{
      'page': page,
      'perPage': pageSize,
      'scope': scope,
      'filter[attention]': 0,
      'filter[sort]': 1,
    };
    
    if (categoryId != null) {
      queryParams['filter[categoryids][0]'] = categoryId;
    }

    return await _httpService.get<ForumPostList>(
      '/api/v3/thread.list',
      baseUrl: baseUrl,
      queryParameters: queryParams,
      headers: _forumHeaders,
      parseStrategy: ResponseTransformers.codeData<ForumPostList>(),
      fromJsonT: (data) {
        // 直接解析 Data 字段中的对象
        return ForumPostList.fromJson(data);
      },
    );
  }

  /// 获取帖子详情
  Future<BaseResponse<ForumPost>> getPostDetail({
    required String baseUrl,
    required int threadId,
  }) async {
    return await _httpService.get<ForumPost>(
      '/api/v3/thread.detail',
      baseUrl: baseUrl,
      queryParameters: {'threadId': threadId},
      headers: _forumHeaders,
      parseStrategy: ResponseTransformers.codeData<ForumPost>(),
      fromJsonT: (data) {
        // 直接解析 Data 字段中的对象
        return ForumPost.fromJson(data);
      },
    );
  }

  /// 获取论坛配置信息
  Future<BaseResponse<ForumConfig>> getForumConfig({
    required String baseUrl,
  }) async {
    return await _httpService.get<ForumConfig>(
      '/api/v3/forum',
      baseUrl: baseUrl,
      headers: _forumHeaders,
      parseStrategy: ResponseTransformers.codeData<ForumConfig>(),
      fromJsonT: (data) {
        // 直接解析 Data 字段中的对象
        return ForumConfig.fromJson(data);
      },
    );
  }

  /// 获取分类对应的推荐/活动/话题配置
  /// 返回 Data 为数组，每个元素包含：style_name/title/content/count 等
  Future<BaseResponse<List<Map<String, dynamic>>>> getActLabel({
    required String baseUrl,
    int? categoryId,
  }) async {
    final queryParams = <String, dynamic>{
      'category_id': categoryId,
    };

    return await _httpService.get<List<Map<String, dynamic>>>(
      '/api/v3/actlabelpage/query',
      baseUrl: baseUrl,
      queryParameters: queryParams,
      headers: _forumHeaders,
      parseStrategy: ResponseTransformers.codeData<List<Map<String, dynamic>>>(),
      fromJsonT: (data) {
        if (data is List) {
          return data.map<Map<String, dynamic>>((e) => Map<String, dynamic>.from(e as Map)).toList();
        }
        return <Map<String, dynamic>>[];
      },
    );
  }
}

