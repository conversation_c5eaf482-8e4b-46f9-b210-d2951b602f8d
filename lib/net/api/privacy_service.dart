import '../http_service.dart';
import '../http_base_response.dart';
import '../transformers/response_transformers.dart';

class PrivacyService {
  static final PrivacyService _instance = PrivacyService._internal();
  factory PrivacyService() => _instance;
  PrivacyService._internal();

  final HttpService _httpService = HttpService();
  
  /// 获取隐私协议配置
  Future<BaseResponse<Map<String, String>>> getPrivacyConfig({
    required String baseUrl,
  }) async {
    return await _httpService.get<Map<String, String>>(
      '/go/cfg/user_protocol', 
      baseUrl: baseUrl,
      parseStrategy: ResponseTransformers.stateData<Map<String, String>>(),
      fromJsonT: (data) {
        return Map<String, String>.from(data);
      },
    );
  }
} 