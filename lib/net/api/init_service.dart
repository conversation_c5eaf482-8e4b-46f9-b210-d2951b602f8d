import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dlyz_flutter/net/http_base_config.dart';
import 'package:dlyz_flutter/net/sign_v2_interceptor.dart';
import 'package:dlyz_flutter/utils/log_util.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import '../../config/app_config.dart';
import '../../utils/enhanced_device_info_util.dart';
import '../http_service.dart';
import '../http_base_response.dart';
import '../transformers/response_transformers.dart';
import '../sign_v5_interceptor.dart';
import '../../model/m_layer_activate_response.dart';
import '../../model/s_layer_activate_response.dart';

class InitService {
  static final InitService _instance = InitService._internal();
  factory InitService() => _instance;
  InitService._internal();

  static DeviceInfoPlugin? _deviceInfoPlugin;
  static PackageInfo? _packageInfo;
  static AndroidDeviceInfo? _androidInfo;
  static IosDeviceInfo? _iosInfo;

  // 激活接口专用的Dio实例缓存
  static Dio? _mActivateDio;
  static Dio? _sActivateDio;

  /// 获取M层激活接口专用的Dio实例
  static Dio _getMActivateDio() {
    if (_mActivateDio != null) return _mActivateDio!;

    _mActivateDio = Dio(
      BaseOptions(
        baseUrl: HttpBaseConfig.mActivateBaseUrl,
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
        headers: {
          'Content-Type': ContentType.form.value,
        },
      ),
    );

    // 添加SignV5签名拦截器
    _mActivateDio!.interceptors.add(
      SignV5Interceptor(appKey: AppConfig.appKey),
    );

    // 添加日志拦截器
    _mActivateDio!.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => LogUtil.d('M层激活 Dio: $obj'),
      ),
    );
    
    return _mActivateDio!;
  }

  /// 获取S层激活接口专用的Dio实例
  static Dio _getSActivateDio() {
    if (_sActivateDio != null) return _sActivateDio!;

    _sActivateDio = Dio(
      BaseOptions(
        baseUrl: HttpBaseConfig.sActivateBaseUrl,
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
        headers: {
          'Content-Type': ContentType.form.value,
        },
      ),
    );

    // 添加SignV5签名拦截器
    _sActivateDio!.interceptors.add(
      SignV2Interceptor(appKey: AppConfig.appKey),
    );

    // 添加日志拦截器
    _sActivateDio!.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => LogUtil.d('S层激活 Dio: $obj'),
      ),
    );

    return _sActivateDio!;
  }

  /// 初始化设备信息
  static Future<void> _initDeviceInfo() async {
    _deviceInfoPlugin ??= DeviceInfoPlugin();
    _packageInfo ??= await PackageInfo.fromPlatform();

    if (Platform.isAndroid) {
      _androidInfo ??= await _deviceInfoPlugin!.androidInfo;
    } else if (Platform.isIOS) {
      _iosInfo ??= await _deviceInfoPlugin!.iosInfo;
    }
  }

  /// 获取MAC地址 (模拟)
  static String _getMacAddress() {
    // 由于隐私限制，返回模拟的MAC地址
    return "020000000000";
  }

  /// 获取IMEI (模拟)
  static String _getImei() {
    // 由于隐私限制，返回模拟的IMEI
    return "99937734161624367";
  }

  /// 获取屏幕宽度像素
  static int _getWidthPixels(BuildContext? context) {
    if (context != null) {
      return EnhancedDeviceInfoUtil.getWidthPixels(context);
    }
    return 1080; // 默认值
  }

  /// 获取屏幕高度像素
  static int _getHeightPixels(BuildContext? context) {
    if (context != null) {
      return EnhancedDeviceInfoUtil.getHeightPixels(context);
    }
    return 1920; // 默认值
  }

  /// 获取设备型号
  static Future<String> _getDeviceModel() async {
    return await EnhancedDeviceInfoUtil.getDeviceModel();
  }

  /// 获取操作系统版本
  static Future<String> _getOSVersion() async {
    return await EnhancedDeviceInfoUtil.getOSVersion();
  }

  /// 获取操作系统描述
  static Future<String> _getOSDescription() async {
    return await EnhancedDeviceInfoUtil.getOSDescription();
  }

  /// 获取设备品牌
  static Future<String> _getDeviceBrand() async {
    return await EnhancedDeviceInfoUtil.getDeviceBrand();
  }

  /// 获取电话号码 (模拟)
  static String _getPhoneNumber() {
    // 由于隐私限制，返回空字符串
    return "";
  }

  /// 获取应用包名
  static Future<String> _getPackageName() async {
    return await EnhancedDeviceInfoUtil.getPackageName();
  }

  /// 获取网络类型 (简化版本)
  static String _getNetworkType() {
    // 简化版本，实际需要使用connectivity_plus插件
    return "wifi"; // 默认值
  }

  /// 获取版本号
  static Future<String> _getVersionCode() async {
    return await EnhancedDeviceInfoUtil.getVersionCode();
  }

  /// 获取电池电量 (模拟)
  static int _getBatteryLevel() {
    // 需要使用battery_plus插件
    return 100; // 默认值
  }

  /// 获取电池状态 (模拟)
  static int _getBatteryStatus() {
    // 需要使用battery_plus插件
    return 2; // 2表示不在充电
  }

  /// 获取WiFi SSID (模拟)
  static String _getWifiSSID() {
    // 需要使用wifi_info_flutter插件
    return "";
  }

  /// 获取WiFi BSSID (模拟)
  static String _getWifiBSSID() {
    // 需要使用wifi_info_flutter插件
    return "";
  }

  /// 获取插件版本号
  static String _getPluginVersion() {
    return "1.0.0"; // 默认插件版本
  }

  /// 检查是否为模拟器
  static Future<String> _getIsSimulator() async {
    bool isEmulator = await EnhancedDeviceInfoUtil.isEmulator();
    return isEmulator ? '1' : '0';
  }

  /// 获取Android ID
  static Future<String> _getAndroidId() async {
    if (Platform.isAndroid) {
      return await EnhancedDeviceInfoUtil.getAndroidId();
    }
    return '100a059eb38a2d9d'; // 默认值
  }

  /// 构建公共参数
  static Future<Map<String, dynamic>> _buildCommonParams(
      {BuildContext? context}) async {
    await _initDeviceInfo();

    final now = DateTime
        .now()
        .millisecondsSinceEpoch ~/ 1000;

    return {
      // 原有参数
      'dev': "000",
      'dev2': "000",
      'over': await _getOSVersion(),
      'gwversion': AppConfig.gwversion,
      'host_sdk_version': AppConfig.sversion,
      'gid': AppConfig.gid,
      'os': Platform.isAndroid ? 'android' : 'ios',
      'pid': AppConfig.pid,
      'is_root': '0',
      'version': AppConfig.sversion,
      'mac': _getMacAddress(),
      'is_simulator': await _getIsSimulator(),
      'refer': 'sy_00001',
      'sversion': AppConfig.sversion,
      'imei': _getImei(),
      'time': now.toString(),
      'android_id': await _getAndroidId(),

      // 新增Java代码中的参数
      'wpi': _getWidthPixels(context).toString(),
      'hpi': _getHeightPixels(context).toString(),
      'mode': await _getDeviceModel(),
      'os_desc': await _getOSDescription(),
      'brand': await _getDeviceBrand(),
      'phone': _getPhoneNumber(),
      'dpgn': await _getPackageName(),
      'nwk': _getNetworkType(),
      'sua': Platform.isAndroid ? '1' : '3', // 1:安卓, 2:iOS越狱, 3:正版iOS
      'versionCode': await _getVersionCode(),

      // 3.5.6 新增电量、WiFi信息
      'battery_level': _getBatteryLevel().toString(),
      'battery_status': _getBatteryStatus().toString(),
      'ssid': _getWifiSSID(),
      'bssid': _getWifiBSSID(),

      // 插件版本号
      'pluginVersion': _getPluginVersion(),
    };
  }

  /// 获取公共参数 (对外接口)
  /// [context] 用于获取准确的屏幕尺寸，如果不提供则使用默认值
  static Future<Map<String, dynamic>> getCommonParams({
    BuildContext? context,
  }) async {
    return await _buildCommonParams(context: context);
  }

  /// M层激活接口
  /// SDK激活接口，用于激活SDK功能
  /// [context] 用于获取准确的屏幕尺寸，如果不提供则使用默认值
  /// [additionalParams] 额外的参数，会合并到公共参数中
  static Future<BaseResponse<MLayerActivateResponse>> mActivate({
    BuildContext? context,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      // 获取当前时间戳（毫秒）
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      
      // 构建请求URL，包含时间戳参数
      final url = '/sdk/active/?t=$timestamp';
      
      // 获取公共参数
      final commonParams = await getCommonParams(context: context);
      
      // 合并额外参数
      if (additionalParams != null) {
        commonParams.addAll(additionalParams);
      }

      LogUtil.d('M层激活请求: $url, 参数: $commonParams');

      // 使用专用的Dio实例发送POST请求
      final response = await _getMActivateDio().post(url, data: commonParams);

      LogUtil.d('M层激活响应: ${response.statusCode}, 数据: ${response.data}');

      // 直接使用ResponseTransformers解析
      final parseStrategy = ResponseTransformers.stateData<MLayerActivateResponse>();
      return parseStrategy.parse(response.data, (data) {
        if (data == null) {
          throw Exception('响应数据为空');
        }
        if (data is Map<String, dynamic>) {
          return MLayerActivateResponse.fromJson(data);
        }
        throw Exception('数据格式不正确');
      });
    } catch (e) {
      LogUtil.e('M层激活请求失败: $e');
      return BaseResponse<MLayerActivateResponse>(
        code: 500,
        message: '激活请求失败: $e',
        data: null,
      );
    }
  }

  // ==================== S层激活接口 ====================

  /// S层激活接口
  /// S层SDK激活接口，返回完整的业务配置信息
  /// [context] 用于获取准确的屏幕尺寸，如果不提供则使用默认值
  /// [additionalParams] 额外的参数，会合并到公共参数中
  static Future<BaseResponse<SLayerActivateResponse>> sActivate({
    BuildContext? context,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      // 获取当前时间戳（毫秒）
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      
      // 构建请求URL，包含时间戳参数
      final url = '/sdk/active/?t=$timestamp';
      
      // 获取公共参数
      final commonParams = await getCommonParams(context: context);
      
      // 合并额外参数
      if (additionalParams != null) {
        commonParams.addAll(additionalParams);
      }

      LogUtil.d('S层激活请求: $url, 参数: $commonParams');

      // 使用S层专用的Dio实例发送POST请求
      final response = await _getSActivateDio().post(url, data: commonParams);

      LogUtil.d('S层激活响应: ${response.statusCode}, 数据: ${response.data}');

      // 直接使用ResponseTransformers解析
      final parseStrategy = ResponseTransformers.stateData<SLayerActivateResponse>();
      return parseStrategy.parse(response.data, (data) {
        if (data == null) {
          throw Exception('响应数据为空');
        }
        if (data is Map<String, dynamic>) {
          return SLayerActivateResponse.fromJson(data);
        }
        throw Exception('数据格式不正确');
      });
    } catch (e) {
      LogUtil.e('S层激活请求失败: $e');
      return BaseResponse<SLayerActivateResponse>(
        code: 500,
        message: 'S层激活请求失败: $e',
        data: null,
      );
    }
  }
}