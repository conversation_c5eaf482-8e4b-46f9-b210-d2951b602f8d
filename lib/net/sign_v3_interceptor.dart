import 'package:dio/dio.dart';
import '../utils/md5_utils.dart';

/// SignV3签名拦截器
/// 参考Android项目中的SignV3Interceptor实现
/// https://developers.37.com.cn/developer/domestic/official/server/intro/#%E7%AD%BE%E5%90%8D%E8%A7%84%E5%88%99
class SignV3Interceptor extends Interceptor {
  static const String _paramName = 'sign';
  final String _appKey;

  SignV3Interceptor({required String appKey}) : _appKey = appKey {
    if (appKey.isEmpty) {
      throw ArgumentError('App key cannot be empty!');
    }
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    try {
      // 处理POST请求的表单数据
      if (options.method.toUpperCase() == 'POST' && options.data != null) {
        if (options.data is Map<String, dynamic>) {
          final params = Map<String, String>.from(options.data);
          final sign = _generateSign(params);
          params[_paramName] = sign;
          options.data = params;
          
          print('[SignV3] POST签名完成: $sign');
        } else if (options.data is String) {
          // 处理form-encoded字符串
          final params = _parseFormData(options.data as String);
          final sign = _generateSign(params);
          params[_paramName] = sign;
          options.data = params;

          print('[SignV3] POST Form签名完成: $sign');
        }
      }
      // 处理GET请求的查询参数
      else if (options.method.toUpperCase() == 'GET' && options.queryParameters.isNotEmpty) {
        final params = Map<String, String>.from(options.queryParameters);
        final sign = _generateSign(params);
        options.queryParameters[_paramName] = sign;
        
        print('[SignV3] GET签名完成: $sign');
      }
      
      handler.next(options);
    } catch (e) {
      print('[SignV3] 签名异常: $e');
      handler.next(options);
    }
  }

  /// 生成V3签名
  /// 1. 提取除了sign以外的所有参数
  /// 2. 如果参数值有为空的参数, 值用空字符串代替值
  /// 3. 将参数以其参数名的字典序升序进行排序
  /// 4. 遍历排序后的参数数组中的每一个key/value对, 生成一个key=value格式的字符串
  /// 5. 在末尾连接上 key , 得到签名原始字符串
  /// 6. 对签名原始字符串计算md5值, 得到最终签名(小写)
  String _generateSign(Map<String, String> params) {
    // 1. 提取除了sign以外的所有参数
    final filteredParams = <String, String>{};
    params.forEach((key, value) {
      if (key != _paramName) {
        // 2. 如果参数值有为空的参数, 值用空字符串代替值
        filteredParams[key] = value ?? '';
      }
    });

    // 3. 将参数以其参数名的字典序升序进行排序
    final sortedKeys = filteredParams.keys.toList()..sort();

    // 4. 遍历排序后的参数数组中的每一个key/value对, 生成一个key=value格式的字符串
    final signStrBuffer = StringBuffer();
    for (final key in sortedKeys) {
      signStrBuffer.write('$key=${filteredParams[key]}');
    }

    // 5. 在末尾连接上 key , 得到签名原始字符串
    signStrBuffer.write(_appKey);
    final signStr = signStrBuffer.toString();

    // 6. 对签名原始字符串计算md5值, 得到最终签名(小写)
    final sign = Md5Utils.generateMd5(signStr).toLowerCase();
    
    print('[SignV3] 签名原串: $signStr');
    print('[SignV3] 签名结果: $sign');
    
    return sign;
  }

  /// 解析form-encoded数据
  Map<String, String> _parseFormData(String formData) {
    final params = <String, String>{};
    if (formData.isNotEmpty) {
      final pairs = formData.split('&');
      for (final pair in pairs) {
        final parts = pair.split('=');
        if (parts.length == 2) {
          params[parts[0]] = parts[1];
        } else if (parts.length == 1) {
          params[parts[0]] = '';
        }
      }
    }
    return params;
  }
}