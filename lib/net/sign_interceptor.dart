import 'package:dio/dio.dart';
import 'package:dlyz_flutter/net/sign_v2_interceptor.dart';
import 'package:dlyz_flutter/net/sign_v3_interceptor.dart';
import 'package:dlyz_flutter/net/sign_v5_interceptor.dart';

/// 签名版本枚举
enum SignType {
  v2('V2'),
  v3('V3'),
  v5('V5');

  const SignType(this.version);

  final String version;
}

/// 动态签名拦截器
/// 支持在请求时动态指定签名类型，而不需要预先固定签名版本
class SignInterceptor extends Interceptor {
  static const String _tag = 'SignInterceptor';

  /// 请求选项中用于指定签名类型的键名
  /// 使用方式: options.extra[SignInterceptor.signTypeKey] = SignType.v3
  static const String signTypeKey = 'signType';

  final String _appKey;

  // 缓存不同版本的签名拦截器实例，避免重复创建
  final Map<SignType, Interceptor> _interceptorCache = {};

  /// 构造函数
  /// [appKey] 应用密钥
  SignInterceptor({required String appKey}) : _appKey = appKey {
    _validateAppKey();
  }

  /// 验证AppKey
  void _validateAppKey() {
    if (_appKey.isEmpty) {
      throw ArgumentError.value(_appKey, 'appKey', 'App key cannot be empty');
    }
  }

  /// 获取或创建指定版本的签名拦截器
  Interceptor _getInterceptor(SignType signType) {
    return _interceptorCache.putIfAbsent(signType, () {
      switch (signType) {
        case SignType.v2:
          return SignV2Interceptor(appKey: _appKey);
        case SignType.v3:
          return SignV3Interceptor(appKey: _appKey);
        case SignType.v5:
          return SignV5Interceptor(appKey: _appKey);
        default:
          return SignV3Interceptor(appKey: _appKey);
      }
    });
  }

  /// 从请求选项中提取签名类型
  SignType _extractSignType(RequestOptions options) {
    final signTypeValue = options.extra[signTypeKey];

    if (signTypeValue == null) {
      return SignType.v3;
    }

    if (signTypeValue is SignType) {
      return signTypeValue;
    }

    if (signTypeValue is String) {
      switch (signTypeValue.toUpperCase()) {
        case 'V2':
          return SignType.v2;
        case 'V3':
          return SignType.v3;
        case 'V5':
          return SignType.v5;
        default:
          return SignType.v3;
      }
    }

    return SignType.v3;
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final signType = _extractSignType(options);

    // 移除签名类型参数，避免传递给服务端
    options.extra.remove(signTypeKey);

    // 获取对应的签名拦截器并处理请求
    final interceptor = _getInterceptor(signType);
    interceptor.onRequest(options, handler);
  }

  /// 获取当前使用的AppKey
  String get appKey => _appKey;
}
