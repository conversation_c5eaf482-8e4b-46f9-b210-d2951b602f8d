import 'package:dio/dio.dart';
import 'package:dlyz_flutter/net/sign_v2_interceptor.dart';
import 'package:dlyz_flutter/net/sign_v3_interceptor.dart';
import 'package:dlyz_flutter/net/sign_v5_interceptor.dart';

import '../config/app_config.dart';

class SignInterceptor extends Interceptor {
  final String _appKey;

  SignV2Interceptor? _signV2Interceptor;
  SignV3Interceptor? _signV3Interceptor;
  SignV5Interceptor? _signV5Interceptor;

  SignInterceptor({required String appKey}) : _appKey = appKey {
    if (appKey.isEmpty) {
      throw ArgumentError('App key cannot be empty!');
    }
    _signV2Interceptor = SignV2Interceptor(appKey: _appKey);
    _signV3Interceptor = SignV3Interceptor(appKey: _appKey);
    _signV5Interceptor = SignV5Interceptor(appKey: _appKey);
  }

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    super.onRequest(options, handler);
  }
}

///签名版本
enum SignType { V2, V3, V5 }
