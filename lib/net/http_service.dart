import 'package:dio/dio.dart';
import 'http_base_response.dart';
import 'http_error_code.dart';
import 'http_net_exception.dart';
import '../utils/log_util.dart';

class HttpService {
  static final HttpService _instance = HttpService._internal();

  factory HttpService() => _instance;

  final Map<String, Dio> _dioCache = {};

  HttpService._internal();

  static HttpService getInstance() {
    return _instance;
  }

  Dio _getDio(String baseUrl) {
    if (_dioCache.containsKey(baseUrl)) return _dioCache[baseUrl]!;

    final dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
        headers: {'Content-Type': 'application/json'},
      ),
    );

    dio.interceptors.add(LogInterceptor(responseBody: true));
    _dioCache[baseUrl] = dio;
    return dio;
  }

  Future<BaseResponse<T>> get<T>(
    String path, {
    required String baseUrl,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    required T Function(dynamic) fromJsonT,
  }) async {
    final stopwatch = Stopwatch()..start();
    final fullUrl = '$baseUrl$path';
    
    // 记录请求日志
    LogUtil.request(
      fullUrl,
      method: 'GET',
      params: queryParameters,
      headers: headers,
    );

    try {
      final response = await _getDio(
        baseUrl,
      ).get(
        path, 
        queryParameters: queryParameters,
        options: headers != null ? Options(headers: headers) : null,
      );
      
      stopwatch.stop();
      
      // 记录响应日志
      LogUtil.response(
        fullUrl,
        response.statusCode ?? 0,
        response.data,
        method: 'GET',
        duration: stopwatch.elapsedMilliseconds,
      );
      
      return _handleResponse(response, fromJsonT);
    } on DioException catch (e) {
      stopwatch.stop();
      
      // 记录错误响应日志
      LogUtil.response(
        fullUrl,
        e.response?.statusCode ?? 0,
        e.response?.data ?? e.message,
        method: 'GET',
        duration: stopwatch.elapsedMilliseconds,
      );
      
      LogUtil.e('GET请求失败: $fullUrl', error: e);
      return handleDioError<T>(e);
    } catch (e) {
      stopwatch.stop();
      LogUtil.e('GET请求异常: $fullUrl', error: e);
      return BaseResponse.error('未知错误: $e', code: ErrorCode.unknown);
    }
  }

  Future<BaseResponse<T>> post<T>(
    String path, {
    required String baseUrl,
    Map<String, dynamic>? data,
    Map<String, dynamic>? headers,
    required T Function(dynamic) fromJsonT,
  }) async {
    final stopwatch = Stopwatch()..start();
    final fullUrl = '$baseUrl$path';
    
    // 记录请求日志
    LogUtil.request(
      fullUrl,
      method: 'POST',
      params: data,
      headers: headers,
    );

    try {
      final response = await _getDio(baseUrl).post(
        path, 
        data: data,
        options: headers != null ? Options(headers: headers) : null,
      );
      
      stopwatch.stop();
      
      // 记录响应日志
      LogUtil.response(
        fullUrl,
        response.statusCode ?? 0,
        response.data,
        method: 'POST',
        duration: stopwatch.elapsedMilliseconds,
      );
      
      return _handleResponse(response, fromJsonT);
    } on DioException catch (e) {
      stopwatch.stop();
      
      // 记录错误响应日志
      LogUtil.response(
        fullUrl,
        e.response?.statusCode ?? 0,
        e.response?.data ?? e.message,
        method: 'POST',
        duration: stopwatch.elapsedMilliseconds,
      );
      
      LogUtil.e('POST请求失败: $fullUrl', error: e);
      return handleDioError<T>(e);
    } catch (e) {
      stopwatch.stop();
      LogUtil.e('POST请求异常: $fullUrl', error: e);
      return BaseResponse.error('未知错误: $e', code: ErrorCode.unknown);
    }
  }

  BaseResponse<T> _handleResponse<T>(
    Response response,
    T Function(dynamic json) fromJsonT,
  ) {
    if (response.statusCode == 200 || response.statusCode == 201) {
      try {
        // 检查是否是标准的API响应格式 {"Code":0,"Message":"","Data":...}
        if (response.data is Map<String, dynamic>) {
          final responseData = response.data as Map<String, dynamic>;
          final code = responseData['Code'] ?? responseData['code'] ?? 200;
          final message = responseData['Message'] ?? responseData['message'] ?? 'OK';
          
          // 如果业务逻辑返回成功
          if (code == 0) {
            final parsedData = fromJsonT(response.data);
            return BaseResponse<T>(code: code, message: message, data: parsedData);
          } else {
            // 业务逻辑返回失败
            LogUtil.w('业务逻辑失败: code=$code, message=$message');
            return BaseResponse.error(message, code: code);
          }
        } else {
          // 直接是数据格式，按成功处理
          final parsedData = fromJsonT(response.data);
          return BaseResponse<T>(code: 200, message: 'OK', data: parsedData);
        }
      } catch (e) {
        LogUtil.e('数据解析失败', error: e);
        return BaseResponse.error('解析失败: $e', code: ErrorCode.parseError);
      }
    } else {
      LogUtil.w('HTTP状态码异常: ${response.statusCode}');
      return BaseResponse.error(
        'HTTP错误: ${response.statusCode}',
        code: ErrorCode.httpError,
      );
    }
  }
}
