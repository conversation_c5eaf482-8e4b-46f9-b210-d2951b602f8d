import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/login_provider.dart';
import '../../model/user_info.dart';
import '../../services/agreement_service.dart';
import 'login_dialog_title_bar.dart';

class LoginDialogHistoryAccount extends StatefulWidget {
  final UserInfo? selectedUser;
  final List<UserInfo> userList;
  final VoidCallback? onLogin;
  final VoidCallback? onForgetPassword;
  final VoidCallback? onOtherLogin;
  final VoidCallback? onWechatLogin;
  final VoidCallback? onClose;
  final Function(UserInfo)? onUserSelected;
  final Function(UserInfo)? onUserDeleted;

  const LoginDialogHistoryAccount({
    super.key,
    this.selectedUser,
    required this.userList,
    this.onLogin,
    this.onForgetPassword,
    this.onOtherLogin,
    this.onWechatLogin,
    this.onClose,
    this.onUserSelected,
    this.onUserDeleted,
  });

  @override
  State<LoginDialogHistoryAccount> createState() => _LoginDialogHistoryAccountState();
}

class _LoginDialogHistoryAccountState extends State<LoginDialogHistoryAccount> with SingleTickerProviderStateMixin {
  bool _isAgreed = false;
  bool _showAccountList = false;
  UserInfo? _selectedUser;
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _selectedUser = widget.selectedUser;
    
    // 如果没有选中用户但有用户列表，默认选中第一个
    if (_selectedUser == null && widget.userList.isNotEmpty) {
      _selectedUser = widget.userList.first;
    }
    
    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(LoginDialogHistoryAccount oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedUser != oldWidget.selectedUser) {
      _selectedUser = widget.selectedUser;
    }
    
    // 如果用户列表发生变化且没有选中用户，默认选中第一个
    if (widget.userList != oldWidget.userList && 
        _selectedUser == null && 
        widget.userList.isNotEmpty) {
      _selectedUser = widget.userList.first;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Center(
        child: Container(
          width: 380,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              LoginDialogTitleBar(
                onClose: widget.onClose,
                showBackButton: false,
              ),
              // 内容区域
              Flexible(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(height: 20),
                      // 账号选择区域
                      _buildAccountSelection(),
                      const SizedBox(height: 20),
                      // 协议勾选
                      _buildAgreement(),
                      const SizedBox(height: 20),
                      // 登录按钮
                      _buildLoginButton(),
                      const SizedBox(height: 20),
                      // 底部链接
                      _buildBottomLinks(),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAccountSelection() {
    return Column(
      children: [
        // 历史账号选择框
        GestureDetector(
          onTap: _toggleAccountList,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: const Color(0xFFE5E5E5)),
            ),
            child: Row(
              children: [
                // 手机图标
                Padding(
                  padding: const EdgeInsets.only(left: 12),
                  child: _buildPhoneIcon(),
                ),
                // 账号文本
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                    child: Text(
                      _selectedUser?.displayName ?? '请选择账号',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF333333),
                      ),
                    ),
                  ),
                ),
                // 下拉箭头
                Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: Icon(
                    _showAccountList ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    color: const Color(0xFF999999),
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
        ),
        // 账号列表
        AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return SizeTransition(
              sizeFactor: _animation,
              child: _showAccountList && widget.userList.isNotEmpty
                  ? Container(
                      margin: const EdgeInsets.only(top: 4),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: const Color(0xFFE5E5E5)),
                      ),
                      child: Column(
                        children: widget.userList.map((user) => _buildAccountItem(user)).toList(),
                      ),
                    )
                  : const SizedBox.shrink(),
            );
          },
        ),
      ],
    );
  }

  Widget _buildPhoneIcon() {
    return Container(
      width: 20,
      height: 20,
      child: Image.asset(
        'assets/images/sysq_item_account_phone.png',
        width: 20,
        height: 20,
        errorBuilder: (context, error, stackTrace) {
          return const Icon(
            Icons.phone,
            color: Color(0xFF999999),
            size: 14,
          );
        },
      ),
    );
  }

  Widget _buildAccountItem(UserInfo user) {
    return GestureDetector(
      onTap: () => _selectUser(user),
      child: Container(
        height: 42,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(
              color: const Color(0xFFE5E5E5),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            // 账号类型图标
            Padding(
              padding: const EdgeInsets.only(left: 12),
              child: _buildAccountItemIcon(user),
            ),
            // 账号信息
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(left: 8),
                child: Text(
                  user.displayName,
                  style: const TextStyle(
                    fontSize: 15,
                    color: Color(0xFF333333),
                  ),
                ),
              ),
            ),
            // 删除按钮 - 所有账号项都显示删除按钮
            GestureDetector(
              onTap: () => _deleteUser(user),
              child: const Padding(
                padding: EdgeInsets.only(right: 12),
                child: Icon(
                  Icons.close,
                  color: Color(0xFF999999),
                  size: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountItemIcon(UserInfo user) {
    switch (user.loginTypeEnum) {
      case LoginType.phone:
        return _buildPhoneIcon();
      case LoginType.wechat:
        return _buildWechatIcon();
      case LoginType.account:
        return _buildAccountIcon();
    }
  }

  Widget _buildWechatIcon() {
    return Container(
      width: 20,
      height: 20,
      child: Image.asset(
        'assets/images/sysq_item_account_wechat.png',
        width: 20,
        height: 20,
        errorBuilder: (context, error, stackTrace) {
          return const Icon(
            Icons.chat,
            color: Color(0xFF4CAF50),
            size: 18,
          );
        },
      ),
    );
  }

  Widget _buildAccountIcon() {
    return Container(
      width: 20,
      height: 20,
      child: Image.asset(
        'assets/images/sysq_ic_account.png',
        width: 20,
        height: 20,
        errorBuilder: (context, error, stackTrace) {
          return const Icon(
            Icons.person,
            color: Color(0xFF2196F3),
            size: 18,
          );
        },
      ),
    );
  }

  Widget _buildAgreement() {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              _isAgreed = !_isAgreed;
            });
          },
          child: Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              border: Border.all(
                color: _isAgreed ? const Color(0xFFFF7700) : const Color(0xFFCCCCCC),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(2),
              color: _isAgreed ? const Color(0xFFFF7700) : Colors.transparent,
            ),
            child: _isAgreed
                ? const Icon(
                    Icons.check,
                    size: 12,
                    color: Colors.white,
                  )
                : null,
          ),
        ),
        const SizedBox(width: 5),
        Expanded(
          child: Row(
            children: [
              const Text(
                '已阅读并同意',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF999999),
                ),
              ),
              GestureDetector(
                onTap: () {
                  AgreementService().showUserProtocol(context);
                },
                child: const Text(
                  '《用户协议》',
                  style: TextStyle(
                    fontSize: 11,
                    color: Color(0xFFFF7700),
                  ),
                ),
              ),
              const Text(
                '及',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF999999),
                ),
              ),
              GestureDetector(
                onTap: () {
                  AgreementService().showPrivacyPolicy(context);
                },
                child: const Text(
                  '《隐私政策》',
                  style: TextStyle(
                    fontSize: 11,
                    color: Color(0xFFFF7700),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLoginButton() {
    return SizedBox(
      width: double.infinity,
      height: 40,
      child: ElevatedButton(
        onPressed: _handleLogin,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFF7700),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
          elevation: 0,
        ),
        child: const Text(
          '登录',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildBottomLinks() {
    return Row(
      children: [
        // 找回账号/密码
        GestureDetector(
          onTap: widget.onForgetPassword,
          child: const Text(
            '找回账号/密码',
            style: TextStyle(
              fontSize: 13,
              color: Color(0xFF333333),
            ),
          ),
        ),
        const Spacer(),
        // 微信登录
        GestureDetector(
          onTap: widget.onWechatLogin,
          child: const Text(
            '微信登录',
            style: TextStyle(
              fontSize: 13,
              color: Color(0xFF333333),
            ),
          ),
        ),
        const SizedBox(width: 16),
        // 其他账号登录
        GestureDetector(
          onTap: widget.onOtherLogin,
          child: const Text(
            '其他账号登录',
            style: TextStyle(
              fontSize: 13,
              color: Color(0xFF333333),
            ),
          ),
        ),
      ],
    );
  }

  void _toggleAccountList() {
    setState(() {
      _showAccountList = !_showAccountList;
    });
    
    // 播放动画
    if (_showAccountList) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  void _selectUser(UserInfo user) async {
    setState(() {
      _selectedUser = user;
      _showAccountList = false;
    });
    
    // 通知Provider
    final loginProvider = Provider.of<LoginProvider>(context, listen: false);
    await loginProvider.selectUser(user);
    
    widget.onUserSelected?.call(user);
  }

  void _deleteUser(UserInfo user) async {
    // 通知Provider
    final loginProvider = Provider.of<LoginProvider>(context, listen: false);
    await loginProvider.deleteUser(user);
    
    // 如果删除的是当前选中的用户，清除选中状态
    if (_selectedUser?.userId == user.userId) {
      setState(() {
        _selectedUser = null;
      });
    }
    
    widget.onUserDeleted?.call(user);
  }

  void _handleLogin() {
    widget.onLogin?.call();
  }

  void _showToast(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }
} 