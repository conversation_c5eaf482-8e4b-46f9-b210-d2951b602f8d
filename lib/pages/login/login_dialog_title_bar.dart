import 'package:flutter/material.dart';

/// 登录弹窗通用标题栏组件
class LoginDialogTitleBar extends StatelessWidget {
  final VoidCallback? onClose;
  final VoidCallback? onBack;
  final bool showBackButton;
  final bool showCloseButton;

  const LoginDialogTitleBar({
    super.key,
    this.onClose,
    this.onBack,
    this.showBackButton = true,
    this.showCloseButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50,
      child: Stack(
        children: [
          // Logo居中显示
          Center(
            child: Padding(
              padding: const EdgeInsets.only(top: 23),
              child: Image.asset(
                'assets/images/sy37_ic_logo.png',
                width: 269,
                height: 69,
                errorBuilder: (context, error, stackTrace) {
                  return const Text(
                    '37手游',
                    style: TextStyle(
                      fontSize: 19,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF000000),
                    ),
                  );
                },
              ),
            ),
          ),
          // 返回按钮
          if (showBackButton)
            Positioned(
              left: 0,
              top: 0,
              child: IconButton(
                onPressed: onBack ?? onClose,
                icon: const Icon(Icons.arrow_back, size: 20),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ),
          // 关闭按钮
          if (showCloseButton)
            Positioned(
              right: 0,
              top: 0,
              child: IconButton(
                onPressed: onClose,
                icon: const Icon(Icons.close, size: 20),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ),
        ],
      ),
    );
  }
} 