import 'package:flutter/material.dart';
import 'login_phone_input_section.dart';
import 'login_dialog_title_bar.dart';

class LoginDialogPhone extends StatefulWidget {
  final String? phoneNumber;
  final Function(String phoneNumber)? onGetVerifyCode;
  final VoidCallback? onForgetPassword;
  final VoidCallback? onAccountLogin;
  final VoidCallback? onWechatLogin;
  final VoidCallback? onUserAgreement;
  final VoidCallback? onPrivacyPolicy;
  final VoidCallback? onClose;

  const LoginDialogPhone({
    super.key,
    this.phoneNumber,
    this.onGetVerifyCode,
    this.onForgetPassword,
    this.onAccountLogin,
    this.onWechatLogin,
    this.onUserAgreement,
    this.onPrivacyPolicy,
    this.onClose,
  });

  @override
  State<LoginDialogPhone> createState() => _LoginDialogPhoneState();
}

class _LoginDialogPhoneState extends State<LoginDialogPhone> {
  void _showToast(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Center(
        child: Container(
          width: 340,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          decoration: BoxDecoration(
            color: const Color(0xFFF8F8F8),
            borderRadius: BorderRadius.circular(5),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              LoginDialogTitleBar(
                onClose: widget.onClose,
                onBack: widget.onClose,
                showBackButton: true,
                showCloseButton: true,
              ),
              // 内容区域
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 复用手机号输入区域组件
                      LoginPhoneInputSection(
                        initialPhoneNumber: widget.phoneNumber,
                        onGetVerifyCode: widget.onGetVerifyCode,
                        onUserAgreement: widget.onUserAgreement,
                        onPrivacyPolicy: widget.onPrivacyPolicy,
                      ),
                      const SizedBox(height: 20),
                      // 底部链接
                      _buildBottomLinks(),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }





  Widget _buildBottomLinks() {
    return Row(
      children: [
        // 找回账号/密码
        GestureDetector(
          onTap: widget.onForgetPassword,
          child: const Text(
            '找回账号/密码',
            style: TextStyle(
              fontSize: 13,
              color: Color(0xFF333333),
            ),
          ),
        ),
        const Spacer(),
        // 微信登录
        GestureDetector(
          onTap: widget.onWechatLogin,
          child: const Text(
            '微信登录',
            style: TextStyle(
              fontSize: 13,
              color: Color(0xFF333333),
            ),
          ),
        ),
        const SizedBox(width: 16),
        // 账号密码登录
        GestureDetector(
          onTap: widget.onAccountLogin,
          child: const Text(
            '账号密码登录',
            style: TextStyle(
              fontSize: 13,
              color: Color(0xFF333333),
            ),
          ),
        ),
      ],
    );
  }
} 