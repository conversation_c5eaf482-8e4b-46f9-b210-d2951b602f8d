import 'package:flutter/material.dart';
import 'dart:async';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';
import 'login_dialog_title_bar.dart';
import '../../providers/login_provider.dart';

class LoginDialogVerifyCode extends StatefulWidget {
  final String phoneNumber;
  final VoidCallback? onClose;
  final VoidCallback? onSendCode;
  final Function(String phone, String code)? onLoginWithCode;
  final VoidCallback? onForgetPassword;
  final VoidCallback? onAccountLogin;
  final VoidCallback? onWechatLogin;


  const LoginDialogVerifyCode({
    super.key,
    required this.phoneNumber,
    this.onClose,
    this.onSendCode,
    this.onLoginWithCode,
    this.onForgetPassword,
    this.onAccountLogin,
    this.onWechatLogin,

  });

  @override
  State<LoginDialogVerifyCode> createState() => _LoginDialogVerifyCodeState();
}

class _LoginDialogVerifyCodeState extends State<LoginDialogVerifyCode> {
  bool _canSendCode = true;
  int _countdown = 0;
  Timer? _timer;
  bool _hasInitialized = false;
  
  final TextEditingController _codeController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // 不在initState中调用context相关的方法
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 在didChangeDependencies中初始化，确保context可用
    if (!_hasInitialized) {
      _hasInitialized = true;
      // 使用SchedulerBinding.addPostFrameCallback确保在build完成后调用
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _sendCode();
      });
    }
  }

  @override
  void dispose() {
    _codeController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    _countdown = 60;
    _canSendCode = false;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_countdown > 0) {
          _countdown--;
        } else {
          _canSendCode = true;
          timer.cancel();
        }
      });
    });
  }

  void _sendCode() {
    if (!_canSendCode) return;
    
    widget.onSendCode?.call();
    _startCountdown();
    
    // 使用SchedulerBinding.addPostFrameCallback来延迟显示Toast
    // 确保在build完成后且context可用时调用
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (mounted && context.mounted) {
        _showToast('验证码已发送');
      }
    });
  }

  void _showToast(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _loginWithCode() async {
    final code = _codeController.text.trim();
    
    if (code.isEmpty) {
      _showToast('请输入验证码');
      return;
    }
    
    if (code.length != 6) {
      _showToast('请输入6位验证码');
      return;
    }
    
    // 显示加载状态
    _showLoadingDialog();
    
    try {
      // 获取登录提供者
      final loginProvider = Provider.of<LoginProvider>(context, listen: false);
      
      // 使用验证码登录方法，这会保存用户信息到历史账号
      final result = await loginProvider.loginWithVerifyCode(
        phoneNumber: widget.phoneNumber!,
        verifyCode: code,
      );
      
      // 隐藏加载对话框
      if (mounted) {
        Navigator.of(context).pop();
        
        if (result['success']) {
          _showToast('登录成功');
          // 关闭当前对话框
          if (mounted) {
            Navigator.of(context).pop();
          }
        } else {
          _showToast(result['message'] ?? '登录失败');
        }
      }
    } catch (e) {
      // 隐藏加载对话框
      if (mounted) {
        Navigator.of(context).pop();
        _showToast('登录失败，请重试');
      }
    }
  }



  void _showLoadingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFF7700)),
                  ),
                  SizedBox(height: 16),
                  Text(
                    '登录中...',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF333333),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Center(
        child: Container(
          width: 340,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          decoration: BoxDecoration(
            color: const Color(0xFFF8F8F8),
            borderRadius: BorderRadius.circular(5),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              LoginDialogTitleBar(
                onClose: widget.onClose,
                onBack: widget.onClose,
              ),
              // 内容区域
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 手机号提示
                      _buildPhoneTip(),
                      const SizedBox(height: 16),
                      // 验证码输入框
                      _buildVerifyCodeInput(),
                      const SizedBox(height: 20),
                      // 底部链接
                      _buildBottomLinks(),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }



  Widget _buildPhoneTip() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '请输入验证码',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '验证码已发送至 ${widget.phoneNumber}',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF999999),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVerifyCodeInput() {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          // 验证码输入框
          Expanded(
            child: TextField(
              controller: _codeController,
              keyboardType: TextInputType.number,
              maxLength: 6,
              style: const TextStyle(
                fontSize: 15,
                color: Color(0xFF333333),
              ),
              decoration: const InputDecoration(
                hintText: '请输入验证码',
                hintStyle: TextStyle(
                  fontSize: 15,
                  color: Color(0xFFCCCCCC),
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 19, vertical: 10),
                counterText: '',
              ),
              onChanged: (value) {
                // 当验证码输入6位时自动登录，参考VerifyCodeView
                if (value.length == 6) {
                  _loginWithCode();
                }
              },
            ),
          ),
          // 发送验证码按钮
          Container(
            margin: const EdgeInsets.only(right: 10),
            child: SizedBox(
              width: 70,
              height: 28,
              child: ElevatedButton(
                onPressed: _canSendCode ? _sendCode : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _canSendCode 
                      ? const Color(0xFFFF7700)
                      : const Color(0xFFCCCCCC),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                  elevation: 0,
                  padding: EdgeInsets.zero,
                ),
                child: _countdown > 0
                    ? Text(
                        '${_countdown}s',
                        style: const TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                      )
                    : const Text(
                        '重发',
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }




  Widget _buildBottomLinks() {
    return Row(
      children: [
        // 找回账号/密码
        GestureDetector(
          onTap: widget.onForgetPassword,
          child: const Text(
            '找回账号/密码',
            style: TextStyle(
              fontSize: 13,
              color: Color(0xFF333333),
            ),
          ),
        ),
        const Spacer(),
        // 微信登录
        GestureDetector(
          onTap: widget.onWechatLogin,
          child: const Text(
            '微信登录',
            style: TextStyle(
              fontSize: 13,
              color: Color(0xFF333333),
            ),
          ),
        ),
        const SizedBox(width: 16),
        // 账号密码登录
        GestureDetector(
          onTap: widget.onAccountLogin,
          child: const Text(
            '账号密码登录',
            style: TextStyle(
              fontSize: 13,
              color: Color(0xFF333333),
            ),
          ),
        ),
      ],
    );
  }


} 