import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/login_provider.dart';
import 'login_dialog_phone.dart';
import 'login_dialog_history_account.dart';
import 'login_dialog_verify_code.dart';
import 'login_dialog_account.dart';
import 'login_dialog_wechat.dart';

enum LoginPageType {
  historyAccount,
  phone,
  verifyCode,
  account,
  wechat,
}

class LoginDialogContainer extends StatefulWidget {
  final String? initialPhoneNumber;
  final VoidCallback? onLoginSuccess;
  final VoidCallback? onForgetPassword;
  final VoidCallback? onClose;

  const LoginDialogContainer({
    super.key,
    this.initialPhoneNumber,
    this.onLoginSuccess,
    this.onForgetPassword,
    this.onClose,
  });

  @override
  State<LoginDialogContainer> createState() => _LoginDialogContainerState();
}

class _LoginDialogContainerState extends State<LoginDialogContainer> {
  LoginPageType _currentPage = LoginPageType.historyAccount;
  String? _phoneNumber;
  bool _isInitialized = false;
  List<LoginPageType> _pageHistory = [];

  @override
  void initState() {
    super.initState();
    _phoneNumber = widget.initialPhoneNumber;
    _initializeLoginFlow();
  }

  void _initializeLoginFlow() async {
    final loginProvider = Provider.of<LoginProvider>(context, listen: false);
    
    // 确保Provider已初始化
    if (!loginProvider.isInitialized) {
      await loginProvider.initialize();
    }
    
    // 检查是否有历史账号
    if (loginProvider.userList.isNotEmpty) {
      setState(() {
        _currentPage = LoginPageType.historyAccount;
        _pageHistory = []; // 初始页面不记录历史
        _isInitialized = true;
      });
    } else {
      setState(() {
        _currentPage = LoginPageType.phone;
        _pageHistory = []; // 初始页面不记录历史
        _isInitialized = true;
      });
    }
  }

  void _switchToPhoneLogin() {
    setState(() {
      _pageHistory.add(_currentPage);
      _currentPage = LoginPageType.phone;
    });
  }

  void _switchToHistoryAccount() {
    setState(() {
      _pageHistory.add(_currentPage);
      _currentPage = LoginPageType.historyAccount;
    });
  }

  void _switchToVerifyCode(String phoneNumber) {
    setState(() {
      _pageHistory.add(_currentPage);
      _currentPage = LoginPageType.verifyCode;
      _phoneNumber = phoneNumber;
    });
  }

  void _switchToAccountLogin() {
    setState(() {
      _pageHistory.add(_currentPage);
      _currentPage = LoginPageType.account;
    });
  }

  void _switchToWechatLogin() {
    setState(() {
      _pageHistory.add(_currentPage);
      _currentPage = LoginPageType.wechat;
    });
  }

  void _goBack() {
    if (_pageHistory.isNotEmpty) {
      setState(() {
        _currentPage = _pageHistory.removeLast();
      });
    } else {
      // 如果没有历史记录，直接关闭
      _handleClose();
    }
  }

  void _handleLoginSuccess() {
    widget.onLoginSuccess?.call();
    Navigator.of(context).pop();
  }

  void _handleClose() {
    widget.onClose?.call();
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Dialog(
        backgroundColor: Colors.transparent,
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    switch (_currentPage) {
      case LoginPageType.historyAccount:
        return Consumer<LoginProvider>(
          builder: (context, loginProvider, child) {
            return LoginDialogHistoryAccount(
              selectedUser: loginProvider.selectedUser,
              userList: loginProvider.userList,
              onLogin: _handleLoginSuccess,
              onForgetPassword: widget.onForgetPassword,
              onOtherLogin: _switchToPhoneLogin,
              onWechatLogin: _switchToWechatLogin,

              onClose: _goBack,
              onUserSelected: (user) {
                // 用户选择处理
              },
              onUserDeleted: (user) {
                // 用户删除处理
              },
            );
          },
        );

      case LoginPageType.phone:
        return LoginDialogPhone(
          phoneNumber: _phoneNumber,
          onGetVerifyCode: (String phoneNumber) {
            // 获取验证码后跳转到验证码页面
            _switchToVerifyCode(phoneNumber);
          },
          onForgetPassword: widget.onForgetPassword,
          onAccountLogin: _switchToAccountLogin,
          onWechatLogin: _switchToWechatLogin,
          onClose: _goBack,
        );

      case LoginPageType.verifyCode:
        return LoginDialogVerifyCode(
          phoneNumber: _phoneNumber ?? '',
          onClose: _goBack,
          onSendCode: () async {
            // 重新发送验证码
            final loginProvider = Provider.of<LoginProvider>(context, listen: false);
            final success = await loginProvider.resendVerifyCode();
            if (success) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('验证码已重新发送'),
                  duration: Duration(seconds: 2),
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(loginProvider.errorMessage),
                  duration: const Duration(seconds: 2),
                ),
              );
            }
          },
          onLoginWithCode: (phone, code) async {
            // 验证码登录成功
            _handleLoginSuccess();
          },
          onForgetPassword: widget.onForgetPassword,
          onAccountLogin: _switchToAccountLogin,
          onWechatLogin: _switchToWechatLogin,
        );

      case LoginPageType.account:
        return LoginDialogAccount(
          onBackToPhone: _goBack,
          onForgetPassword: widget.onForgetPassword,
          onWechatLogin: _switchToWechatLogin,
          onClose: _goBack,
        );

      case LoginPageType.wechat:
        return LoginDialogWechat(
          onClose: _goBack,
          onBackToPhone: _goBack,
          onForgetPassword: widget.onForgetPassword,
        );
    }
  }
} 