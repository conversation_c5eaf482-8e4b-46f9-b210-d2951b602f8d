import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/login_provider.dart';
import '../../services/agreement_service.dart';

/// 可复用的手机号输入区域组件
/// 包含手机号输入框、获取验证码按钮和协议同意区域
class LoginPhoneInputSection extends StatefulWidget {
  final String? initialPhoneNumber;
  final Function(String phoneNumber)? onGetVerifyCode;
  final VoidCallback? onUserAgreement;
  final VoidCallback? onPrivacyPolicy;
  final bool showAgreement;

  const LoginPhoneInputSection({
    super.key,
    this.initialPhoneNumber,
    this.onGetVerifyCode,
    this.onUserAgreement,
    this.onPrivacyPolicy,
    this.showAgreement = true,
  });

  @override
  State<LoginPhoneInputSection> createState() => _LoginPhoneInputSectionState();
}

class _LoginPhoneInputSectionState extends State<LoginPhoneInputSection> {
  bool _isAgreed = false;
  final TextEditingController _phoneController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _phoneController.text = widget.initialPhoneNumber ?? '';
  }

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  void _sendCode() async {
    final phone = _phoneController.text.trim();
    if (phone.isEmpty || phone.length != 11) {
      _showToast('请输入正确的手机号');
      return;
    }
    
    // 检查是否同意协议
    if (widget.showAgreement && !_isAgreed) {
      _showToast('请先同意用户协议和隐私政策');
      return;
    }
    
    // 获取登录提供者
    final loginProvider = Provider.of<LoginProvider>(context, listen: false);
    
    // 发送验证码
    final success = await loginProvider.sendVerifyCode(phone);
    
    if (success) {
      _showToast('验证码已发送');
      // 通过回调通知父组件
      if (widget.onGetVerifyCode != null) {
        widget.onGetVerifyCode!.call(phone);
      }
    } else {
      _showToast(loginProvider.errorMessage);
    }
  }

  void _showToast(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 手机号输入框
        _buildPhoneInput(),
        const SizedBox(height: 16),
        // 获取验证码按钮
        _buildGetVerifyCodeButton(),
        if (widget.showAgreement) ...[
          const SizedBox(height: 13), // 与原来手机号登录页面保持一致
          // 协议同意
          _buildPrivacyAgreement(),
        ],
      ],
    );
  }

  Widget _buildPhoneInput() {
    return Container(
      height: 40,
      margin: const EdgeInsets.only(top: 17),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          // +86
          Container(
            width: 60,
            height: 40,
            padding: const EdgeInsets.only(left: 21),
            child: const Center(
              child: Text(
                '+86',
                style: TextStyle(
                  fontSize: 15,
                  color: Color(0xFF333333),
                ),
              ),
            ),
          ),
          // 分隔线
          Container(
            width: 0.5,
            height: 23,
            margin: const EdgeInsets.only(left: 20, top: 8.5, bottom: 8.5),
            color: const Color(0xFFCCCCCC),
          ),
          // 手机号输入框
          Expanded(
            child: TextField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              maxLength: 11,
              style: const TextStyle(
                fontSize: 15,
                color: Color(0xFF333333),
              ),
              decoration: const InputDecoration(
                hintText: '请输入手机号',
                hintStyle: TextStyle(
                  fontSize: 15,
                  color: Color(0xFFCCCCCC),
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 19, vertical: 10),
                counterText: '',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGetVerifyCodeButton() {
    return SizedBox(
      width: double.infinity,
      height: 38,
      child: ElevatedButton(
        onPressed: _sendCode,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFF7700),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
          elevation: 0,
        ),
        child: const Text(
          '获取验证码',
          style: TextStyle(
            fontSize: 17,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildPrivacyAgreement() {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              _isAgreed = !_isAgreed;
            });
          },
          child: Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              border: Border.all(
                color: _isAgreed ? const Color(0xFFFF7700) : const Color(0xFFCCCCCC),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(2),
              color: _isAgreed ? const Color(0xFFFF7700) : Colors.transparent,
            ),
            child: _isAgreed
                ? const Icon(
                    Icons.check,
                    size: 12,
                    color: Colors.white,
                  )
                : null,
          ),
        ),
        const SizedBox(width: 5),
        Expanded(
          child: Row(
            children: [
              const Text(
                '已阅读并同意',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF999999),
                ),
              ),
              GestureDetector(
                onTap: () {
                  if (widget.onUserAgreement != null) {
                    widget.onUserAgreement!();
                  } else {
                    AgreementService().showUserProtocol(context);
                  }
                },
                child: const Text(
                  '《用户协议》',
                  style: TextStyle(
                    fontSize: 11,
                    color: Color(0xFFFF7700),
                  ),
                ),
              ),
              const Text(
                '及',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF999999),
                ),
              ),
              GestureDetector(
                onTap: () {
                  if (widget.onPrivacyPolicy != null) {
                    widget.onPrivacyPolicy!();
                  } else {
                    AgreementService().showPrivacyPolicy(context);
                  }
                },
                child: const Text(
                  '《隐私政策》',
                  style: TextStyle(
                    fontSize: 11,
                    color: Color(0xFFFF7700),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
} 