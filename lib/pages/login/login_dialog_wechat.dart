import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/wechat_login_service.dart';
import '../../providers/login_provider.dart';
import '../../manager/channel_manager.dart';
import 'login_dialog_title_bar.dart';
import 'login_phone_input_section.dart';

class LoginDialogWechat extends StatefulWidget {
  final VoidCallback? onClose;
  final VoidCallback? onBackToPhone;
  final VoidCallback? onForgetPassword;
  final VoidCallback? onUserAgreement;
  final VoidCallback? onPrivacyPolicy;

  const LoginDialogWechat({
    super.key,
    this.onClose,
    this.onBackToPhone,
    this.onForgetPassword,
    this.onUserAgreement,
    this.onPrivacyPolicy,
  });

  @override
  State<LoginDialogWechat> createState() => _LoginDialogWechatState();
}

class _LoginDialogWechatState extends State<LoginDialogWechat> {
  bool _isLoading = false;
  final WechatLoginService _wechatService = WechatLoginService();

  @override
  void initState() {
    super.initState();
    // 初始化微信SDK
    _initializeWechat();
  }

  Future<void> _initializeWechat() async {
    try {
      await _wechatService.initialize();
      debugPrint('微信SDK初始化完成');
    } catch (e) {
      debugPrint('微信SDK初始化失败: $e');
    }
  }

  void _wechatLogin() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 检查微信是否安装
      bool isInstalled = await _wechatService.isWechatInstalled();
      if (!isInstalled) {
        setState(() {
          _isLoading = false;
        });
        _showToast('请先安装微信应用');
        return;
      }

      final result = await _wechatService.login(
        callback: (response) {
          if (mounted) {
            setState(() {
              _isLoading = false;
            });

            if (response['success']) {
              _showToast('微信登录成功');
              // 这里可以处理登录成功的逻辑，比如保存用户信息等
              // 登录成功后关闭对话框
              Navigator.of(context).pop();
            } else {
              _showToast(response['message'] ?? '微信登录失败');
            }
          }
        },
      );

      // 处理立即返回的结果（如初始化失败等）
      if (!result['success']) {
        setState(() {
          _isLoading = false;
        });
        _showToast(result['message'] ?? '微信登录失败');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showToast('微信登录失败，请重试');
      }
    }
  }

  void _fastLogin() async {
    final loginProvider = Provider.of<LoginProvider>(context, listen: false);
    
    try {
      await loginProvider.fastLogin();
      if (mounted) {
        _showToast('闪验登录成功');
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        if (e is! FastLoginCancelException) {
          _showToast(loginProvider.errorMessage.isNotEmpty 
              ? loginProvider.errorMessage 
              : '闪验登录失败，请重试');
        }
      }
    }
  }

  @override
  void dispose() {
    _wechatService.dispose();
    super.dispose();
  }

  void _showToast(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Center(
        child: Container(
          width: 340, // 固定宽度340dp，与Android布局一致
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          decoration: BoxDecoration(
            color: const Color(0xFFF8F8F8), // 使用灰色背景，与Android一致
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              LoginDialogTitleBar(
                onClose: widget.onClose,
                onBack: widget.onBackToPhone,
                showBackButton: widget.onBackToPhone != null,
                showCloseButton: true,
              ),
              // 内容区域
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 复用手机号输入区域组件
                      LoginPhoneInputSection(
                        onGetVerifyCode: (phoneNumber) {
                          // 处理获取验证码的回调
                          _showToast('验证码已发送到 $phoneNumber');
                        },
                        onUserAgreement: widget.onUserAgreement,
                        onPrivacyPolicy: widget.onPrivacyPolicy,
                      ),
                      const SizedBox(height: 14), 
                      // 账号密码登录
                      _buildAccountLogin(),
                      const SizedBox(height: 18), 
                      // 其他登录方式分割线
                      _buildOtherLoginDivider(),
                      const SizedBox(height: 15), 
                      // 登录方式图标行
                      _buildLoginMethodsRow(),
                      const SizedBox(height: 18),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }





  Widget _buildAccountLogin() {
    return GestureDetector(
      onTap: widget.onForgetPassword,
      child: Container(
        width: double.infinity,
        alignment: Alignment.center,
        child: const Text(
          '账号密码登录',
          style: TextStyle(
            fontSize: 14, // 与Android的textSize="14dp"一致
            color: Color(0xFF3D3D3D), // 与Android的textColor="#3D3D3D"一致
          ),
        ),
      ),
    );
  }

  Widget _buildOtherLoginDivider() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20), // 与Android布局的marginLeft和marginRight="20dp"一致
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 1,
              color: const Color(0xFFCECECE), // 与Android的background="#CECECE"一致
            ),
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 15), // 与Android布局的marginStart和marginEnd="15dp"一致
            child: const Text(
              '其他方式登录',
              style: TextStyle(
                fontSize: 11, // 与Android的textSize="11dp"一致
                color: Color(0xFF999999), // 与Android的textColor="#999999"一致
              ),
            ),
          ),
          Expanded(
            child: Container(
              height: 1,
              color: const Color(0xFFCECECE),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoginMethodsRow() {
    return Consumer<LoginProvider>(
      builder: (context, loginProvider, child) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // 闪验登录按钮
            _buildFastLoginButton(loginProvider),
            // 微信登录按钮
            _buildWechatIcon(),
          ],
        );
      },
    );
  }

  Widget _buildFastLoginButton(LoginProvider loginProvider) {
    bool isLoadingFast = loginProvider.isShowingFastLogin || loginProvider.isLoading;
    
    return GestureDetector(
      onTap: isLoadingFast ? null : _fastLogin,
      child: Container(
        width: 56,
        height: 56,
        decoration: BoxDecoration(
          color: isLoadingFast ? const Color(0xFF999999) : const Color(0xFF1890FF),
          borderRadius: BorderRadius.circular(28),
        ),
        child: isLoadingFast
            ? const Center(
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                ),
              )
            : const Icon(
                Icons.flash_on,
                color: Colors.white,
                size: 32,
              ),
      ),
    );
  }

  Widget _buildWechatIcon() {
    return GestureDetector(
      onTap: _isLoading ? null : _wechatLogin,
      child: Container(
        width: 56, // 与Android布局的width="56dp"一致
        height: 56, // 与Android布局的height="56dp"一致
        decoration: BoxDecoration(
          color: _isLoading ? const Color(0xFF9FC87F) : const Color(0xFF07C160),
          borderRadius: BorderRadius.circular(28),
        ),
        child: _isLoading
            ? const Center(
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                ),
              )
            : Image.asset(
                'assets/images/sysq_ic_wechat_login.png',
                width: 32,
                height: 32,
                errorBuilder: (context, error, stackTrace) {
                  return const Icon(
                    Icons.chat,
                    color: Colors.white,
                    size: 32,
                  );
                },
              ),
      ),
    );
  }
}