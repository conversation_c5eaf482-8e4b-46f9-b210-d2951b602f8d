import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../providers/login_provider.dart';

class PhoneLoginForm extends StatefulWidget {
  final VoidCallback? onSwitchToAccount;
  final Function(String)? onSendCode;
  final VoidCallback? onLoginSuccess;

  const PhoneLoginForm({
    super.key,
    this.onSwitchToAccount,
    this.onSendCode,
    this.onLoginSuccess,
  });

  @override
  State<PhoneLoginForm> createState() => _PhoneLoginFormState();
}

class _PhoneLoginFormState extends State<PhoneLoginForm> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _verificationCodeController = TextEditingController();
  bool _agreedToTerms = false;
  int _countdown = 0;
  bool _isCountingDown = false;

  @override
  Widget build(BuildContext context) {
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    
    return Stack(
      children: [
        // 主要内容区域 - 可滚动，底部留出空间给固定区域
        SingleChildScrollView(
          // 为底部固定区域预留空间
          padding: const EdgeInsets.only(bottom: 180), // 根据底部区域高度调整
          child: Column(
            children: [
              // 标题
              Container(
                padding: const EdgeInsets.fromLTRB(30, 20, 30, 0),
                alignment: Alignment.centerLeft,
                child: const Text(
                  '手机号登录',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF333333),
                  ),
                ),
              ),
              
              // 登录表单
              Container(
                padding: const EdgeInsets.fromLTRB(30, 20, 30, 0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      // 手机号输入框
                      Container(
                        height: 50,
                        decoration: BoxDecoration(
                          color: const Color(0xFFF5F5F5),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 15),
                              child: const Text(
                                '+86',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Color(0xFF333333),
                                ),
                              ),
                            ),
                            Container(
                              width: 1,
                              height: 20,
                              color: const Color(0xFFE0E0E0),
                            ),
                            Expanded(
                              child: TextFormField(
                                controller: _phoneController,
                                keyboardType: TextInputType.phone,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                  LengthLimitingTextInputFormatter(11),
                                ],
                                decoration: const InputDecoration(
                                  hintText: '请输入手机号',
                                  hintStyle: TextStyle(
                                    fontSize: 16,
                                    color: Color(0xFFCCCCCC),
                                  ),
                                  border: InputBorder.none,
                                  contentPadding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                                ),
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Color(0xFF333333),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return '请输入手机号';
                                  }
                                  if (value.length != 11) {
                                    return '请输入11位手机号';
                                  }
                                  if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value)) {
                                    return '请输入有效的手机号';
                                  }
                                  return null;
                                },
                                onChanged: (value) {
                                  setState(() {});
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // 验证码输入框
                      Container(
                        height: 50,
                        decoration: BoxDecoration(
                          color: const Color(0xFFF5F5F5),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: _verificationCodeController,
                                keyboardType: TextInputType.number,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                  LengthLimitingTextInputFormatter(6),
                                ],
                                decoration: const InputDecoration(
                                  hintText: '请输入验证码',
                                  hintStyle: TextStyle(
                                    fontSize: 16,
                                    color: Color(0xFFCCCCCC),
                                  ),
                                  border: InputBorder.none,
                                  contentPadding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                                ),
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Color(0xFF333333),
                                ),
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return '请输入验证码';
                                  }
                                  if (value.length < 4) {
                                    return '请输入正确的验证码';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            Container(
                              height: 30,
                              child: TextButton(
                                onPressed: _canSendCode() ? _handleSendCode : null,
                                style: TextButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(horizontal: 15),
                                  minimumSize: Size.zero,
                                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                ),
                                child: Text(
                                  _isCountingDown ? '${_countdown}s' : '获取验证码',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: _canSendCode() ? const Color(0xFF6B73FF) : const Color(0xFFCCCCCC),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // 登录按钮
                      Consumer<LoginStateProvider>(
                        builder: (context, loginProvider, child) {
                          final bool isButtonEnabled = _agreedToTerms;
                          return Container(
                            width: double.infinity,
                            height: 50,
                            decoration: BoxDecoration(
                              color: isButtonEnabled ? const Color(0xFF4571FB) : const Color(0x994571FB),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: ElevatedButton(
                              onPressed: isButtonEnabled ? _handleLogin : null,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.transparent,
                                foregroundColor: Colors.white,
                                shadowColor: Colors.transparent,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                elevation: 0,
                              ),
                              child: loginProvider.isLoading
                                  ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                      ),
                                    )
                                  : const Text(
                                      '登录',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.white,
                                      ),
                                    ),
                            ),
                          );
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // 手机账号密码登录按钮
                      Align(
                        alignment: Alignment.centerLeft,
                        child: GestureDetector(
                          onTap: widget.onSwitchToAccount,
                          child: const Text(
                            '手机账号密码登录',
                            style: TextStyle(
                              fontSize: 14,
                              color: Color(0xFF606266),
                            ),
                          ),
                        ),
                      ),
                      
                      // 错误信息显示
                      Consumer<LoginStateProvider>(
                        builder: (context, loginProvider, child) {
                          if (loginProvider.errorMessage != null) {
                            return Container(
                              padding: const EdgeInsets.all(12),
                              margin: const EdgeInsets.only(top: 20),
                              decoration: BoxDecoration(
                                color: Colors.red[50],
                                border: Border.all(color: Colors.red[200]!),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  Icon(Icons.error_outline, color: Colors.red[600], size: 20),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      loginProvider.errorMessage!,
                                      style: TextStyle(color: Colors.red[600], fontSize: 14),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        
        // 底部固定区域 - 始终固定在屏幕底部
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          child: Container(
            padding: EdgeInsets.only(
              left: 30,
              right: 30,
              top: 20,
              bottom: bottomPadding > 0 ? bottomPadding + 10 : 30,
            ),
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 底部登录选项
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // 微信登录
                    GestureDetector(
                      onTap: _handleWechatLogin,
                      child: Column(
                        children: [
                          Container(
                            width: 60,
                            height: 60,
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                            ),
                            child: ClipOval(
                              child: Image.asset(
                                'assets/images/login_wechat.png',
                                width: 60,
                                height: 60,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            '微信登录',
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF666666),
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // 账号密码登录
                    GestureDetector(
                      onTap: widget.onSwitchToAccount,
                      child: Column(
                        children: [
                          Container(
                            width: 60,
                            height: 60,
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                            ),
                            child: ClipOval(
                              child: Image.asset(
                                'assets/images/login_account.png',
                                width: 60,
                                height: 60,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            '账号密码登录',
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF666666),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                // 用户协议
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _agreedToTerms = !_agreedToTerms;
                        });
                      },
                      child: Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: _agreedToTerms
                                ? const Color(0xFF6B73FF)
                                : const Color(0xFFCCCCCC),
                            width: 1,
                          ),
                          color: _agreedToTerms
                              ? const Color(0xFF6B73FF)
                              : Colors.transparent,
                        ),
                        child: _agreedToTerms
                            ? const Icon(
                                Icons.check,
                                size: 12,
                                color: Colors.white,
                              )
                            : null,
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      '已阅读并同意',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF999999),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        // 打开用户协议
                      },
                      child: const Text(
                        '《用户协议》',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF6B73FF),
                        ),
                      ),
                    ),
                    const Text(
                      '及',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF999999),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        // 打开隐私政策
                      },
                      child: const Text(
                        '《隐私政策》',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF6B73FF),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  bool _canSendCode() {
    return !_isCountingDown && _phoneController.text.length == 11;
  }

  void _startCountdown() {
    setState(() {
      _isCountingDown = true;
      _countdown = 60;
    });
    
    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 1));
      if (mounted) {
        setState(() {
          _countdown--;
        });
        if (_countdown <= 0) {
          setState(() {
            _isCountingDown = false;
          });
          return false;
        }
        return true;
      }
      return false;
    });
  }

  Future<void> _handleSendCode() async {
    if (!_canSendCode()) return;
    
    final phoneNumber = _phoneController.text.trim();
    if (phoneNumber.length != 11) {
      return;
    }

    final loginProvider = Provider.of<LoginStateProvider>(context, listen: false);
    final success = await loginProvider.sendVerificationCode(phoneNumber);

    if (success) {
      _startCountdown();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('验证码已发送'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;
    if (!_agreedToTerms) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请同意用户协议和隐私政策'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    final loginProvider = Provider.of<LoginStateProvider>(context, listen: false);
    final phoneNumber = _phoneController.text.trim();
    final verificationCode = _verificationCodeController.text.trim();
    
    final success = await loginProvider.verifyPhoneAndLogin(
      phoneNumber: phoneNumber,
      verificationCode: verificationCode,
      context: context,
    );

    if (success) {
      widget.onLoginSuccess?.call();
    }
  }

  Future<void> _handleWechatLogin() async {
    final loginProvider = Provider.of<LoginStateProvider>(context, listen: false);

    final success = await loginProvider.performWechatLogin(
      context: context,
    );

    if (success) {
      widget.onLoginSuccess?.call();
    }
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _verificationCodeController.dispose();
    super.dispose();
  }
}