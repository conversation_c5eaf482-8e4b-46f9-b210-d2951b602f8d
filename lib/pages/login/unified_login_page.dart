import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/login_provider.dart';
import '../../services/app_route_manager.dart';
import 'widgets/account_login_form.dart';
import 'widgets/phone_login_form.dart';

/// 统一登录页面 - 合并原LoginPage和LoginManager功能
class UnifiedLoginPage extends StatefulWidget {
  final VoidCallback? onLoginSuccess;
  final bool directPop;

  const UnifiedLoginPage({
    super.key,
    this.onLoginSuccess,
    this.directPop = false,
  });

  @override
  State<UnifiedLoginPage> createState() => _UnifiedLoginPageState();
}

class _UnifiedLoginPageState extends State<UnifiedLoginPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    // 设置状态栏样式
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
    );
    
    // 初始化动画
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    // 初始化登录状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final loginProvider = Provider.of<LoginStateProvider>(context, listen: false);
      loginProvider.initializeLoginFlow(context);
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, dynamic result) {
        if (!didPop) {
          _handleBackPressed();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        resizeToAvoidBottomInset: false, // 防止键盘顶起底部内容
        body: SafeArea(
          // 登录页需要头图铺满到状态栏下方，因此关闭顶部安全区；
          // 仍保留底部安全区以避免 Home 指示条遮挡。
          top: false,
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Consumer<LoginStateProvider>(
                    builder: (context, loginProvider, child) {
                      return Container(
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Color(0xFF6B73FF),
                              Color(0xFF9DD5FF),
                            ],
                          ),
                        ),
                        child: Stack(
                          children: [
                            Column(
                              children: [
                                _buildWelcomeArea(loginProvider.currentState),
                                Expanded(
                                  child: Container(
                                    decoration: const BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(16),
                                        topRight: Radius.circular(16),
                                      ),
                                    ),
                                    child: _buildContent(loginProvider),
                                  ),
                                ),
                              ],
                            ),
                            if (loginProvider.isFastLoginInProgress)
                              Positioned.fill(
                                child: IgnorePointer(
                                  child: Container(color: Colors.white),
                                ),
                              ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildContent(LoginStateProvider loginProvider) {
    // 根据登录状态显示不同内容
    switch (loginProvider.currentState) {
      case LoginState.loading:
        return _buildLoadingView();
      
      case LoginState.accountLogin:
        return AccountLoginForm(
          onSwitchToPhone: () => loginProvider.switchToPhoneLogin(),
          onLoginSuccess: () => _handleLoginSuccess(),
          onForgetPassword: () => loginProvider.switchToPhoneLogin(),
        );
      
      case LoginState.phoneLogin:
        return PhoneLoginForm(
          onSwitchToAccount: () => loginProvider.switchToAccountLogin(),
          onSendCode: (phone) => loginProvider.switchToVerifyCode(phone),
          onLoginSuccess: () => _handleLoginSuccess(),
        );
      
      case LoginState.verifyCode:
        return PhoneLoginForm(
          onSwitchToAccount: () => loginProvider.switchToAccountLogin(),
          onSendCode: (phone) => loginProvider.switchToVerifyCode(phone),
          onLoginSuccess: () => _handleLoginSuccess(),
        );
      
      case LoginState.wechatLogin:
        return _buildWechatLoginView();
      
      case LoginState.error:
        return _buildErrorView(loginProvider.errorMessage);
      default:
        return _buildLoadingView();
    }
  }

  Widget _buildWelcomeArea(LoginState state) {
    switch (state) {
      case LoginState.accountLogin:
        return _welcomeAreaWithBanner(
          title: 'Hello!',
          subtitle: '欢迎来到斗罗宇宙',
        );
      case LoginState.phoneLogin:
        return _welcomeAreaWithBanner(
          title: 'Hello!',
          subtitle: '欢迎来到斗罗宇宙',
        );
      case LoginState.verifyCode:
        return _welcomeArea(
          title: '验证身份',
          subtitle: '请输入验证码完成登录',
          icon: Icons.security,
          height: 240,
        );
      default:
        return const SizedBox(height: 56);
    }
  }

  Widget _welcomeAreaWithBanner({
    required String title,
    required String subtitle,
  }) {
    return Container(
      width: double.infinity,
      child: GestureDetector(
        onTap: () {
          Navigator.pushNamed(context, '/proxy-config');
        },
        child: Image.asset(
          'assets/images/login_top_banner.png',
          fit: BoxFit.fitWidth, // 宽度撑满，高度按比例缩放
          alignment: Alignment.topCenter,
        ),
      ),
    );
  }

  Widget _welcomeArea({
    required String title,
    required String subtitle,
    required IconData icon,
    double height = 300,
  }) {
    return Container(
      height: height,
      padding: const EdgeInsets.symmetric(horizontal: 30),
      child: Stack(
        children: [
          Positioned(
            left: 0,
            top: height == 240 ? 20 : 60,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            right: -20,
            top: height == 240 ? 0 : 30,
            child: SizedBox(
              width: height == 240 ? 180 : 200,
              height: height == 240 ? 220 : 250,
              child: Icon(
                icon,
                size: height == 240 ? 80 : 100,
                color: Colors.white70,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingView() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.blue[50]!,
            Colors.white,
          ],
        ),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 20),
            Text(
              '正在初始化登录...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWechatLoginView() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.green[50]!,
            Colors.white,
          ],
        ),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.wechat,
              size: 80,
              color: Colors.green,
            ),
            SizedBox(height: 20),
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
            ),
            SizedBox(height: 20),
            Text(
              '正在进行微信登录...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 10),
            Text(
              '请在微信中确认登录',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView(String? errorMessage) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: Colors.red[400],
            ),
            const SizedBox(height: 20),
            Text(
              '登录遇到问题',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.red[600],
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red[200]!),
              ),
              child: Text(
                errorMessage ?? '登录过程中出现错误',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.red[600],
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 30),
            SizedBox(
              width: double.infinity,
              child: Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF4571FB),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: ElevatedButton.icon(
                  onPressed: () {
                    final loginProvider = Provider.of<LoginStateProvider>(context, listen: false);
                    loginProvider.initializeLoginFlow(context);
                  },
                  icon: const Icon(Icons.refresh),
                  label: const Text('重试'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    foregroundColor: Colors.white,
                    shadowColor: Colors.transparent,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    elevation: 0,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }


  void _handleLoginSuccess() {
    // 直接跳转，不延迟
    if (mounted) {
      widget.onLoginSuccess?.call();
      
      // 如果需要直接返回，则pop并传递成功结果
      if (widget.directPop) {
        Navigator.of(context).pop(true);
      } else {
        AppRouteManager.navigateAfterLogin(context);
      }
    }
  }


  void _handleBackPressed() {
    final loginProvider = Provider.of<LoginStateProvider>(context, listen: false);
    // 交给 Provider 的历史栈处理返回；未消费则弹退出
    final handled = loginProvider.back();
    if (!handled) {
      _showExitConfirmation();
    }
  }

  void _showExitConfirmation() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.help_outline, color: Colors.orange[600]),
              const SizedBox(width: 8),
              const Text('提示'),
            ],
          ),
          content: const Text('您还未登录，请选择继续登录或退出应用'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                '继续登录',
                style: TextStyle(color: Theme.of(context).primaryColor),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                SystemNavigator.pop();
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('退出应用'),
            ),
          ],
        );
      },
    );
  }
}