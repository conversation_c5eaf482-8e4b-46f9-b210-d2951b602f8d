import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/login_provider.dart';
import 'login_dialog_title_bar.dart';
import '../../services/agreement_service.dart';

class LoginDialogAccount extends StatefulWidget {
  final VoidCallback? onBackToPhone;
  final VoidCallback? onForgetPassword;
  final VoidCallback? onWechatLogin;

  final VoidCallback? onClose;

  const LoginDialogAccount({
    super.key,
    this.onBackToPhone,
    this.onForgetPassword,
    this.onWechatLogin,

    this.onClose,
  });

  @override
  State<LoginDialogAccount> createState() => _LoginDialogAccountState();
}

class _LoginDialogAccountState extends State<LoginDialogAccount> {
  bool _isAgreed = false;
  bool _isPasswordVisible = false;
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _login() async {
    final username = _usernameController.text.trim();
    final password = _passwordController.text.trim();
    
    if (username.isEmpty) {
      _showToast('请输入用户名');
      return;
    }
    
    if (password.isEmpty) {
      _showToast('请输入密码');
      return;
    }
    
    if (!_isAgreed) {
      _showToast('请先同意用户协议和隐私政策');
      return;
    }

    // 显示加载状态
    _showLoadingDialog();
    
    try {
      final loginProvider = Provider.of<LoginProvider>(context, listen: false);
      final result = await loginProvider.login(
        phoneNumber: username, // 这里使用username作为phoneNumber参数
        password: password,
      );
      
      // 隐藏加载对话框
      if (mounted) {
        Navigator.of(context).pop();
        
        if (result['success']) {
          _showToast('登录成功');
          // 可以在这里添加登录成功后的处理逻辑
        } else {
          _showToast(result['message'] ?? '登录失败');
        }
      }
    } catch (e) {
      // 隐藏加载对话框
      if (mounted) {
        Navigator.of(context).pop();
        _showToast('登录失败，请重试');
      }
    }
  }

  void _showLoadingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  void _showToast(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Center(
        child: Container(
          width: 340,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          decoration: BoxDecoration(
            color: const Color(0xFFF8F8F8),
            borderRadius: BorderRadius.circular(5),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              _buildTitleBar(),
              // 内容区域
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 用户名输入框
                      _buildUsernameInput(),
                      const SizedBox(height: 16),
                      // 密码输入框
                      _buildPasswordInput(),
                      const SizedBox(height: 16),
                      // 登录按钮
                      _buildLoginButton(),
                      const SizedBox(height: 13),
                      // 协议同意
                      _buildPrivacyAgreement(),
                      const SizedBox(height: 20),
                      // 底部链接
                      _buildBottomLinks(),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTitleBar() {
    return LoginDialogTitleBar(
      onClose: widget.onClose,
      onBack: widget.onBackToPhone,
    );
  }

  Widget _buildUsernameInput() {
    return Container(
      height: 40,
      margin: const EdgeInsets.only(top: 17),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          // 用户名图标
          Padding(
            padding: const EdgeInsets.only(left: 21),
            child: Image.asset(
              'assets/images/sysq_ic_account.png',
              width: 20,
              height: 20,
              errorBuilder: (context, error, stackTrace) {
                return Icon(
                  Icons.person,
                  size: 20,
                  color: const Color(0xFFCCCCCC),
                );
              },
            ),
          ),
          // 分隔线
          Container(
            width: 0.5,
            height: 23,
            margin: const EdgeInsets.only(left: 20, top: 8.5, bottom: 8.5),
            color: const Color(0xFFCCCCCC),
          ),
          // 用户名输入框
          Expanded(
            child: TextField(
              controller: _usernameController,
              keyboardType: TextInputType.text,
              style: const TextStyle(
                fontSize: 15,
                color: Color(0xFF333333),
              ),
              decoration: const InputDecoration(
                hintText: '请输入用户名',
                hintStyle: TextStyle(
                  fontSize: 15,
                  color: Color(0xFFCCCCCC),
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 19, vertical: 10),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordInput() {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          // 密码图标
          Padding(
            padding: const EdgeInsets.only(left: 21),
            child: Image.asset(
              'assets/images/sysq_ic_pwd.png',
              width: 20,
              height: 20,
              errorBuilder: (context, error, stackTrace) {
                return Icon(
                  Icons.lock,
                  size: 20,
                  color: const Color(0xFFCCCCCC),
                );
              },
            ),
          ),
          // 分隔线
          Container(
            width: 0.5,
            height: 23,
            margin: const EdgeInsets.only(left: 20, top: 8.5, bottom: 8.5),
            color: const Color(0xFFCCCCCC),
          ),
          // 密码输入框
          Expanded(
            child: TextField(
              controller: _passwordController,
              obscureText: !_isPasswordVisible,
              keyboardType: TextInputType.visiblePassword,
              style: const TextStyle(
                fontSize: 15,
                color: Color(0xFF333333),
              ),
              decoration: InputDecoration(
                hintText: '请输入密码',
                hintStyle: const TextStyle(
                  fontSize: 15,
                  color: Color(0xFFCCCCCC),
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 19, vertical: 10),
                                suffixIcon: GestureDetector(
                  onTap: () {
                    setState(() {
                      _isPasswordVisible = !_isPasswordVisible;
                    });
                  },
                  child: Container(
                    width: 26,
                    height: 26,
                    padding: const EdgeInsets.only(right: 8),
                    child: Image.asset(
                      _isPasswordVisible 
                        ? 'assets/images/sysq_ic_pwd_show.png'
                        : 'assets/images/sysq_ic_pwd_hide.png',
                        width: 26,
                        height: 26,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                            color: const Color(0xFFCCCCCC),
                            size: 26,
                          );
                        },
                    ),
                  ),
                ),
                suffixIconConstraints: const BoxConstraints(
                  minWidth: 26,
                  minHeight: 26,
                  maxWidth: 26,
                  maxHeight: 26,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoginButton() {
    return SizedBox(
      width: double.infinity,
      height: 38,
      child: ElevatedButton(
        onPressed: _login,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFF7700),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
          elevation: 0,
        ),
        child: const Text(
          '登录',
          style: TextStyle(
            fontSize: 17,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildPrivacyAgreement() {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              _isAgreed = !_isAgreed;
            });
          },
          child: Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              border: Border.all(
                color: _isAgreed ? const Color(0xFFFF7700) : const Color(0xFFCCCCCC),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(2),
              color: _isAgreed ? const Color(0xFFFF7700) : Colors.transparent,
            ),
            child: _isAgreed
                ? const Icon(
                    Icons.check,
                    size: 12,
                    color: Colors.white,
                  )
                : null,
          ),
        ),
        const SizedBox(width: 5),
        Expanded(
          child: Row(
            children: [
              const Text(
                '已阅读并同意',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF999999),
                ),
              ),
              GestureDetector(
                onTap: () {
                  AgreementService().showUserProtocol(context);
                },
                child: const Text(
                  '《用户协议》',
                  style: TextStyle(
                    fontSize: 11,
                    color: Color(0xFFFF7700),
                  ),
                ),
              ),
              const Text(
                '及',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF999999),
                ),
              ),
              GestureDetector(
                onTap: () {
                  AgreementService().showPrivacyPolicy(context);
                },
                child: const Text(
                  '《隐私政策》',
                  style: TextStyle(
                    fontSize: 11,
                    color: Color(0xFFFF7700),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBottomLinks() {
    return Row(
      children: [
        // 找回账号/密码
        GestureDetector(
          onTap: widget.onForgetPassword,
          child: const Text(
            '找回账号/密码',
            style: TextStyle(
              fontSize: 13,
              color: Color(0xFF333333),
            ),
          ),
        ),
        const Spacer(),
        // 微信登录
        GestureDetector(
          onTap: widget.onWechatLogin,
          child: const Text(
            '微信登录',
            style: TextStyle(
              fontSize: 13,
              color: Color(0xFF333333),
            ),
          ),
        ),
      ],
    );
  }
} 