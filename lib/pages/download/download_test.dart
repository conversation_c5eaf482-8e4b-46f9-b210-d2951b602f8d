import 'dart:ui';
import 'package:dlyz_flutter/model/download_info.dart';
import 'package:dlyz_flutter/components/bottom_sheet_dialog.dart';
import 'package:dlyz_flutter/providers/download_provider.dart';
import 'package:dlyz_flutter/services/download/ALDownloader.dart';
import 'package:dlyz_flutter/services/download/ALDownloaderStatus.dart';
import 'package:dlyz_flutter/services/download/ALDownloaderTask.dart';
import 'package:flutter/material.dart';

/// 下载管理器测试页面
class DownloadTest extends StatefulWidget {
  const DownloadTest({super.key});

  @override
  State<DownloadTest> createState() => _DownloadTestState();
}

class _DownloadTestState extends State<DownloadTest> {

  List<ALDownloaderTask> tasks = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    updateDownloadTasks();
    return Scaffold(
      appBar: AppBar(
        title: const Text('下载管理器测试'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 测试下载按钮
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                                         const Text(
                       '测试下载（包含AAB文件）',
                       style: TextStyle(
                         fontSize: 18,
                         fontWeight: FontWeight.bold,
                       ),
                     ),
                    const SizedBox(height: 16),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        ElevatedButton(
                          onPressed: () => _testDownload('港台云上城'),
                          child: const Text('下载港台云上城'),
                        ),
                        ElevatedButton(
                          onPressed: () => _testDownload('猎魂世界'),
                          child: const Text('下载猎魂世界'),
                        ),
                        ElevatedButton(
                          onPressed: () => _testDownload('指尖像素城'),
                          child: const Text('下载指尖像素城'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 下载任务列表
            Expanded(
              child: ListView.builder(
                itemCount: tasks.length,
                itemBuilder: (context, index) {
                  final task = tasks[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      title: Text(task.file.fileName),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('状态: ${task.status.name}'),
                          LinearProgressIndicator(
                            value: task.progress,
                            backgroundColor: Colors.grey[300],
                          ),
                          Text('进度: ${(task.progress * 100).toStringAsFixed(1)}%'),
                        ],
                      ),
                      trailing: _buildActionButtons(task),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 测试下载
  Future<void> _testDownload(String type) async {
    String url;
    String fileName;
    
    switch (type) {
      case '港台云上城':
        url = 'https://developer-mt.nofeba.com/media/DiskExtension/android_oversea_pack_output/2025_07_30/1000100_com_tg_ysczg_tw_seasdk_37000000_20250730_095649_1753840621.apk';
        fileName = 'ysc_output.aab';
        break;
      case '猎魂世界':
        url = 'https://dlcs-vlc-gw.37tgy.com/upload/1_1021926_11327/douluodaluliehunshijieguanwang_1110001.apk?file_name=%E6%96%97%E7%BD%97%E5%A4%A7%E9%99%86%EF%BC%9A%E7%8C%8E%E9%AD%82%E4%B8%96%E7%95%8C%EF%BC%88%E5%AE%98%E7%BD%91%EF%BC%89.apk';
        fileName = 'lhsj_output.aab';
        break;
      case '指尖像素城':
        url = 'https://developer-mt.nofeba.com/media/android_mainland_pack_output/2025_06_23/huawei_20250618-dcb8ab4f-8196-4948-84f8-8f964f875134_20250623_1750652824.apk';
        fileName = 'zjxsc_output.apk';
        break;
      default:
        return;
    }
    
    try {
      ALDownloader.download(
          url
      );

      updateDownloadTasks();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('开始下载: $fileName')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('下载失败: $e')),
        );
      }
    }
  }

  Future<void> updateDownloadTasks() async {
    var alTasks = await ALDownloader.tasks;
    alTasks = alTasks.where((task) => task.file.fileName.isNotEmpty).toList();
    setState(() {
      tasks = alTasks;
    });
  }

  /// 构建操作按钮
  Widget _buildActionButtons(ALDownloaderTask task) {
    switch (task.status) {
      case ALDownloaderStatus.downloading:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => ALDownloader.pause(task.url),
              icon: const Icon(Icons.pause),
            ),
            IconButton(
              onPressed: () {
                final DownloadInfo game = DownloadProvider().downloadItems.firstWhere((item) => item.url == task.url);
                DownloadProvider().handleRemove(game);
                updateDownloadTasks();
              },
              icon: const Icon(Icons.cancel),
            ),
          ],
        );
        
      case ALDownloaderStatus.paused:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => ALDownloader.download(task.url),
              icon: const Icon(Icons.play_arrow),
            ),
            IconButton(
              onPressed: () {
                final DownloadInfo game = DownloadProvider().downloadItems.firstWhere((item) => item.url == task.url);
                DownloadProvider().handleRemove(game);
                updateDownloadTasks();
              },
              icon: const Icon(Icons.cancel),
            ),
          ],
        );
        
      case ALDownloaderStatus.succeeded:
        return IconButton(
          onPressed: () {
            final DownloadInfo game = DownloadProvider().downloadItems.firstWhere((item) => item.url == task.url);
            DownloadProvider().handleRemove(game);
            updateDownloadTasks();
          },
          icon: const Icon(Icons.delete),
        );
        
      case ALDownloaderStatus.failed:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => ALDownloader.download(task.url),
              icon: const Icon(Icons.refresh),
            ),
            IconButton(
              onPressed: () {
                final DownloadInfo game = DownloadProvider().downloadItems.firstWhere((item) => item.url == task.url);
                DownloadProvider().handleRemove(game);
                updateDownloadTasks();
              },
              icon: const Icon(Icons.delete),
            ),
          ],
        );
      case ALDownloaderStatus.unstarted:
        return IconButton(
          icon: const Icon(Icons.downloading),
          onPressed: () {},
        );
    }
  }
} 