import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../../net/config/proxy_config.dart';

class ProxyConfigPage extends StatefulWidget {
  const ProxyConfigPage({super.key});

  @override
  State<ProxyConfigPage> createState() => _ProxyConfigPageState();
}

class _ProxyConfigPageState extends State<ProxyConfigPage> {
  bool _isProxyEnabled = false;
  final TextEditingController _ipController = TextEditingController();
  final TextEditingController _portController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  
  final ProxyConfig _proxyConfig = ProxyConfig.getInstance();

  @override
  void initState() {
    super.initState();
    _loadProxyConfig();
  }

  @override
  void dispose() {
    _ipController.dispose();
    _portController.dispose();
    super.dispose();
  }

  void _loadProxyConfig() {
    setState(() {
      _isProxyEnabled = _proxyConfig.isEnabled;
      _ipController.text = _proxyConfig.ip;
      _portController.text = _proxyConfig.port;
    });
  }

  void _saveProxyConfig() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    _proxyConfig.setConfig(
      _isProxyEnabled,
      _ipController.text.trim(),
      _portController.text.trim(),
    );

    Fluttertoast.showToast(
      msg: '代理配置已保存',
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.CENTER,
      timeInSecForIosWeb: 1,
      backgroundColor: Colors.green,
      textColor: Colors.white,
      fontSize: 16.0,
    );
  }

  void _resetProxyConfig() {
    setState(() {
      _isProxyEnabled = false;
      _ipController.clear();
      _portController.clear();
    });

    _proxyConfig.resetConfig();

    Fluttertoast.showToast(
      msg: '代理配置已重置',
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.CENTER,
      timeInSecForIosWeb: 1,
      backgroundColor: Colors.orange,
      textColor: Colors.white,
      fontSize: 16.0,
    );
  }

  String? _validateIp(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '请输入IP地址';
    }
    
    final ip = value.trim();
    final parts = ip.split('.');
    
    if (parts.length != 4) {
      return 'IP地址格式错误';
    }
    
    for (final part in parts) {
      final num = int.tryParse(part);
      if (num == null || num < 0 || num > 255) {
        return 'IP地址格式错误';
      }
    }
    
    return null;
  }

  String? _validatePort(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '请输入端口号';
    }
    
    final port = int.tryParse(value.trim());
    if (port == null || port < 1 || port > 65535) {
      return '端口号必须在1-65535之间';
    }
    
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('代理配置'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '代理设置',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      SwitchListTile(
                        title: const Text('启用代理'),
                        subtitle: Text(_isProxyEnabled 
                          ? '代理已启用 - ${_proxyConfig.getProxyString()}' 
                          : '代理已禁用'),
                        value: _isProxyEnabled,
                        onChanged: (bool value) {
                          setState(() {
                            _isProxyEnabled = value;
                          });
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _ipController,
                        enabled: _isProxyEnabled,
                        decoration: const InputDecoration(
                          labelText: 'IP地址',
                          hintText: '例如: *************',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.computer),
                        ),
                        keyboardType: TextInputType.text,
                        validator: _isProxyEnabled ? _validateIp : null,
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _portController,
                        enabled: _isProxyEnabled,
                        decoration: const InputDecoration(
                          labelText: '端口',
                          hintText: '例如: 8080',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.numbers),
                        ),
                        keyboardType: TextInputType.number,
                        validator: _isProxyEnabled ? _validatePort : null,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _saveProxyConfig,
                      icon: const Icon(Icons.save),
                      label: const Text('保存配置'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _resetProxyConfig,
                      icon: const Icon(Icons.refresh),
                      label: const Text('重置配置'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Card(
                color: Colors.blue.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.blue),
                          SizedBox(width: 8),
                          Text(
                            '使用说明',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '• 启用代理后，应用的网络请求将通过指定的代理服务器\n'
                        '• IP地址格式：xxx.xxx.xxx.xxx (每个数字0-255)\n'
                        '• 端口范围：1-65535\n'
                        '• 保存后配置将自动生效\n'
                        '• 当前代理字符串：${_proxyConfig.getProxyString()}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}