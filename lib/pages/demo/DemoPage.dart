import 'package:dlyz_flutter/components/common_alert.dart';
import 'package:dlyz_flutter/pages/demo/privacy_demo.dart';
import 'package:dlyz_flutter/pages/login/login_dialog_container.dart';
import 'package:dlyz_flutter/pages/download/download_test.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../../manager/channel_manager.dart';
import '../../utils/aes_utils.dart';
import '../../utils/md5_utils.dart';
import '../../utils/permission_utils.dart';
import '../../utils/sp_utils.dart';
import '../../webview/webview_page.dart';
import '../update/update_dialog.dart';
import 'gateway_encrypt_demo.dart';
import 'guide_demo_page.dart';
import 'bottom_sheet_demo_page.dart';

class DemoPage extends StatefulWidget {
  const DemoPage({super.key, required this.title});

  final String title;

  @override
  State<DemoPage> createState() => _DemoPageState();
}

class _DemoPageState extends State<DemoPage> {
  String _counter = "下载进度：0%";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (context) => const WebViewPage(title: 'WebView Demo'),
                      ),
                    );
                  },
                  child: const Text('打开WebView演示'),
                ),
                ...commonDemoLayout(),
                ...loginDemoLayout(),
                ...spDemoLayout(),
                ...encryptDemoLayout(),
                ...md5DemoLayout(),
                ...gatewayEncryptDemoLayout(),
                ...downLoadApkDemoLayout(),
                ...downloadTestLayout(),
                ...errorDemoLayout(),
                ...permissionDemoLayout(),
                ...appListsDemoLayout(),
                ...guideDemoLayout(),
                ...bottomSheetDemoLayout(),
                ...privacyDemoLayout(),
                ...gameBindingDemoLayout()
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 登录弹窗demo
  List loginDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              return LoginDialogContainer(
                initialPhoneNumber: '13800138001',
                onLoginSuccess: () {
                  print('用户登录成功');
                  Fluttertoast.showToast(
                    msg: '登录成功',
                    toastLength: Toast.LENGTH_SHORT,
                    gravity: ToastGravity.CENTER,
                    timeInSecForIosWeb: 1,
                    backgroundColor: Colors.green,
                    textColor: Colors.white,
                    fontSize: 16.0,
                  );
                },
                onForgetPassword: () {
                  print('用户点击了找回账号/密码');
                  Fluttertoast.showToast(
                    msg: '找回账号/密码功能开发中...',
                    toastLength: Toast.LENGTH_SHORT,
                    gravity: ToastGravity.CENTER,
                    timeInSecForIosWeb: 1,
                    backgroundColor: Colors.grey,
                    textColor: Colors.white,
                    fontSize: 16.0,
                  );
                },
                onClose: () {
                  print('用户关闭了登录弹窗');
                },
              );
            },
          );
        },
        child: const Text('打开登录弹窗'),
      ),
    ];
  }


  ///sp demo
  List spDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          SpManager.getInstance().put("counter", _counter);
        },
        child: const Text('存储sp'),
      ),
      ElevatedButton(
        onPressed: () {
          var toastMsg = SpManager.getInstance().getString("counter") ?? "null";
          Fluttertoast.showToast(
            msg: toastMsg,
            toastLength: Toast.LENGTH_SHORT,
            // 或者Toast.LENGTH_LONG
            gravity: ToastGravity.CENTER,
            // 位置：可以调整为TOP、CENTER等
            timeInSecForIosWeb: 1,
            // iOS和Web上显示的时间（秒）
            backgroundColor: Colors.grey,
            textColor: Colors.white,
            fontSize: 16.0,
          );
        },
        child: const Text('获取sp'),
      ),
    ];
  }

  ///加解密demo
  List encryptDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          SpManager.getInstance().put("counter", AesUtil.encrypt(_counter));
        },
        child: const Text('AES加密'),
      ),

      ElevatedButton(
        onPressed: () {
          var decrypted = AesUtil.decrypt(
            SpManager.getInstance().getString('counter') ?? "",
          );
          Fluttertoast.showToast(
            msg: decrypted,
            toastLength: Toast.LENGTH_SHORT,
            // 或者Toast.LENGTH_LONG
            gravity: ToastGravity.CENTER,
            // 位置：可以调整为TOP、CENTER等
            timeInSecForIosWeb: 1,
            // iOS和Web上显示的时间（秒）
            backgroundColor: Colors.grey,
            textColor: Colors.white,
            fontSize: 16.0,
          );
        },
        child: const Text('AES解密'),
      ),
    ];
  }

  ///md5 demo
  List md5DemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          var md5 = Md5Utils.generateMd5(_counter);
          Fluttertoast.showToast(
            msg: md5,
            toastLength: Toast.LENGTH_SHORT,
            // 或者Toast.LENGTH_LONG
            gravity: ToastGravity.CENTER,
            // 位置：可以调整为TOP、CENTER等
            timeInSecForIosWeb: 1,
            // iOS和Web上显示的时间（秒）
            backgroundColor: Colors.grey,
            textColor: Colors.white,
            fontSize: 16.0,
          );
        },
        child: const Text('MD5加密'),
      ),
    ];
  }

  ///网关加密拦截器demo
  List gatewayEncryptDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const GateWayEncryptDemo(),
            ),
          );
        },
        child: const Text('网关加密拦截器'),
      ),
    ];
  }



  ///下载APk demo
  List downLoadApkDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          VersionUpdateHelper.showVersionUpdateDialog(
                  context,
                  onUpdateNow: () {
                    Navigator.of(context).pop();
                    // 这里可以添加更新逻辑
                    print('用户选择立即更新');
                  },
                  downloadUrl:'https://developer-mt.nofeba.com/media/android_mainland_pack_output/2025_06_23/huawei_20250618-dcb8ab4f-8196-4948-84f8-8f964f875134_20250623_1750652824.apk'
                );
        },
        child: const Text('强更'),
      ),
      
    ];
  }

  ///下载测试demo
  List downloadTestLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const DownloadTest(),
            ),
          );
        },
        child: const Text('下载管理器测试'),
      ),
    ];
  }

  ///异常demo
  List errorDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          throw Exception('测试异常');
        },
        child: const Text('测试异常'),
      ),
    ];
  }

  ///权限获取demo
  List permissionDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () async {
          bool requestPermissionResult = await PermissionUtils.requestCameraPermission(
            context,
            description: '需要相机权限来拍摄照片和录制视频',
          );
          if (requestPermissionResult) {
            Fluttertoast.showToast(
              msg: "相机权限获取成功",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.grey,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          } else {
            Fluttertoast.showToast(
              msg: "相机权限获取失败",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          }
        },
        child: const Text('申请相机权限'),
      ),
      ElevatedButton(
        onPressed: () async {
          bool requestPermissionResult = await PermissionUtils.requestMicrophonePermission(
            context,
            description: '需要麦克风权限来录制音频',
          );
          if (requestPermissionResult) {
            Fluttertoast.showToast(
              msg: "麦克风权限获取成功",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.grey,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          } else {
            Fluttertoast.showToast(
              msg: "麦克风权限获取失败",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          }
        },
        child: const Text('申请麦克风权限'),
      ),
      ElevatedButton(
        onPressed: () async {
          bool requestPermissionResult = await PermissionUtils.requestStoragePermission(
            context,
            description: '需要存储权限来保存文件',
          );
          if (requestPermissionResult) {
            Fluttertoast.showToast(
              msg: "存储权限获取成功",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.grey,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          } else {
            Fluttertoast.showToast(
              msg: "存储权限获取失败",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          }
        },
        child: const Text('申请存储权限'),
      ),
      ElevatedButton(
        onPressed: () async {
          bool requestPermissionResult = await PermissionUtils.requestLocationPermission(
            context,
            description: '需要位置权限来获取您的位置信息',
          );
          if (requestPermissionResult) {
            Fluttertoast.showToast(
              msg: "位置权限获取成功",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.grey,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          } else {
            Fluttertoast.showToast(
              msg: "位置权限获取失败",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          }
        },
        child: const Text('申请位置权限'),
      ),
      ElevatedButton(
        onPressed: () async {
          bool requestPermissionResult = await PermissionUtils.requestPhotosPermission(
            context,
            description: '需要相册权限来保存相片',
          );
          if (requestPermissionResult) {
            Fluttertoast.showToast(
              msg: "相册权限获取成功",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.grey,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          } else {
            Fluttertoast.showToast(
              msg: "相册权限获取失败",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          }
        },
        child: const Text('申请相册权限'),
      ),
      ElevatedButton(
        onPressed: () async {
          bool requestPermissionResult = await PermissionUtils.requestNotificationPermission(
            context,
            description: '需要通知权限来推送信息',
          );
          if (requestPermissionResult) {
            Fluttertoast.showToast(
              msg: "通知权限获取成功",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.grey,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          } else {
            Fluttertoast.showToast(
              msg: "通知权限获取失败",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          }
        },
        child: const Text('申请通知权限'),
      ),
      ElevatedButton(
        onPressed: () async {
          if (await PermissionUtils.isPermissionGranted(PermissionType.camera)) {
            Fluttertoast.showToast(
              msg: "相机权限已获取",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.grey,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          } else {
            Fluttertoast.showToast(
              msg: "相机权限未获取",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.red,
              textColor: Colors.white,
              fontSize: 16.0,
            );
          }
        },
        child: const Text('检查相机权限'),
      ),
    ];
  }

  ///应用列表demo
  List appListsDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () async {
          List appLists = await PermissionUtils.getInstalledApps();
          print("applist:"+appLists.toString());
          Fluttertoast.showToast(
            msg: appLists.toString(),
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.CENTER,
            timeInSecForIosWeb: 1,
            backgroundColor: Colors.red,
            textColor: Colors.white,
            fontSize: 16.0,
          );
        },
        child: const Text('获取应用列表'),
      ),
    ];
  }

  ///攻略页面demo
  List guideDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const GuideDemoPage(),
            ),
          );
        },
        child: const Text('游戏攻略页面'),
      ),
    ];
  }

  ///底部弹窗demo
  List bottomSheetDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const BottomSheetDemoPage(),
            ),
          );
        },
        child: const Text('底部弹窗演示'),
      ),
    ];
  }

  /// 通用弹窗demo
  List commonDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () async {
          CommonAlert.show(context: context, title: "提示", content: "请选择是否授权当前登录信息《斗罗宇宙》");
        },
        child: const Text('通用提示弹窗'),
      ),
    ];
  }

  ///隐私政策demo
  List privacyDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const TestPrivacyPage(),
            ),
          );
        },
        child: const Text('隐私政策测试'),
      ),
    ];
  }

  List gameBindingDemoLayout() {
    return [
      ElevatedButton(
        onPressed: () {
          _launchApp();
        },
        child: const Text('游戏绑定演示'),
      ),
    ];
  }

  Future<void> _launchApp() async {
    try {
      const params = {
        'uid': 'test-uid',
        'ticket': 'test-ticket',
        'dialogContent': '请选择是否授权当前登录信息《斗罗宇宙》',
      };
      ChannelManager().bindingGame(packageName: "com.sy.yxjun", bindingParams: params);
    } catch (e) {

    }
  }
}
