
import 'dart:io';

import 'package:dlyz_flutter/components/download_bottom_dialog.dart';
import 'package:dlyz_flutter/components/game_card.dart';
import 'package:dlyz_flutter/providers/download_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../webview/webview_dialog.dart';

class GamesPage extends StatefulWidget {
  const GamesPage({super.key});

  @override
  State<GamesPage> createState() => _GamesState();
}

class _GamesState extends State<GamesPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              width: double.infinity,
              color: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 8.0),
              child: Text(
                "游戏中心",
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600
                ),
              ),
            ),
            // 游戏列表
            Padding(
                padding: const EdgeInsets.only(top: 1.0),
                child: Consumer<DownloadProvider>(
                    builder: (context, provider, child) {
                      return ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: provider.gameItems.length,
                        itemBuilder: (context, index) {
                          final game = provider.gameItems[index];
                          return GameCard(
                              game: game,
                              onTap: () {
                                showOfficialWeb(game.officialWeb);
                              },
                              onDownload: () {
                                if (Platform.isAndroid) {
                                  DownloadBottomDialog.show(context: context, game: game);
                                } else if (Platform.isIOS) {
                                  if (game.miniProgram.isNotEmpty) {
                                    DownloadBottomDialog.show(context: context, game: game);
                                  } else {
                                    provider.handleDownload(game.downloadInfo);
                                  }
                                }
                              }
                          );
                        },
                      );
                    }
                ),
            ),
          ],
        ),
      ),
    );
  }

  /// 展示游戏官网
  void showOfficialWeb(String url) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.transparent,
      builder: (context) {
      return WebViewDialog(
        url: url,
        title: '官网',
        showToolBar: true,
      );
    });
  }

}