
import 'package:dlyz_flutter/components/download_bottom_dialog.dart';
import 'package:dlyz_flutter/components/game_card.dart';
import 'package:dlyz_flutter/providers/game_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class GamesPage extends StatefulWidget {
  const GamesPage({super.key});

  @override
  State<GamesPage> createState() => _GamesState();
}

class _GamesState extends State<GamesPage> {
  @override
  void initState() {
    super.initState();
    // 初始化下载处理器
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<GameProvider>(context, listen: false).initializeDownloadHandlers();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 搜索栏
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
              child: Sized<PERSON><PERSON>(
                height: 30, // 设置固定高度
                child: TextField(
                  decoration: InputDecoration(
                    hintText: '搜索游戏',
                    prefixIcon: const Icon(Icons.search, size: 14),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6.0),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.grey[200],
                    contentPadding: const EdgeInsets.symmetric(vertical: 2.0, horizontal: 8.0),
                    hintStyle: const TextStyle(fontSize: 11),
                    isDense: true,
                  ),
                  style: const TextStyle(fontSize: 12),
                ),
              ),
            ),

            // 游戏列表
            Consumer<GameProvider>(
              builder: (context, gameProvider, child) {
                return ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: gameProvider.gameItems.length,
                  itemBuilder: (context, index) {
                    final game = gameProvider.gameItems[index];
                    return GameCard(
                        game: game,
                        onTap: () => DownloadBottomDialog.show(context: context, game: game),
                        onDownload: () => gameProvider.handleDownload(game)
                    );
                  },
                );
              }
            ),
          ],
        ),
      ),
    );
  }

}