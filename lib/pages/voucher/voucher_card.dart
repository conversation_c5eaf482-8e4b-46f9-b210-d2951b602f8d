import 'package:flutter/material.dart';

enum VoucherCardType { receive, usable, used }

class VoucherCard extends StatefulWidget {
  final Map<String, dynamic> data;
  final VoucherCardType type;
  const VoucherCard({super.key, required this.data, required this.type});

  @override
  State<VoucherCard> createState() => _VoucherCardState();
}

class _VoucherCardState extends State<VoucherCard> {
  bool _expanded = false;

  @override
  Widget build(BuildContext context) {
    final isUsed = widget.type == VoucherCardType.used;
    final isUsable = widget.type == VoucherCardType.usable;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isUsed ? const Color(0xFFE5E5E5) : (isUsable ? const Color(0xFFFFEFDB) : const Color(0xFFFFEFDB)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 主要内容区域
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 金额和使用条件
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.baseline,
                      textBaseline: TextBaseline.alphabetic,
                      children: [
                        Text(
                          '¥${widget.data['amount']}',
                          style: TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: isUsed ? Colors.black38 : Colors.black,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '满￥${widget.data['limit']}使用',
                          style: TextStyle(
                            fontSize: 14,
                            color: isUsed ? Colors.black26 : Colors.black54,
                          ),
                        ),
                        const Spacer(),
                        if (!isUsed)
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: isUsable ? const Color(0xFFD4A574) : Colors.orange,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            ),
                            onPressed: () {},
                            child: Text(
                              isUsable ? '去使用' : '领取',
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // 使用时间
                    Text(
                      '使用时间',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isUsed ? Colors.black38 : Colors.black,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.data['time'] ?? '',
                      style: TextStyle(
                        fontSize: 14,
                        color: isUsed ? Colors.black26 : Colors.black54,
                      ),
                    ),

                    const SizedBox(height: 12),

                    // 代金券标题和展开按钮
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            widget.data['title'] ?? '',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: isUsed ? Colors.black38 : Colors.black,
                            ),
                          ),
                        ),
                        IconButton(
                          icon: Icon(
                            _expanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                            color: Colors.grey,
                            size: 24,
                          ),
                          onPressed: () {
                            setState(() {
                              _expanded = !_expanded;
                            });
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // 展开的详细信息
              if (_expanded)
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.only(top: 8),
                  decoration: BoxDecoration(
                    color: isUsed ? const Color(0xFFECECEC) : const Color(0xFFFFFAF3),
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                  ),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (widget.data['desc'] != null && widget.data['desc'].isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: Text(
                            widget.data['desc'],
                            style: TextStyle(
                              fontSize: 14,
                              color: isUsed ? Colors.black26 : Colors.black54,
                            ),
                          ),
                        ),

                      if (widget.data['rule'] != null && widget.data['rule'].isNotEmpty)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '使用规则：',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: isUsed ? Colors.black38 : Colors.black,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              widget.data['rule'],
                              style: TextStyle(
                                fontSize: 13,
                                color: isUsed ? Colors.black26 : Colors.black45,
                                height: 1.4,
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),
            ],
          ),

          // 右上角状态标签
          if (isUsed)
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  '已失效',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
} 