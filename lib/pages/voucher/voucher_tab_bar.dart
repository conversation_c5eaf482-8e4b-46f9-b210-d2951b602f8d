import 'package:flutter/material.dart';

///代金券TabBar组件
class VoucherTabBar extends StatelessWidget {
  final List<String> tabs;
  final int currentIndex;
  final Function(int) onTabChanged;

  const VoucherTabBar({
    Key? key,
    required this.tabs,
    required this.currentIndex,
    required this.onTabChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          color: const Color(0xFFFFFFFF),
          padding: const EdgeInsets.only(left: 16, right: 16),
          child: Container(color: const Color(0xFFFFFFFF), child: buildTab()),
        ),
        Divider(height: 1, color: const Color(0xFFF5F5F5)),
      ],
    );
  }

  Widget buildTab() {
    return Row(
      children:
          tabs.asMap().entries.map((entry) {
            int index = entry.key;
            String tab = entry.value;
            bool isSelected = index == currentIndex;

            return Expanded(
              child: GestureDetector(
                onTap: () => onTabChanged(index),
                child: Container(
                  height: 48,
                  child: Stack(
                    children: [
                      // 文字居中
                      Center(
                        child: Text(
                          tab,
                          style: TextStyle(
                            fontSize: isSelected ? 18 : 16,
                            fontWeight:
                                isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                            color: isSelected ? Colors.black : Colors.grey,
                          ),
                        ),
                      ),
                      // 指示器在底部
                      if (isSelected)
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: Center(
                            child: Container(
                              height: 3,
                              width: 30,
                              decoration: const BoxDecoration(
                                color: Colors.orange,
                                borderRadius: BorderRadius.all(
                                  Radius.circular(0),
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
    );
  }
}
