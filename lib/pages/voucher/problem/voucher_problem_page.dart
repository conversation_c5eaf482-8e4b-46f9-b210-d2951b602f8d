import 'dart:math';
import 'package:flutter/material.dart';

/// 常见问题页面
class VoucherProblemPage extends StatefulWidget {
  const VoucherProblemPage({super.key});

  @override
  State<VoucherProblemPage> createState() => _VoucherProblemPageState();
}

class _VoucherProblemPageState extends State<VoucherProblemPage>
    with TickerProviderStateMixin {
  // 当前展开的问题索引，默认展开第一项
  int _expandedIndex = 0;

  // ExpansionTile控制器列表
  late List<ExpansionTileController> _controllers;

  // FAQ数据
  List<VoucherFaqItem>? _faqData;

  // 加载状态
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFaqData();
  }

  /// todo 待后续补充网络接口
  /// 模拟网络请求获取FAQ数据
  Future<void> _loadFaqData() async {
    // 模拟网络延时2秒
    await Future.delayed(const Duration(seconds: 2));

    // 随机bool值，决定是否返回数据
    final bool shouldReturnData = Random().nextBool();

    // 模拟获取的网络数据
    final faqData = shouldReturnData ? [
      VoucherFaqItem(
        question: '1、什么是代金券?',
        answer: '代金券是面向官方自营游戏包玩家的专项福利,可在官方自营游戏包中,用于购买游戏商品时抵扣相应面值的费用。',
      ),
      VoucherFaqItem(
        question: '2、如何领取和查看代金券?',
        answer: '您可以通过游戏内活动、官方活动页面等方式领取代金券。领取后可在"我的代金券"页面查看所有代金券。',
      ),
      VoucherFaqItem(
        question: '3、代金券如何使用?',
        answer: '在支持代金券的游戏中，选择商品后点击支付，系统会自动识别可用的代金券并显示抵扣金额。',
      ),
      VoucherFaqItem(
        question: '4、代金券使用有效期多久?',
        answer: '代金券有效期因活动而异，通常在7-30天不等，具体有效期请查看代金券详情页面。',
      ),
      VoucherFaqItem(
        question: '5、如何区分代金券是否有使用门槛?',
        answer: '代金券详情页面会明确显示使用门槛，如"满30元可用"表示需要消费满30元才能使用该代金券。',
      ),
      VoucherFaqItem(
        question: '6、代金券支持组合支付吗?',
        answer: '是的，代金券可以与余额、银行卡等其他支付方式组合使用，但每笔订单仅限使用1张代金券。',
      ),
      VoucherFaqItem(
        question: '7、能否多张代金券一起使用?',
        answer: '不可以，每笔订单仅限使用1张代金券，不支持多张代金券同时使用。',
      ),
      VoucherFaqItem(
        question: '8、代金券余额是否可分开多次使用?',
        answer: '不可以，代金券为一次性使用，使用后即失效，不支持分多次使用。',
      ),
      VoucherFaqItem(
        question: '9、已使用的代金券能否退还?',
        answer: '代金券使用后不支持退还，请在确认订单信息后再使用代金券。',
      ),
      VoucherFaqItem(
        question: '10、平台券适用游戏范围?',
        answer: '代金券适用范围因活动而异，具体适用游戏请查看代金券详情页面的"适用游戏"说明。',
      ),
      VoucherFaqItem(
        question: '11、苹果手机(iOS)可以使用代金券吗?',
        answer: '可以，iOS设备同样支持代金券功能，使用方式与Android设备相同。',
      ),
    ] : <VoucherFaqItem>[];

    if (mounted) {
      setState(() {
        _faqData = faqData;
        _isLoading = false;
        // 初始化控制器列表
        _controllers = List.generate(
          _faqData!.length,
          (index) => ExpansionTileController(),
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          '常见问题',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    '正在加载常见问题...',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            )
          : _faqData == null || _faqData!.isEmpty
              ? const Center(
                  child: Text(
                    '暂无常见问题',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 16,
                    ),
                  ),
                )
              : ListView.builder(
                  padding: EdgeInsets.zero,
                  itemCount: _faqData!.length,
                  physics: const ClampingScrollPhysics(), // 优化滚动性能
                  cacheExtent: 500, // 缓存更多item以减少重建
                  itemBuilder: (context, index) {
                    return _FAQItem(
                      key: ValueKey(index),
                      index: index,
                      faqData: _faqData![index],
                      isExpanded: _expandedIndex == index,
                      controller: _controllers[index],
                      onExpansionChanged: (expanded) {
                        setState(() {
                          if (expanded) {
                            // 先收起所有其他item
                            for (int i = 0; i < _controllers.length; i++) {
                              if (i != index && _expandedIndex == i) {
                                _controllers[i].collapse();
                              }
                            }
                            _expandedIndex = index;
                          } else {
                            _expandedIndex = -1;
                          }
                        });
                      },
                    );
                  },
                ),
    );
  }
}

class _FAQItem extends StatelessWidget {
  final int index;
  final VoucherFaqItem faqData;
  final bool isExpanded;
  final ExpansionTileController controller;
  final ValueChanged<bool> onExpansionChanged;

  const _FAQItem({
    super.key,
    required this.index,
    required this.faqData,
    required this.isExpanded,
    required this.controller,
    required this.onExpansionChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E5E5), width: 0.5),
        ),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          controller: controller,
          initiallyExpanded: isExpanded,
          title: Text(
            faqData.question ?? '',
            style: const TextStyle(
              color: Colors.black,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          trailing: AnimatedRotation(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            turns: isExpanded ? 0.5 : 0,
            child: Icon(
              Icons.keyboard_arrow_down,
              color: Colors.grey[600],
            ),
          ),
          onExpansionChanged: onExpansionChanged,
          tilePadding: const EdgeInsets.symmetric(
            horizontal: 16.0,
            vertical: 0.0,
          ),
          childrenPadding: const EdgeInsets.fromLTRB(
            16.0,
            0,
            16.0,
            8.0,
          ),
          children: [
            RepaintBoundary(
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  faqData.answer ?? "",
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 16,
                    height: 1.6,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 问题数据模型
class VoucherFaqItem {
  final String? question;
  final String? answer;

  VoucherFaqItem({this.question, this.answer});
}
