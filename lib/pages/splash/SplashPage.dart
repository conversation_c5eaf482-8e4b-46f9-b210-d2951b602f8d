import 'package:flutter/material.dart';
import 'dart:async';
import 'package:provider/provider.dart';
import '../../providers/user_provider.dart';
import '../../services/app_route_manager.dart';
import '../../net/api/init_service.dart';
import '../../utils/log_util.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({Key? key}) : super(key: key);

  @override
  _SplashPageState createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  // 更新类型常量
  static const String TYPE_NORMAL = "1";   // 正常，不需要更新
  static const String TYPE_UPDATE = "2";   // 普通更新
  static const String TYPE_FORCE = "3";    // 强制更新

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // 创建渐变动画
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    // 创建缩放动画
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeOutBack),
    );

    // 启动动画
    _fadeController.forward();
    _scaleController.forward();

    // 调用激活接口
    _callActivateAPI();

    // 3秒后跳转到主页面，带渐变动画
    Timer(const Duration(seconds: 0), () {
      _navigateWithAnimation();
    });
  }

  Future<void> _callActivateAPI() async {
    try {
      final response = await InitService.mActivate(context: context);
      LogUtil.e("激活 ${response.toString()}");
      if (response.success && response.data != null) {
        LogUtil.d('M层激活成功');
        // 检查是否需要更新
        await _checkUpdateConfig(response.data);
        // 执行S层激活接口
        await _callSActivateAPI();
      } else {
        LogUtil.d('M层激活失败: ${response.message}');
      }
    } catch (e) {
      LogUtil.e('M层激活接口调用异常: $e');
    }
  }

  Future<void> _callSActivateAPI() async {
    try {
      final response = await InitService.sActivate(context: context);
      LogUtil.e("S层激活 ${response.toString()}");
      if (response.success && response.data != null) {
        LogUtil.d('S层激活成功');
      } else {
        LogUtil.d('S层激活失败: ${response.message}');
      }
    } catch (e) {
      LogUtil.e('S层激活接口调用异常: $e');
    }
  }

  /// 检查更新配置
  Future<void> _checkUpdateConfig(dynamic data) async {
    try {
      // 检查是否有更新类型字段
      if (data.utype != null && data.utype!.isNotEmpty) {
        final updateType = data.utype!;
        final apkUrl = data.uurl ?? '';
        final updateContent = data.uct ?? '';
        const version = "1.0";

        LogUtil.d('检查更新配置: updateType=$updateType, apkUrl=$apkUrl');

        if (updateType == TYPE_NORMAL) {
          // 正常，不需要更新
          LogUtil.d('当前版本正常，无需更新');
        } else if (updateType == TYPE_UPDATE) {
          // 普通更新
          LogUtil.d('发现可用更新，弹出更新提示');
          _showUpdateDialog(false, updateContent, apkUrl, version);
        } else if (updateType == TYPE_FORCE) {
          // 强制更新
          LogUtil.d('发现强制更新，弹出强制更新提示');
          _showUpdateDialog(true, updateContent, apkUrl, version);
        }
      }
    } catch (e) {
      LogUtil.e('检查更新配置失败: $e');
    }
  }

  /// 显示更新弹窗
  void _showUpdateDialog(bool isForce, String updateContent, String apkUrl, String version) {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: !isForce, // 强制更新时不允许点击外部关闭
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(isForce ? '强制更新' : '版本更新'),
          content: Text(updateContent.isNotEmpty ? updateContent : '发现新版本，建议立即更新以获得更好的体验。'),
          actions: [
            if (!isForce) // 非强制更新时显示取消按钮
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('稍后更新'),
              ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _startDownload(apkUrl);
              },
              child: const Text('立即更新'),
            ),
          ],
        );
      },
    );
  }

  /// 开始下载更新
  void _startDownload(String apkUrl) {
    if (apkUrl.isEmpty) {
      LogUtil.e('下载地址为空');
      return;
    }

    LogUtil.d('开始下载更新: $apkUrl');

    // 这里应该调用实际的下载管理器
    // 目前先用简单的提示代替
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('开始下载更新: $apkUrl'),
          duration: const Duration(seconds: 3),
        ),
      );
    }

    // TODO: 集成实际的下载管理器
    // 例如：SQUpdateManager.startDownload(context, apkUrl);
  }

  void _navigateWithAnimation() async {
    // 在动画期间检查登录状态
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    await userProvider.initialize();

    // 先执行淡出动画
    await _fadeController.reverse();

    // 使用路由管理器进行跳转
    if (mounted) {
      await AppRouteManager.checkLoginNavigate(context);
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Container(
            width: double.infinity, // 全屏宽度
            height: double.infinity, // 全屏高度
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/images/dlyz_splash.png'),
                fit: BoxFit.cover, // 填充整个屏幕
              ),
            ),
          ),
        ),
      ),
    );
  }
}