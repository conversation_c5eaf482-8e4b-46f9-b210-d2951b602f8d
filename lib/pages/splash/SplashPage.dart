import 'package:flutter/material.dart';
import 'dart:async';
import '../../main.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({Key? key}) : super(key: key);

  @override
  _SplashPageState createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // 创建渐变动画
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    // 创建缩放动画
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeOutBack),
    );

    // 启动动画
    _fadeController.forward();
    _scaleController.forward();

    // 3秒后跳转到主页面，带渐变动画
    Timer(const Duration(seconds: 3), () {
      _navigateWithAnimation();
    });
  }

  void _navigateWithAnimation() async {
    // 先执行淡出动画
    await _fadeController.reverse();

    // 动画完成后直接跳转到主页面
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) {
            return const MyHomePage(title: 'Flutter Demo Home Page');
          },
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 800),
        ),
      );
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: Colors.white, // 设置背景色
      //   body: Center(
      //     child: FadeTransition(
      //       opacity: _fadeAnimation,
      //       child: ScaleTransition(
      //         scale: _scaleAnimation,
      //         child: Container(
      //           width: 200,
      //           height: 200,
      //           decoration: const BoxDecoration(
      //             image: DecorationImage(
      //               image: AssetImage('assets/images/sy37_ic_logo.png'),
      //               fit: BoxFit.contain,
      //             ),
      //           ),
      //         ),
      //       ),
      //     ),
      //   ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Container(
            width: double.infinity, // 全屏宽度
            height: double.infinity, // 全屏高度
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/images/dlyz_splash.png'),
                fit: BoxFit.cover, // 填充整个屏幕
              ),
            ),
          ),
        ),
      ),
    );
  }
}
