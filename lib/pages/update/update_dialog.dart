import 'dart:async';
import 'package:flutter/material.dart';

class VersionUpdateDialog extends StatefulWidget {
  final VoidCallback? onExitApp;
  final VoidCallback? onUpdateNow;

  const VersionUpdateDialog({
    super.key,
    this.onExitApp,
    this.onUpdateNow,
  });

  @override
  State<VersionUpdateDialog> createState() => _VersionUpdateDialogState();
}

class _VersionUpdateDialogState extends State<VersionUpdateDialog> {
  bool _isDownloading = false;
  double _downloadProgress = 0.0;

  void _startDownload() {
    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
    });

    // 模拟下载过程
    const totalSteps = 100;
    int currentStep = 0;

    Timer.periodic(const Duration(milliseconds: 50), (timer) {
      currentStep++;
      setState(() {
        _downloadProgress = currentStep / totalSteps;
      });

      if (currentStep >= totalSteps) {
        timer.cancel();
        _downloadComplete();
      }
    });
  }

  void _downloadComplete() {
    // 下载完成后的逻辑
    if (widget.onUpdateNow != null) {
      widget.onUpdateNow!();
    } else {
      // 默认完成逻辑
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('下载完成！'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 8,
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            const Text(
              '版本更新',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),

            const SizedBox(height: 24),

            // 图标
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.lightBlue[100],
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.refresh,
                size: 40,
                color: Colors.blue,
              ),
            ),

            const SizedBox(height: 24),

            // 消息或下载进度
            if (!_isDownloading) ...[
              const Column(
                children: [
                  Text(
                    '当前版本已不再支持,',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 4),
                  Text(
                    '请立即更新以继续使用',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ] else ...[
              // 下载进度条
              Column(
                children: [
                  const Text(
                    '正在下载新版本...',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: _downloadProgress,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${(_downloadProgress * 100).toInt()}%',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ],

            const SizedBox(height: 32),

            // 按钮
            if (!_isDownloading) ...[
              Row(
                children: [
                  // 退出应用按钮
                  Expanded(
                    child: ElevatedButton(
                      onPressed: widget.onExitApp ?? () {
                        Navigator.of(context).pop();
                        // 这里可以添加退出应用的逻辑
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[300],
                        foregroundColor: Colors.black,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text(
                        '退出应用',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // 立即更新按钮
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        _startDownload();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text(
                        '立即更新',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ] else ...[
              // 下载时只显示退出按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: widget.onExitApp ?? () {
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[300],
                    foregroundColor: Colors.black,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text(
                    '退出应用',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// 显示版本更新弹窗的便捷方法
class VersionUpdateHelper {
  static void showVersionUpdateDialog(
      BuildContext context, {
        VoidCallback? onExitApp,
        VoidCallback? onUpdateNow,
      }) {
    showDialog(
      context: context,
      barrierDismissible: false, // 禁止点击外部关闭
      builder: (BuildContext context) {
        return VersionUpdateDialog(
          onExitApp: onExitApp,
          onUpdateNow: onUpdateNow,
        );
      },
    );
  }
}