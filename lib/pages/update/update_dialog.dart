import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../providers/download_provider.dart';
import '../../model/download_info.dart';
import '../../services/download/ALDownloader.dart';
import '../../services/download/ALDownloaderStatus.dart';
import '../../services/download/ALDownloaderHandlerInterface.dart';
import '../../services/download/ALDownloaderFileManager.dart';
import '../../services/download/ALDownloaderTypeDefine.dart';
import '../../manager/channel_manager.dart';

class VersionUpdateDialog extends StatefulWidget {
  final VoidCallback? onExitApp;
  final VoidCallback? onUpdateNow;
  final String? downloadUrl;  // 添加下载链接参数

  const VersionUpdateDialog({
    super.key,
    this.onExitApp,
    this.onUpdateNow,
    this.downloadUrl,  // 下载链接
  });

  @override
  State<VersionUpdateDialog> createState() => _VersionUpdateDialogState();
}

class _VersionUpdateDialogState extends State<VersionUpdateDialog> {
  bool _isDownloading = false;
  bool _isDownloadCompleted = false;  // 下载完成状态
  double _downloadProgress = 0.0;
  DownloadInfo? _downloadInfo;  // 下载信息对象
  ALDownloaderHandlerInterfaceId? _handlerInterfaceId;  // 处理器接口ID
  String? _downloadedFilePath;  // 下载完成的文件路径

  void _startDownload() {
    if (widget.downloadUrl == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('下载链接未配置')),
      );
      return;
    }

    _checkExistingFile();
  }

  void _checkExistingFile() async {
    if (widget.downloadUrl == null) return;

    // 检查是否已有下载的文件
    final physicalFilePath = await ALDownloaderFileManager.getPhysicalFilePathForUrl(widget.downloadUrl!);
    if (physicalFilePath != null) {
      // 有缓存文件，直接设置为下载完成状态
      setState(() {
        _isDownloading = false;
        _isDownloadCompleted = true;
        _downloadProgress = 1.0;
        _downloadedFilePath = physicalFilePath;
      });
      return;
    }

    // 没有缓存文件，开始新下载
    _startActualDownload();
  }

  void _startActualDownload() {
    setState(() {
      _isDownloading = true;
      _isDownloadCompleted = false;  // 重置下载完成状态
      _downloadProgress = 0.0;
      _downloadedFilePath = null;    // 重置文件路径
    });

    // 创建下载信息对象
    _downloadInfo = DownloadInfo(
      title: '应用更新',
      subtitle: '正在下载最新版本',
      imageUrl: '',
      packageName: 'com.example.update',
      url: widget.downloadUrl!,
      progress: '0',
      downloadStatus: '下载中',
    );

    // 添加下载处理器
    _addDownloadHandler();
    
    // 开始下载
    DownloadProvider().handleDownload(_downloadInfo!);
  }

  void _addDownloadHandler() {
    if (_downloadInfo == null) return;

    _handlerInterfaceId = ALDownloader.addHandlerInterface(
      ALDownloaderHandlerInterface(
        progressHandler: (progress, speed) {
          if (mounted) {
            _downloadInfo!.downloadStatusNotifier.value = '下载中';
            _downloadInfo!.downloadSpeedNotifier.value = _formatSpeed(speed);
            final targetProgress = progress * 100;
            setState(() {
              _downloadProgress = progress;
            });
            _downloadInfo!.progressNotifier.value = "${targetProgress.toStringAsFixed(2)}%";
          }
        },
        succeededHandler: () async {
          if (mounted) {
            final physicalFilePath = await ALDownloaderFileManager.getPhysicalFilePathForUrl(_downloadInfo!.url);
            if (physicalFilePath != null) {
              setState(() {
                _isDownloading = false;
                _isDownloadCompleted = true;
                _downloadProgress = 1.0;
                _downloadedFilePath = physicalFilePath;
              });
              _downloadInfo!.downloadStatusNotifier.value = '已完成';
              
              // 下载完成后自动安装
              _installApk();
            }
          }
        },
        failedHandler: () {
          if (mounted) {
            setState(() {
              _isDownloading = false;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('下载失败，请重试'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        pausedHandler: () {
          // 暂停处理（可选）
        },
      ),
      _downloadInfo!.url,
    );
  }

  String _formatSpeed(double speedInBytes) {
    if (speedInBytes < 1024) {
      return "${speedInBytes.toStringAsFixed(1)} B/s";
    } else if (speedInBytes < 1024 * 1024) {
      return "${(speedInBytes / 1024).toStringAsFixed(1)} KB/s";
    } else {
      return "${(speedInBytes / (1024 * 1024)).toStringAsFixed(1)} MB/s";
    }
  }

  void _downloadComplete() {
    // 下载完成后的逻辑
    if (widget.onUpdateNow != null) {
      widget.onUpdateNow!();
    } else {
      // 默认完成逻辑
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('下载完成，正在安装...'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _installApk() async {
    if (_downloadedFilePath != null) {
      try {
        // 使用原生方法安装APK
        final channelManager = ChannelManager();
        await channelManager.installApk(_downloadedFilePath!);
        _downloadComplete();
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('安装失败：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    // 只有在下载未完成时才清理下载任务，避免删除已下载的文件
    if (_handlerInterfaceId != null) {
      ALDownloader.removeHandlerInterfaceForId(_handlerInterfaceId!);
    }
    super.dispose();
  }

  // 退出应用方法
  void _exitApp() {
      Navigator.of(context).pop(false);
      // 延迟退出应用，让弹窗先关闭
      Future.delayed(const Duration(milliseconds: 100), () {
      if (Platform.isAndroid) {
        SystemNavigator.pop();
      } else {
        exit(0);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false, // 禁止通过返回键关闭弹窗
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 8,
        child: Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题
              const Text(
                '版本更新',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),

              const SizedBox(height: 24),

              // 图标
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.lightBlue[100],
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.refresh,
                  size: 40,
                  color: Colors.blue,
                ),
              ),

              const SizedBox(height: 24),

              // 消息或下载进度
              if (!_isDownloading && !_isDownloadCompleted) ...[
                const Column(
                  children: [
                    Text(
                      '当前版本已不再支持,',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.black,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 4),
                    Text(
                      '请立即更新以继续使用',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.black,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ] else ...[
                // 下载进度条
                Column(
                  children: [
                    Text(
                      _isDownloadCompleted ? '下载完成' : '正在下载新版本...',
                      style: TextStyle(
                        fontSize: 16,
                        color: _isDownloadCompleted ? Colors.green : Colors.black,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Container(
                      width: double.infinity,
                      height: 8,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: _downloadProgress,
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.blue,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${(_downloadProgress * 100).toInt()}%',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        // 显示下载速度
                        if (_downloadInfo != null)
                          ValueListenableBuilder<String>(
                            valueListenable: _downloadInfo!.downloadSpeedNotifier,
                            builder: (context, speed, child) {
                              return Text(
                                speed,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              );
                            },
                          ),
                      ],
                    ),
                  ],
                ),
              ],

              const SizedBox(height: 32),

              // 按钮
              if (!_isDownloading && !_isDownloadCompleted) ...[
                Row(
                  children: [
                    // 退出应用按钮
                    Expanded(
                      child: ElevatedButton(
                        onPressed: widget.onExitApp ?? _exitApp, // 使用真正的退出应用方法
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[300],
                          foregroundColor: Colors.black,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: const Text(
                          '退出应用',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // 立即更新按钮
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          _startDownload();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: const Text(
                          '立即更新',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ] else if (_isDownloading && !_isDownloadCompleted) ...[
                // 下载时只显示退出按钮
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: widget.onExitApp ?? _exitApp, // 使用真正的退出应用方法
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[300],
                      foregroundColor: Colors.black,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text(
                      '退出应用',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ] else if (_isDownloadCompleted) ...[
                // 下载完成时只显示立即安装按钮
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _installApk,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text(
                      '立即安装',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

// 显示版本更新弹窗的便捷方法
class VersionUpdateHelper {
  static void showVersionUpdateDialog(
      BuildContext context, {
        VoidCallback? onExitApp,
        VoidCallback? onUpdateNow,
        String? downloadUrl,  // 添加下载链接参数
      }) {
    showDialog(
      context: context,
      barrierDismissible: false, // 禁止点击外部关闭
      builder: (BuildContext context) {
        return VersionUpdateDialog(
          onExitApp: onExitApp,
          onUpdateNow: onUpdateNow,
          downloadUrl: downloadUrl,  // 传递下载链接
        );
      },
    );
  }
}