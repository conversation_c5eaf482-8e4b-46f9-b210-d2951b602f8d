import 'package:dlyz_flutter/pages/gift/gift_detail_page_new.dart';
import 'package:flutter/material.dart';
import '../../components/cache_image.dart';
import 'gift_search_overlay.dart';
import 'gift_detail_page.dart';

class GiftCenterPage extends StatefulWidget {
  const GiftCenterPage({super.key});

  @override
  State<GiftCenterPage> createState() => _GiftCenterPageState();
}

class _GiftCenterPageState extends State<GiftCenterPage> {
  bool _showAllMyGifts = false;
  bool _showSearchOverlay = false;
  List<GameGiftItem>? _searchResults; // 搜索结果列表

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          '礼包中心',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
      ),
      body: Stack(
        children: [
          // 主要内容
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 我的游戏礼包部分
                _buildMyGameGifts(),

                // 热门游戏礼包标题和搜索栏
                _buildHeader(),

                // 游戏礼包列表 - 如果有搜索结果则显示搜索结果，否则显示热门游戏列表
                _buildGameListOrSearchResults(),
              ],
            ),
          ),

          // 搜索覆盖层组件
          GiftSearchOverlay(
            isVisible: _showSearchOverlay,
            onClose: () {
              setState(() {
                _showSearchOverlay = false;
                // 不清除搜索结果，保持搜索后的状态
              });
            },
            onSearchResultsChanged: (searchResults) {
              setState(() {
                _searchResults = searchResults;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMyGameGifts() {
    final myGames = [
      GameGiftItem(
        name: '凡人修仙传:人界篇',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 2,
        badges: ['凡人修仙传'],
      ),
      GameGiftItem(
        name: '谜题大陆',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 2,
        badges: ['休闲'],
      ),
      GameGiftItem(
        name: '斗罗大陆:猎魂世界',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 2,
        badges: ['MMO'],
      ),
      GameGiftItem(
        name: '三国群英传:鸿鹄霸业',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 4,
        badges: ['SLG'],
      ),
      GameGiftItem(
        name: '离火之境',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 2,
        badges: ['MMO'],
      ),
    ];

    // 根据展开状态决定显示的游戏数量
    final displayGames = _showAllMyGifts ? myGames : myGames.take(3).toList();

    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (myGames.isNotEmpty)
            // 标题
            const Padding(
              padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Text(
                '我的游戏礼包',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ),

          // 游戏列表
          ...displayGames.asMap().entries.map((entry) {
            int index = entry.key;
            GameGiftItem game = entry.value;
            bool isLast = index == displayGames.length - 1;
            return _buildGameItem(game, context, isLast: isLast);
          }),

          // 查看更多/收起按钮
          if (myGames.length > 3)
            GestureDetector(
              onTap: () {
                setState(() {
                  _showAllMyGifts = !_showAllMyGifts;
                });
              },
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _showAllMyGifts ? '收起' : '查看更多礼包',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      _showAllMyGifts ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                  ],
                ),
              ),
            ),

          if (myGames.isNotEmpty)
            Container(
              color: const Color(0xFFF8F8F8),
              height: 30,
            ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        children: [
          const Expanded(
            child: Text(
              '热门游戏礼包',
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              setState(() {
                _showSearchOverlay = true;
              });
            },
            child: Container(
              width: 160,
              height: 36,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(18),
              ),
              child: Row(
                children: [
                  const SizedBox(width: 12),
                  const Icon(
                    Icons.search,
                    size: 18,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      '输入游戏名字',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 根据搜索状态显示游戏列表或搜索结果
  Widget _buildGameListOrSearchResults() {
    if (_searchResults != null) {
      // 显示搜索结果
      if (_searchResults!.isEmpty) {
        return Container(
          width: double.infinity,
          color: Colors.white,
          padding: const EdgeInsets.all(32),
          child: const Column(
            children: [
              Icon(
                Icons.search_off,
                size: 64,
                color: Colors.grey,
              ),
              SizedBox(height: 16),
              Text(
                '搜索结果为空',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        );
      } else {
        return Container(
          color: Colors.white,
          child: Column(
            children: [
              ..._searchResults!.asMap().entries.map((entry) {
                int index = entry.key;
                GameGiftItem game = entry.value;
                bool isLast = index == _searchResults!.length - 1;
                return _buildGameItem(game, context, isLast: isLast);
              }),
              const SizedBox(height: 16),
            ],
          ),
        );
      }
    } else {
      // 显示热门游戏列表
      return _buildGameList();
    }
  }

  Widget _buildGameList() {
    final games = [
      GameGiftItem(
        name: '斗罗大陆:猎魂世界',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 2,
        badges: ['MMO', '37手游'],
      ),
      GameGiftItem(
        name: '斗罗大陆:魂师对决',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 3,
        badges: ['RPG'],
      ),
      GameGiftItem(
        name: '凡人修仙传:人界篇',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 1,
        badges: ['凡人修仙传'],
      ),
      GameGiftItem(
        name: '三国群英传:鸿鹄霸业',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 4,
        badges: ['SLG'],
      ),
      GameGiftItem(
        name: '离火之境',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 2,
        badges: ['MMO'],
      ),
      GameGiftItem(
        name: '小小蚁国',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 1,
        badges: ['休闲'],
      ),
      GameGiftItem(
        name: '诸神黄昏:征服',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 3,
        badges: ['RPG'],
      ),
      GameGiftItem(
        name: '龙骑士学园',
        iconUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
        giftCount: 2,
        badges: ['卡牌'],
      ),
    ];

    return Container(
      color: Colors.white,
      child: Column(
        children: [
          ...games.asMap().entries.map((entry) {
            int index = entry.key;
            GameGiftItem game = entry.value;
            bool isLast = index == games.length - 1;
            return _buildGameItem(game, context, isLast: isLast);
          }),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildGameItem(GameGiftItem game, BuildContext context, {bool isLast = false}) {
    return Column(
      children: [
        GestureDetector(
          onTap: () {
            _jumpGiftDetailPage();
          },
          child: Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Row(
              children: [
                // 游戏图标
                Container(
                  width: 70,
                  height: 70,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: CachedImage(
                      imageUrl: game.iconUrl,
                      placeholder: Container(
                        color: Colors.grey[300],
                        child: const Icon(
                          Icons.games,
                          color: Colors.grey,
                          size: 30,
                        ),
                      ),
                      errorWidget: Container(
                        color: Colors.grey[300],
                        child: const Icon(
                          Icons.error_outline,
                          color: Colors.grey,
                          size: 30,
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 12),

                // 游戏信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        game.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      RichText(
                        text: TextSpan(
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                          children: [
                            const TextSpan(text: '可领取礼包总数'),
                            TextSpan(
                              text: '${game.giftCount}',
                              style: const TextStyle(
                                color: Color(0xFFF97727),
                              ),
                            ),
                            const TextSpan(text: '款'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // 右箭头
                const Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey,
                ),
              ],
            ),
          ),
        ),
        // 分割线 - 只在不是最后一个item时显示
        if (!isLast)
          Container(
            height: 1,
            margin: const EdgeInsets.symmetric(horizontal: 16), // 左右各16px边距
            color: const Color(0xFFF5F5F5),
          ),
      ],
    );
  }



  void _jumpGiftDetailPage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const GiftDetailPageNew(),
      ),
    );
  }
}

// 游戏礼包数据模型
class GameGiftItem {
  final String name;
  final String iconUrl;
  final int giftCount;
  final List<String> badges;

  const GameGiftItem({
    required this.name,
    required this.iconUrl,
    required this.giftCount,
    required this.badges,
  });
}

