import 'package:flutter/material.dart';

/// 每日签到页面
class GiftDetailPageNew extends StatefulWidget {
  const GiftDetailPageNew({super.key});

  @override
  State<GiftDetailPageNew> createState() => _GiftDetailPageNewState();
}

class _GiftDetailPageNewState extends State<GiftDetailPageNew> {
  // 签到提醒开关状态
  bool _reminderEnabled = true;

  // 当前已签到天数
  int _signedDays = 1;

  // 选中的区服
  String _selectedServer = '区服';

  // 角色昵称
  String _characterName = '角色昵称';

  // 角色等级
  String _characterLevel = '角色等级';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
        shadowColor: Colors.transparent,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          '斗罗大陆：魂师对决签到礼包',
          style: TextStyle(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // 顶部标题区域
          _buildHeaderSection(),
          // 区服选择区域
          _buildServerSection(),
          // 签到日历区域
          Expanded(
            child: _buildCalendarSection(),
          ),
        ],
      ),
    );
  }

  /// 构建顶部标题区域
  Widget _buildHeaderSection() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 左侧空白区域，用于平衡布局
          const SizedBox(width: 60),
          // 中间的"每日签到"标题
          const Text(
            '每日签到',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          // 右侧垂直排列的按钮
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              GestureDetector(
                onTap: () {
                  // 领取记录
                  _showReceiveRecords();
                },
                child: const Text(
                  '领取记录',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
              ),
              const SizedBox(height: 4),
              GestureDetector(
                onTap: () {
                  // 规则
                  _showRules();
                },
                child: const Text(
                  '规则',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建区服选择区域
  Widget _buildServerSection() {
    return Container(
      color: Colors.white,
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 左侧角色信息区域
          Expanded(
            child: Row(
              children: [
                // 角色信息显示区域
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      '$_selectedServer | $_characterName | $_characterLevel',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // 切换图标
                GestureDetector(
                  onTap: () {
                    _showCharacterSelector();
                  },
                  child: const Icon(
                    Icons.swap_horiz,
                    color: Colors.grey,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          // 右侧签到提醒
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                '签到提醒',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.black,
                ),
              ),
              const SizedBox(width: 8),
              Switch(
                value: _reminderEnabled,
                onChanged: (value) {
                  setState(() {
                    _reminderEnabled = value;
                  });
                },
                activeColor: Colors.orange,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 显示角色选择器（包含区服、角色名称、角色等级）
  void _showCharacterSelector() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '选择角色信息',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),

            // 区服选择
            const Text(
              '选择区服',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 120,
              child: ListView(
                children: [
                  '一区', '二区', '三区', '四区', '五区',
                ].map((server) => ListTile(
                  title: Text(server),
                  trailing: _selectedServer == server
                      ? const Icon(Icons.check, color: Colors.orange)
                      : null,
                  onTap: () {
                    setState(() {
                      _selectedServer = server;
                    });
                  },
                )).toList(),
              ),
            ),

            const SizedBox(height: 16),

            // 角色昵称输入
            const Text(
              '角色昵称',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              decoration: InputDecoration(
                hintText: '请输入角色昵称',
                border: const OutlineInputBorder(),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              onChanged: (value) {
                _characterName = value.isNotEmpty ? value : '角色昵称';
              },
            ),

            const SizedBox(height: 16),

            // 角色等级选择
            const Text(
              '角色等级',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 120,
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 5,
                  childAspectRatio: 2,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: 20, // 显示1-20级作为示例
                itemBuilder: (context, index) {
                  String level = '${index + 1}级';
                  bool isSelected = _characterLevel == level;
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _characterLevel = level;
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected ? Colors.orange : Colors.grey[200],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Center(
                        child: Text(
                          level,
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.black,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),

            const Spacer(),

            // 确定按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  setState(() {
                    // 更新状态已在上面的选择中完成
                  });
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: const Text(
                  '确定',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建签到日历区域
  Widget _buildCalendarSection() {
    return Container(
      color: Colors.white,
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'X月已累计签到${_signedDays}天',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.black,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 5,
                childAspectRatio: 1,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: 31,
              itemBuilder: (context, index) {
                return _buildCalendarItem(index + 1);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建单个签到日历项
  Widget _buildCalendarItem(int day) {
    bool isSigned = day <= _signedDays;
    bool isToday = day == _signedDays;

    return GestureDetector(
      onTap: () {
        if (day == _signedDays + 1) {
          // 可以签到
          _performSignIn(day);
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: isSigned ? Colors.orange[100] : Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSigned ? Colors.orange : Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 签到状态图标或礼品图标
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: isSigned ? Colors.orange : Colors.grey[200],
                borderRadius: BorderRadius.circular(4),
              ),
              child: isSigned
                  ? const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 20,
                    )
                  : const Icon(
                      Icons.card_giftcard,
                      color: Colors.grey,
                      size: 20,
                    ),
            ),
            const SizedBox(height: 4),
            Text(
              '累计${day}天',
              style: TextStyle(
                fontSize: 12,
                color: isSigned ? Colors.orange : Colors.grey,
                fontWeight: isSigned ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 执行签到
  void _performSignIn(int day) {
    setState(() {
      _signedDays = day;
    });

    // 显示签到成功提示
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('签到成功'),
        content: Text('恭喜您完成第${day}天签到！'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }






  /// 显示领取记录
  void _showReceiveRecords() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('领取记录'),
        content: const SizedBox(
          width: double.maxFinite,
          height: 300,
          child: Column(
            children: [
              Text('暂无领取记录'),
              // 这里可以添加实际的领取记录列表
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 显示规则
  void _showRules() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('签到规则'),
        content: const SizedBox(
          width: double.maxFinite,
          height: 300,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('1. 每日可签到一次，连续签到可获得更多奖励'),
                SizedBox(height: 8),
                Text('2. 签到奖励将直接发放到游戏内邮箱'),
                SizedBox(height: 8),
                Text('3. 每月签到周期为31天，次月重新开始'),
                SizedBox(height: 8),
                Text('4. 签到奖励包括金币、道具、装备等'),
                SizedBox(height: 8),
                Text('5. 如有疑问请联系客服'),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('我知道了'),
          ),
        ],
      ),
    );
  }
}