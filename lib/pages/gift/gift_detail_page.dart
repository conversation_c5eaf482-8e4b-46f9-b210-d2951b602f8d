import 'package:flutter/material.dart';
import '../../components/cache_image.dart';
import 'gift_detail_dialog.dart';
import 'gift_claim_dialog.dart';

class GiftDetailPage extends StatefulWidget {
  const GiftDetailPage({super.key});

  @override
  State<GiftDetailPage> createState() => _GiftDetailPageState();
}

class _GiftDetailPageState extends State<GiftDetailPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      appBar: AppBar(
        title: const Text(
          '礼包',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        centerTitle: true, // 标题居中
        backgroundColor: const Color(0xFFF8F8F8), // 与页面背景色一致
        foregroundColor: Colors.black,
        elevation: 0,
        scrolledUnderElevation: 0, // 滑动时不改变颜色
      ),
      body: _buildScrollableContent(),
    );
  }

  Widget _buildScrollableContent() {
    final gifts = [
      GiftItem(
        title: '玩心新手礼包（白银）',
        eligibility: 'V0-V16可领',
        contents: '补天灵珠*2,玄血丹*3,凝血丹*3',
        buttonText: '领取',
        buttonColor: Colors.orange,
        canClaim: true,
        remainingPercent: 97,
      ),
      GiftItem(
        title: '玩心每周礼包（白银）',
        eligibility: 'V0-V16可领',
        contents: '灵石*30,玄血丹*1,凝血丹*1',
        buttonText: '领取',
        buttonColor: Colors.orange,
        canClaim: true,
        remainingPercent: 92,
      ),
      GiftItem(
        title: '玩心新手礼包（黄金）',
        eligibility: 'V7-V16可领',
        contents: '补天灵珠*8,玄血丹*7,凝血丹*7',
        buttonText: '等级不足',
        buttonColor: Colors.grey,
        canClaim: false,
        remainingPercent: 99,
      ),
      GiftItem(
        title: '玩心新手礼包（钻石）',
        eligibility: 'V10-V16可领',
        contents: '补天灵珠*20,玄血丹*6,凝血丹*6,灵...',
        buttonText: '等级不足',
        buttonColor: Colors.grey,
        canClaim: false,
        remainingPercent: 99,
      ),
      GiftItem(
        title: '玩心每周礼包（黑金）',
        eligibility: 'V13-V16可领',
        contents: '补天灵珠*3,深体精髓*8,玄血丹*8',
        buttonText: '等级不足',
        buttonColor: Colors.grey,
        canClaim: false,
        remainingPercent: 99,
      ),
      GiftItem(
        title: '玩心每周礼包（黄金）',
        eligibility: 'V7-V16可领',
        contents: '补天灵珠*1,深体精髓*1,玄血丹*1',
        buttonText: '等级不足',
        buttonColor: Colors.grey,
        canClaim: false,
        remainingPercent: 98,
      ),
      GiftItem(
        title: '玩心每周礼包（钻石）',
        eligibility: 'V10-V16可领',
        contents: '补天灵珠*1,深体精髓*1,玄血丹*1',
        buttonText: '等级不足',
        buttonColor: Colors.grey,
        canClaim: false,
        remainingPercent: 99,
      ),
    ];

    return ListView.builder(
      padding: EdgeInsets.zero,
      itemCount: gifts.length + 1, // +1 for header
      itemBuilder: (context, index) {
        if (index == 0) {
          return _buildGameHeader();
        }
        return _buildGiftItem(gifts[index - 1]);
      },
    );
  }

  Widget _buildGameHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 16),
      color: const Color(0xFFF8F8F8), // 浅灰色背景
      child: Row(
        children: [
          // 游戏图标
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: CachedImage(
                imageUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
                placeholder: Container(
                  color: Colors.grey[300],
                  child: const Icon(
                    Icons.games,
                    color: Colors.grey,
                    size: 30,
                  ),
                ),
                errorWidget: Container(
                  color: Colors.grey[300],
                  child: const Icon(
                    Icons.error_outline,
                    color: Colors.grey,
                    size: 30,
                  ),
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // 游戏信息
          Expanded(
            child: Row(
              children: [
                // 游戏标题
                const Expanded(
                  child: Text(
                    '凡人修仙传：人界篇',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // 区服选择器
                GestureDetector(
                  onTap: () => _handleServerCharacterSelect(),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '区服+角色名',
                        style: TextStyle(
                          fontSize: 13,
                          color: Color(0xFFF97727),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(width: 6),
                      Icon(
                        Icons.keyboard_arrow_down,
                        size: 16,
                        color: Color(0xFFF97727),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGiftItem(GiftItem gift) {
    return GestureDetector(
      onTap: () => _showGiftDetailDialog(gift),
      child: Container(
        margin: const EdgeInsets.fromLTRB(16, 0, 16, 8),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFFFFF4E6), // 浅橙色背景
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
        children: [
          // 左侧内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题
                Text(
                  gift.title,
                  style: const TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),

                const SizedBox(height: 4),

                // 领取条件
                Text(
                  gift.eligibility,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),

                const SizedBox(height: 4),

                // 礼包内容
                Text(
                  gift.contents,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          const SizedBox(width: 12),

          // 右侧内容
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 按钮
              SizedBox(
                width: 100,
                height: 34,
                child: ElevatedButton(
                  onPressed: gift.canClaim ? () {
                    _handleClaimGift(gift.title);
                  } : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: gift.canClaim ? const Color(0xFFF97727) : Colors.grey[400],
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    padding: EdgeInsets.zero,
                    elevation: 0,
                  ),
                  child: Text(
                    gift.buttonText,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 8),

              // 剩余百分比
              Text(
                '剩余${gift.remainingPercent}%',
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFFF97727),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    ),
    );
  }

  void _showGiftDetailDialog(GiftItem gift) {
    GiftDetailDialog.show(
      context,
      title: gift.title,
      contents: gift.contents,
      eligibility: '${gift.eligibility} （剩余 ${_getRandomNumber()}件）',
      claimTime: '2023-05-24 09:30:00-2050-12-31 00:00:00',
      rules: [
        '该礼包每个账号终身仅能领取一次；',
        '领取的角色需要本周至少在游戏内登录过1次；',
        '该礼包非本公众号独有，全渠道仅可领一次。',
      ],
      canClaim: gift.canClaim,
      buttonText: gift.buttonText,
      onClaim: () => _handleClaimGift(gift.title),
    );
  }

  String _getRandomNumber() {
    // 模拟随机剩余数量
    final numbers = ['99991779', '97979175', '88888888', '********'];
    return numbers[(DateTime.now().millisecondsSinceEpoch % numbers.length)];
  }

  void _handleClaimGift(String giftTitle) {
    // 显示角色选择弹窗
    CharacterSelectDialog.show(
      context,
      characters: _getMockCharacters(),
      onConfirm: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('领取礼包成功：$giftTitle'),
            duration: const Duration(seconds: 2),
          ),
        );
      },
      onSwitchAccount: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('切换账号功能'),
            duration: Duration(seconds: 2),
          ),
        );
      },
    );
  }

  void _handleServerCharacterSelect() {
    // 显示角色选择弹窗
    CharacterSelectDialog.show(
      context,
      characters: _getMockCharacters(),
      onConfirm: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('角色选择成功'),
            duration: Duration(seconds: 2),
          ),
        );
      },
      onSwitchAccount: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('切换账号功能'),
            duration: Duration(seconds: 2),
          ),
        );
      },
    );
  }

  List<GitClaimInfo> _getMockCharacters() {
    return [
      const GitClaimInfo(
        name: '蛋仔551wggDQ',
        serverInfo: '正式服',
        canReceive: true,
      ),
      const GitClaimInfo(
        name: '槐梦青鱼',
        serverInfo: '正式服',
        canReceive: false,
        statusText: '非当前账号角色',
        statusDetail: '切换至132****2080',
      ),
      const GitClaimInfo(
        name: '小一班第一2',
        serverInfo: '正式服',
        canReceive: false,
        statusText: '非官服角色',
        statusDetail: '暂不支持领取会员礼包',
      ),
    ];
  }
}

// 礼包数据模型
class GiftItem {
  final String title;
  final String eligibility;
  final String contents;
  final String buttonText;
  final Color buttonColor;
  final bool canClaim;
  final int remainingPercent;

  const GiftItem({
    required this.title,
    required this.eligibility,
    required this.contents,
    required this.buttonText,
    required this.buttonColor,
    required this.canClaim,
    required this.remainingPercent,
  });
} 