import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../splash/SplashPage.dart';
import 'privacy_manager.dart';

class PrivacyCheckPage extends StatefulWidget {
  const PrivacyCheckPage({super.key});

  @override
  State<PrivacyCheckPage> createState() => _PrivacyCheckPageState();
}

class _PrivacyCheckPageState extends State<PrivacyCheckPage> {
  @override
  void initState() {
    super.initState();
    _checkPrivacyPolicy();
  }

  void _checkPrivacyPolicy() async {
    // 等待一个短暂的时间，确保页面渲染完成
    await Future.delayed(const Duration(milliseconds: 300));
    
    if (mounted) {
      // 检查并处理隐私政策
      final privacyResult = await PrivacyManager.checkAndHandlePrivacy(context);
      
      if (privacyResult && mounted) {
        // 用户同意隐私政策，跳转到闪屏页
        Navigator.of(context).pushReplacement(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) {
              return const SplashPage();
            },
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return FadeTransition(opacity: animation, child: child);
            },
            transitionDuration: const Duration(milliseconds: 500),
          ),
        );
      }
      // 如果用户不同意，PrivacyManager 会自动退出应用
    }
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: Colors.white,
      body: SizedBox.shrink(), // 空白页面，不显示任何内容
    );
  }
} 