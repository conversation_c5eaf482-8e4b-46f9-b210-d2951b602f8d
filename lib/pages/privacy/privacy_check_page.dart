import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../push/sq_push_manager.dart';
import '../../track/track.dart';
import '../splash/SplashPage.dart';
import 'privacy_manager.dart';

class PrivacyCheckPage extends StatefulWidget {
  const PrivacyCheckPage({super.key});

  @override
  State<PrivacyCheckPage> createState() => _PrivacyCheckPageState();
}

class _PrivacyCheckPageState extends State<PrivacyCheckPage> {
  @override
  void initState() {
    super.initState();
    _checkPrivacyPolicy();
  }

  void _checkPrivacyPolicy() async {
    // 1) 先检查是否已同意
    final isAgreed = await PrivacyManager.isPrivacyAgreed();
    if (isAgreed) {
      _goSplash();
      return;
    }

    // 2) 需要展示隐私弹窗时，先拉取隐私协议配置；失败则给出重试弹窗
    bool loaded = await _loadPrivacyWithRetry();
    if (!mounted) return;
    if (!loaded) {
      // 用户取消重试，停留在当前页
      return;
    }

    // 3) 展示隐私弹窗
    final result = await PrivacyManager.showPrivacyDialog(context);
    if (!mounted) return;
    if (result == true) {
      await PrivacyManager.setPrivacyAgreed(true);
      _goSplash();
    }
  }

  Future<bool> _loadPrivacyWithRetry() async {
    while (mounted) {
      try {
        await PrivacyManager.loadPrivacy();
        // 基于已加载到的 URL 判断是否成功
        final ok = PrivacyManager.userAgreementUrl.isNotEmpty || PrivacyManager.privacyPolicyUrl.isNotEmpty;
        if (ok) return true;
      } catch (_) {}

      final retry = await _showFetchErrorDialog();
      if (retry != true) return false;
    }
    return false;
  }

  Future<bool?> _showFetchErrorDialog() {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return AlertDialog(
          title: const Text('提示'),
          content: const Text('请求隐私协议失败，是否重试？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  void _goSplash() {
    _afterPermissionGranted();
    Navigator.of(context).pushReplacementNamed('/splash');
  }

  Future<void> _afterPermissionGranted() async {
    // 同意隐私权限
    await SqTrackManager.init();
    SqPushManager().init(context);
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: Colors.white,
      body: SizedBox.shrink(), // 空白页面，不显示任何内容
    );
  }
} 