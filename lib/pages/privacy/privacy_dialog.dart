import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../webview/webview_page.dart';
import 'privacy_manager.dart';

// 确认弹窗组件
class ConfirmExitDialog extends StatelessWidget {
  const ConfirmExitDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(horizontal: 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Container(
              width: double.infinity,
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
              child: const Text(
                '温馨提示',
                style: TextStyle(
                  fontSize: 17,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            
            // 内容
            Padding(
              padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
              child: RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                    height: 1.4,
                  ),
                  children: [
                    const TextSpan(text: '不授权同意'),
                    WidgetSpan(
                      child: GestureDetector(
                        onTap: () => _openWebView(context, '用户协议', 'https://example.com/user-agreement'),
                        child: const Text(
                          '《用户协议》',
                          style: TextStyle(
                            color: Colors.blue,
                            decoration: TextDecoration.underline,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                    const TextSpan(text: '和'),
                    WidgetSpan(
                      child: GestureDetector(
                        onTap: () => _openWebView(context, '隐私政策', 'https://example.com/privacy-policy'),
                        child: const Text(
                          '《隐私政策》',
                          style: TextStyle(
                            color: Colors.blue,
                            decoration: TextDecoration.underline,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                    const TextSpan(text: '将无法进行游戏'),
                  ],
                ),
              ),
            ),
            
            // 副标题 - 合并为一行
            Padding(
              padding: const EdgeInsets.fromLTRB(15, 0, 15, 5),
              child: RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.4,
                  ),
                  children: [
                    const TextSpan(
                      text: '是否确认',
                      style: TextStyle(color: Colors.black87),
                    ),
                    TextSpan(
                      text: '取消授权并退出游戏',
                      style: TextStyle(color: Colors.red[600]),
                    ),
                  ],
                ),
              ),
            ),
            
            // 底部按钮
            Container(
              padding: const EdgeInsets.fromLTRB(20, 10, 20, 20),
              child: Row(
                children: [
                  // 取消授权按钮
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        Navigator.of(context).pop(false);
                        // 延迟退出应用，让弹窗先关闭
                        Future.delayed(const Duration(milliseconds: 100), () {
                          SystemNavigator.pop();
                        });
                      },
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Color(0xFFDDDDDD)),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        backgroundColor: Colors.white,
                      ),
                      child: const Text(
                        '取消授权',
                        style: TextStyle(
                          color: Colors.black87,
                          fontSize: 16,
                          // fontWeight: FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  // 我再想想按钮
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop(true); // 关闭确认弹窗，返回隐私协议弹窗
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        '我再想想',
                        style: TextStyle(
                          fontSize: 16,
                          // fontWeight: FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 打开WebView
  void _openWebView(BuildContext context, String title, String url) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewPage(
          title: title,
          url: url,
        ),
      ),
    );
  }
}

class PrivacyDialog extends StatelessWidget {
  const PrivacyDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: const BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Color(0xFFEEEEEE),
                      width: 1,
                    ),
                  ),
                ),
                child: const Text(
                  '个人信息保护指引',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              
              // 内容区域
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 欢迎信息
                      RichText(
                        text: TextSpan(
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.black87,
                            height: 1.5,
                          ),
                          children: [
                            const TextSpan(text: '欢迎下载本应用,我们非常重视个人信息和隐私保护。请在使用我们的服务前,详细阅读并同意'),
                            WidgetSpan(
                              child: GestureDetector(
                                onTap: () => _openWebView(context, '用户协议', 'https://example.com/user-agreement'),
                                child: const Text(
                                  '《用户协议》',
                                  style: TextStyle(
                                    color: Colors.blue,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                              ),
                            ),
                            const TextSpan(text: '和'),
                            WidgetSpan(
                              child: GestureDetector(
                                onTap: () => _openWebView(context, '隐私政策', 'https://example.com/privacy-policy'),
                                child: const Text(
                                  '《隐私政策》',
                                  style: TextStyle(
                                    color: Colors.blue,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                              ),
                            ),
                            const TextSpan(text: '。'),
                          ],
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // 权限说明
                      const Text(
                        '为了提供完整的应用体验,我们会向您申请必要的权限和信息。您可选择同意或拒绝权限申请,如果拒绝可能会导致部分应用体验异常。权限和信息包括:',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                          height: 1.5,
                        ),
                      ),
                      
                      const SizedBox(height: 10),
                      
                      // 权限列表
                      _buildPermissionItem(
                        '存储权限:',
                        '为实现查询历史账号功能,保存玩家登录信息,及图片的保存与分享。',
                      ),
                      
                      const SizedBox(height: 10),
                      
                      _buildPermissionItem(
                        '通知权限:',
                        '为实现游戏攻略等信息推送。',
                      ),
                    ],
                  ),
                ),
              ),
              
              // 底部按钮
              Container(
                padding: const EdgeInsets.fromLTRB(20, 10, 20, 20),
                child: Row(
                  children: [
                    // 不同意按钮
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => _disagree(context),
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: Color(0xFFDDDDDD)),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          '不同意',
                          style: TextStyle(
                            color: Colors.black87,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                    
                    const SizedBox(width: 12),
                    
                    // 同意按钮
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _agree(context),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          '同意',
                          style: TextStyle(
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建权限项
  Widget _buildPermissionItem(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          content,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black54,
            height: 1.4,
          ),
        ),
      ],
    );
  }

  // 打开WebView
  void _openWebView(BuildContext context, String title, String url) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewPage(
          title: title,
          url: url,
        ),
      ),
    );
  }

  // 同意处理
  void _agree(BuildContext context) {
    // 保存用户同意状态并关闭弹窗
    Navigator.of(context).pop(true);
  }

  // 不同意处理
  void _disagree(BuildContext context) async {
    // 显示确认弹窗，不关闭当前隐私弹窗
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const ConfirmExitDialog();
      },
    );
    
    // 根据确认弹窗的结果处理
    if (result == false) {
      // 用户选择"取消授权"，关闭隐私弹窗并退出应用
      Navigator.of(context).pop(false);
    }
    // 如果用户选择"我再想想"（result == true），什么都不做，继续显示当前隐私弹窗
  }
} 