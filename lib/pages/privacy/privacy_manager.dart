import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'privacy_dialog.dart';

class PrivacyManager {
  static const String _privacyAgreedKey = 'privacy_agreed';
  
  /// 检查隐私政策是否已同意
  static Future<bool> isPrivacyAgreed() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_privacyAgreedKey) ?? false;
  }
  
  /// 保存隐私政策同意状态
  static Future<void> setPrivacyAgreed(bool agreed) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_privacyAgreedKey, agreed);
  }
  
  /// 显示隐私弹窗
  static Future<bool?> showPrivacyDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false, // 禁止点击外部关闭
      builder: (BuildContext context) {
        return const PrivacyDialog();
      },
    );
  }
  
  /// 检查并处理隐私政策
  static Future<bool> checkAndHandlePrivacy(BuildContext context) async {
    // 检查是否已同意隐私政策
    final isAgreed = await isPrivacyAgreed();
    
    if (!isAgreed) {
      // 显示隐私弹窗
      final result = await showPrivacyDialog(context);
      
      if (result == true) {
        // 用户同意，保存状态
        await setPrivacyAgreed(true);
        return true;
      } else {
        // 用户不同意或取消，退出应用
        return false;
      }
    }
    
    return true;
  }
} 