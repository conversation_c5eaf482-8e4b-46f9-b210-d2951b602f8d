import 'package:dlyz_flutter/net/http_base_config.dart';
import 'package:dlyz_flutter/pages/community/Information_content.dart';
import 'package:dlyz_flutter/pages/voucher/voucher_page.dart';
import 'package:flutter/material.dart';
import '../guide/guide_page.dart';
import '../wallpaper/wallpaper_download_page.dart';
import '../gift/gift_center_page.dart';
import '../bind/character_bind_page.dart';
import '../bind/switch_bind_character_page.dart';
import 'recommend_content.dart';
import 'empty_page.dart';
import 'banner_carousel.dart';
import 'forum_post_list_page.dart';
import '../../net/api/forum_service.dart';
import '../../model/forum_category.dart';

class CommunityPage extends StatefulWidget {
  const CommunityPage({super.key});

  @override
  State<CommunityPage> createState() => _CommunityPageState();
}

class _CommunityPageState extends State<CommunityPage> with TickerProviderStateMixin {
  int _selectedTabIndex = 0;
  List<ForumCategory> _categories = [];
  bool _isLoading = true;
  String? _error;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    try {
      final forumService = ForumService();
      final response = await forumService.getCategories(
        baseUrl: HttpBaseConfig.forumBaseUrl, // 替换为实际的API域名
      );

      if (response.code == 0 && response.data != null) {
        setState(() {
          _categories = response.data!;
          _isLoading = false;
          _initTabController();
        });
      } else {
        setState(() {
          _error = response.message ?? '获取分类失败';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = '网络错误: $e';
        _isLoading = false;
      });
    }
  }

  void _initTabController() {
    _tabController = TabController(
      length: _categories.length,
      vsync: this,
      initialIndex: _selectedTabIndex,
    );
    _tabController.addListener(() {
      setState(() {
        _selectedTabIndex = _tabController.index;
      });
    });
    
    // 确保第一个tab在TabController初始化后立即刷新
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _categories.isNotEmpty) {
        // 通过setState触发TabBarView重建，确保第一个tab正确初始化
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: Column(
          children: [
            // 固定内容区域
            _buildHeader(),
            _buildUserSection(),
            // 可滚动的内容区域
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _error != null
                      ? _buildErrorWidget()
                      : NestedScrollView(
                          headerSliverBuilder: (context, innerBoxIsScrolled) {
                            return [
                              // 可折叠的功能按钮和活动banner
                              SliverToBoxAdapter(child: _buildFunctionButtons()),
                              SliverToBoxAdapter(child: _buildActivityBanner()),
                              // 吸顶的Tab栏
                              SliverPersistentHeader(
                                pinned: true,
                                delegate: _SliverAppBarDelegate(
                                  _buildContentTabs(),
                                  preferredHeight: 50,
                                ),
                              ),
                            ];
                          },
                          body: _buildContentArea(),
                        ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _error ?? '加载失败',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _isLoading = true;
                _error = null;
              });
              _loadCategories();
            },
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back_ios, size: 20),
            onPressed: () {},
          ),
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Colors.blue[100],
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(Icons.games, color: Colors.blue, size: 20),
          ),
          const SizedBox(width: 8),
          const Expanded(
            child: Text(
              '斗罗大陆:魂师对决',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.search, size: 24),
            onPressed: () {},
          ),
        ],
      ),
    );
  }

  Widget _buildUserSection() {
    return GestureDetector(
      onTap: _navigateToSwitchBindCharacter,
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            GestureDetector(
              onTap: _navigateToSwitchBindCharacter,
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(24),
                ),
                child: const Icon(Icons.person, color: Colors.grey, size: 24),
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '未绑定角色',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    '绑定角色领礼包',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: _navigateToCharacterBinding,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Text(
                  '绑定角色领礼包',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFunctionButtons() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildFunctionButton('必得体验金', '可领取', Colors.orange, () {
            _handleFunctionButtonClick('必得体验金');
          }),
          _buildFunctionButton('代金券', '', Colors.blue, () {
            _handleFunctionButtonClick('代金券');
          }),
          _buildFunctionButton('礼包中心', '', Colors.green, () {
            _handleFunctionButtonClick('礼包中心');
          }),
          _buildFunctionButton('积分商城', '', Colors.purple, () {
            _handleFunctionButtonClick('积分商城');
          }),
        ],
      ),
    );
  }

  Widget _buildFunctionButton(String title, String subtitle, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Icon(Icons.card_giftcard, color: color, size: 24),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
          ),
          if (subtitle.isNotEmpty)
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 10,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildContentTabs() {
    if (_categories.isEmpty) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: _categories.map((category) => Tab(text: category.name)).toList(),
        labelColor: Colors.blue,
        unselectedLabelColor: Colors.grey[600],
        labelStyle: const TextStyle(fontWeight: FontWeight.bold),
        unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.normal),
        indicatorColor: Colors.blue,
        indicatorWeight: 2,
      ),
    );
  }

  Widget _buildContentArea() {
    if (_categories.isEmpty) return const SizedBox.shrink();
    
    return TabBarView(
      controller: _tabController,
      // 添加 physics 属性确保所有页面都能正常滑动
      physics: const ClampingScrollPhysics(),
      children: _categories.asMap().entries.map((entry) {
        int index = entry.key;
        ForumCategory category = entry.value;
        
        // 根据分类名称返回不同的页面内容
        return _getContentForCategory(category, index);
      }).toList(),
    );
  }

  Widget _getContentForCategory(ForumCategory category, int index) {
    // 为每个页面创建唯一的key，确保页面能正确重建
    return ForumPostListPage(
      key: ValueKey('forum_${category.categoryId}'),
      category: category,
    );
  }



  void _navigateToCharacterBinding() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CharacterBindPage(
          phoneNumber: '138****8888', // 替换为实际的手机号
        ),
      ),
    );
  }

  void _navigateToSwitchBindCharacter() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SwitchBindCharacterPage(),
      ),
    );
  }

  void _handleFunctionButtonClick(String buttonName) {
    switch (buttonName) {
      case '必得体验金':
        _showComingSoonDialog('必得体验金');
        break;
      case '代金券':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const VoucherPage(),
          ),
        );
        break;
      case '礼包中心':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const GiftCenterPage(),
          ),
        );
        break;
      case '积分商城':
        _showComingSoonDialog('积分商城');
        break;
    }
  }

  void _showComingSoonDialog(String featureName) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('功能开发中'),
          content: Text('$featureName 功能正在开发中，敬请期待！'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildActivityBanner() {
    // 模拟轮播图数据
    final List<BannerItem> banners = [
      const BannerItem(
        title: '新版本上线',
        subtitle: '全新玩法等你体验',
        backgroundColor: Colors.blue,
      ),
      const BannerItem(
        title: '限时活动',
        subtitle: '参与活动赢取大奖',
        backgroundColor: Colors.orange,
      ),
      const BannerItem(
        title: '每日签到',
        subtitle: '连续签到送好礼',
        backgroundColor: Colors.green,
      ),
      const BannerItem(
        title: 'VIP特权',
        subtitle: '开通VIP享受专属福利',
        backgroundColor: Colors.purple,
      ),
    ];

    return BannerCarousel(
      banners: banners,
      height: 120,
      autoPlayInterval: const Duration(seconds: 3),
      showIndicator: true,
    );
  }


}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double preferredHeight;

  _SliverAppBarDelegate(this.child, {required this.preferredHeight});

  @override
  double get minExtent => preferredHeight;

  @override
  double get maxExtent => preferredHeight;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Colors.grey[50],
      child: child,
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}