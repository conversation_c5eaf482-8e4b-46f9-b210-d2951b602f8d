import 'package:dlyz_flutter/net/http_base_config.dart';
import 'package:dlyz_flutter/pages/voucher/voucher_page.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:convert';
import '../../info/forum_info.dart';
import '../gift/gift_center_page.dart';
import '../bind/character_bind_page.dart';
import '../bind/switch_bind_character_page.dart';
import 'forum_post_list_page.dart';
import 'forum_post_waterfall_list_page.dart';
import '../../net/api/forum_service.dart';
import '../../model/forum_category.dart';
import '../../model/act_module.dart';
import '../../model/forum_post_list.dart';
import 'community_detail_page.dart';

class CommunityPage extends StatefulWidget {
  /// 是否显示游戏画廊模式
  /// true: 显示游戏画廊页面 (forum_post_waterfall_list_page.dart)
  /// false: 显示论坛帖子列表 (forum_post_list_page.dart)
  final bool showGameGallery;
  
  const CommunityPage({super.key, this.showGameGallery = true});

  @override
  State<CommunityPage> createState() => _CommunityPageState();
}

class _CommunityPageState extends State<CommunityPage> with TickerProviderStateMixin {
  int _selectedTabIndex = 0;
  List<ForumCategory> _categories = [];
  List<ActModule> _activityModules = [];
  bool _isLoading = true;
  String? _error;
  TabController? _tabController;
  // 预缓存的指示器图片
  final AssetImage _tabIndicatorAsset = const AssetImage('assets/images/tab_indicator.png');
  
  // 功能图标滑动控制器和进度
  final ScrollController _functionScrollController = ScrollController();
  double _scrollProgress = 0.0;

  // 缓存已构建的tab页面
  final Map<int, Widget> _cachedTabPages = {};

  @override
  void initState() {
    super.initState();
    _loadCategories();
    _loadActivityModules();
    _initScrollListener();
    
    // 添加一个定时器来定期检查ForumInfo的状态
    _setupForumInfoListener();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 预加载Tab指示器图片，保证首次展示时即可绘制
    precacheImage(_tabIndicatorAsset, context);
  }
  
  void _setupForumInfoListener() {
    // 每隔1秒检查一次ForumInfo是否已经初始化
    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      
      final forumInfo = ForumInfo();
      if (forumInfo.isInitialized && forumInfo.forumConfig != null) {
        final modules = forumInfo.actModules;
        if (modules.isNotEmpty && _activityModules.isEmpty) {
          print('Timer: Found ${modules.length} activity modules, updating UI');
          setState(() {
            _activityModules = modules;
          });
          timer.cancel(); // 数据加载完成后停止定时器
        }
      }
    });
  }

  void _initScrollListener() {
    _functionScrollController.addListener(() {
      if (_functionScrollController.hasClients) {
        final maxScrollExtent = _functionScrollController.position.maxScrollExtent;
        final currentScroll = _functionScrollController.offset;
        
        if (maxScrollExtent > 0) {
          final newProgress = (currentScroll / maxScrollExtent).clamp(0.0, 1.0);
          if (_scrollProgress != newProgress) {
            setState(() {
              _scrollProgress = newProgress;
            });
          }
        } else {
          // 当没有滚动内容时，重置进度为0
          if (_scrollProgress != 0.0) {
            setState(() {
              _scrollProgress = 0.0;
            });
          }
        }
      }
    });
  }

  Future<void> _loadCategories() async {
    try {
      final forumService = ForumService();
      final response = await forumService.getCategories(
        baseUrl: HttpBaseConfig.forumBaseUrl, // 替换为实际的API域名
      );

      if (response.code == 0 && response.data != null) {
        setState(() {
          _categories = response.data!;
          _isLoading = false;
          _initTabController();
        });
      } else {
        setState(() {
          _error = response.message ?? '获取分类失败';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = '网络错误: $e';
        _isLoading = false;
      });
    }
  }

  void _loadActivityModules() {
    // 从ForumInfo获取活动模块数据
    final forumInfo = ForumInfo();
    if (forumInfo.isInitialized && forumInfo.forumConfig != null) {
      final modules = forumInfo.actModules;
      print('ForumInfo actModules count: ${modules.length}'); // 调试日志
      setState(() {
        _activityModules = modules;
      });
    } else {
      print('ForumInfo not initialized yet, waiting...'); // 调试日志
      // 如果ForumInfo还没有初始化，等待一段时间后再尝试
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _loadActivityModules();
        }
      });
    }
  }

  void _initTabController() {
    if (_categories.isEmpty) {
      // 无分类时不初始化，避免空指针
      return;
    }
    _tabController = TabController(
      length: _categories.length,
      vsync: this,
      initialIndex: _selectedTabIndex.clamp(0, _categories.length - 1),
    );
    _tabController!.addListener(() {
      if (!mounted) return;
      setState(() {
        _selectedTabIndex = _tabController!.index;
      });
    });
    
    // 确保第一个tab在TabController初始化后立即刷新
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _categories.isNotEmpty && _tabController != null) {
        // 通过setState触发TabBarView重建，确保第一个tab正确初始化
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _tabController?.dispose();
    _functionScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: Column(
          children: [
            // 固定内容区域
            _buildHeader(),
            _buildUserSection(),
            // 可滚动的内容区域
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _error != null
                      ? _buildErrorWidget()
                      : NestedScrollView(
                          headerSliverBuilder: (context, innerBoxIsScrolled) {
                            return [
                              // 可折叠的功能按钮和活动banner
                              SliverToBoxAdapter(child: _buildFunctionButtons()),
                              SliverToBoxAdapter(child: _buildActivityBanner()),
                              // 吸顶的Tab栏
                              SliverPersistentHeader(
                                pinned: true,
                                delegate: _SliverAppBarDelegate(
                                  _buildContentTabs(),
                                  preferredHeight: 60,
                                ),
                              ),
                            ];
                          },
                          body: _buildContentArea(),
                        ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _error ?? '加载失败',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _isLoading = true;
                _error = null;
              });
              _loadCategories();
            },
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back_ios, size: 20),
            onPressed: () {},
          ),
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Colors.blue[100],
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(Icons.games, color: Colors.blue, size: 20),
          ),
          const SizedBox(width: 8),
          const Expanded(
            child: Text(
              '斗罗大陆:魂师对决',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.search, size: 24),
            onPressed: () {},
          ),
        ],
      ),
    );
  }

  Widget _buildUserSection() {
    return GestureDetector(
      onTap: _navigateToSwitchBindCharacter,
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            GestureDetector(
              onTap: _navigateToSwitchBindCharacter,
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(24),
                ),
                child: const Icon(Icons.person, color: Colors.grey, size: 24),
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '未绑定角色',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    '绑定角色领礼包',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: _navigateToCharacterBinding,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Text(
                  '绑定角色领礼包',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFunctionButtons() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildFunctionButton('必得体验金', '可领取', Colors.orange, () {
            _handleFunctionButtonClick('必得体验金');
          }),
          _buildFunctionButton('代金券', '', Colors.blue, () {
            _handleFunctionButtonClick('代金券');
          }),
          _buildFunctionButton('礼包中心', '', Colors.green, () {
            _handleFunctionButtonClick('礼包中心');
          }),
          _buildFunctionButton('积分商城', '', Colors.purple, () {
            _handleFunctionButtonClick('积分商城');
          }),
        ],
      ),
    );
  }

  Widget _buildFunctionButton(String title, String subtitle, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Icon(Icons.card_giftcard, color: color, size: 24),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
          ),
          if (subtitle.isNotEmpty)
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 10,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildContentTabs() {
    if (_categories.isEmpty || _tabController == null) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabAlignment: TabAlignment.start,
        tabs: _categories
            .map((category) => Tab(
                  text: category.name,
                ))
            .toList(),
        labelColor: Colors.black87,
        unselectedLabelColor: Colors.grey[600],
        labelStyle: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 16,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.normal,
          fontSize: 16,
        ),
        // 使用自定义图片指示器，自动跟随选中tab并居中显示
        indicator: ImageTabIndicator(
          image: _tabIndicatorAsset,
          imageSize: const Size(24, 8),
          bottomPadding: 4,
        ),
        dividerColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        overlayColor: MaterialStateProperty.all(Colors.transparent),
        labelPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }

  Widget _buildContentArea() {
    if (_categories.isEmpty || _tabController == null) return const SizedBox.shrink();
    
    return TabBarView(
      controller: _tabController,
      // 添加 physics 属性确保所有页面都能正常滑动
      physics: const ClampingScrollPhysics(),
      children: _categories.asMap().entries.map((entry) {
        int index = entry.key;
        ForumCategory category = entry.value;
        
        // 根据分类名称返回不同的页面内容
        return _getContentForCategory(category, index);
      }).toList(),
    );
  }

  Widget _getContentForCategory(ForumCategory category, int index) {
    // 检查缓存中是否已经有这个页面
    if (_cachedTabPages.containsKey(index)) {
      return _cachedTabPages[index]!;
    }

    // 使用 FutureBuilder 获取并构建头部组件后，传入帖子页面
    final forumService = ForumService();
    final future = forumService.getActLabel(
      baseUrl: HttpBaseConfig.forumBaseUrl,
      categoryId: category.categoryId,
    );

    final pageWidget = FutureBuilder(
      future: future,
      builder: (context, snapshot) {
        List<Widget> headerWidgets = [];
        if (snapshot.connectionState == ConnectionState.done && snapshot.hasData) {
          final resp = snapshot.data!;
          if (resp.code == 0 && resp.data != null) {
            headerWidgets = _buildHeaderWidgetsFromActLabels(resp.data!);
          }
        }

        if (widget.showGameGallery) {
          return ForumPostWaterfallListPage(
            key: ValueKey('waterfall_${category.categoryId}'),
            category: category,
            headerWidgets: headerWidgets,
          );
        } else {
          return ForumPostListPage(
            key: ValueKey('forum_${category.categoryId}'),
            category: category,
            headerWidgets: headerWidgets,
          );
        }
      },
    );

    _cachedTabPages[index] = pageWidget;
    return pageWidget;
  }

  List<Widget> _buildHeaderWidgetsFromActLabels(List<Map<String, dynamic>> labels) {
    // 按 sort 升序
    labels.sort((a, b) => (a['sort'] ?? 0).compareTo(b['sort'] ?? 0));
    final widgets = <Widget>[];
    for (final item in labels) {
      final style = (item['style_name'] ?? '').toString();
      final title = (item['title'] ?? '').toString();
      final count = (item['count'] ?? 0) is int ? item['count'] as int : int.tryParse(item['count'].toString()) ?? 0;
      final contentStr = (item['content'] ?? '[]').toString();
      List<dynamic> contentList = [];
      try {
        contentList = json.decode(contentStr) as List<dynamic>;
      } catch (_) {}

      if (style == '推荐帖子列表') {
        widgets.add(_buildRecommendList(title, contentList));
      } else if (style == '活动卡片') {
        widgets.add(_buildActivityCards(title, contentList, count));
      } else if (style == '话题卡片') {
        widgets.add(_buildTopicCards(title, contentList, count));
      }
    }
    return widgets;
  }

  Widget _sectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
      child: Row(
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.black87),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendList(String title, List<dynamic> items) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _sectionTitle(title),
          Container(
            padding: const EdgeInsets.only(left: 12,right: 12,top: 12,bottom: 2),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(color: Colors.black.withOpacity(0.04), blurRadius: 8, offset: const Offset(0, 2)),
              ],
            ),
            child: Column(
              children: items.map((e) {
                final map = Map<String, dynamic>.from(e as Map);
                final header = (map['header'] ?? '').toString();
                final display = (map['display'] ?? '').toString();
                final link = (map["link"] ?? '').toString();
                return Container(
                  margin: const EdgeInsets.only(bottom: 10),
                  child: GestureDetector(
                    onTap: () => _openThreadFromLink(link, title: display),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.transparent,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(color: Colors.orange, width: 1),
                          ),
                          child: Text(
                            header,
                            style: const TextStyle(
                              color: Colors.orange,
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            display,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(fontSize: 13, color: Colors.black87, fontWeight: FontWeight.w500),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Color _tagColor(String header) {
    switch (header) {
      case '最新':
        return Colors.red;
      case '攻略':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  Widget _buildActivityCards(String title, List<dynamic> items, int count) {
    final perRow = count.clamp(1, 5);
    final rows = <List<Map<String, dynamic>>>[];
    final parsed = items.map((e) => Map<String, dynamic>.from(e as Map)).toList();
    for (int i = 0; i < parsed.length; i += perRow) {
      rows.add(parsed.sublist(i, (i + perRow > parsed.length) ? parsed.length : i + perRow));
    }
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _sectionTitle(title),
          Column(
            children: rows.map((row) {
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    ...row.map((m) => Expanded(
                          child: GestureDetector(
                            onTap: () => _openThreadFromLink((m['link'] ?? '').toString(), title: (m['desc'] ?? '').toString()),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                // 纯图片卡片
                                Container(
                                  margin: const EdgeInsets.only(right: 8),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(8),
                                    boxShadow: [
                                      BoxShadow(color: Colors.black.withOpacity(0.04), blurRadius: 6, offset: const Offset(0, 1)),
                                    ],
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: AspectRatio(
                                      aspectRatio: 16 / 10,
                                      child: Image.network(
                                        (m['cover'] ?? '').toString(),
                                        fit: BoxFit.cover,
                                        errorBuilder: (c, e, s) => Container(color: Colors.grey[200]),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 6),
                                // 文本独立在图片下方
                                Text(
                                  (m['desc'] ?? '').toString(),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                                ),
                              ],
                            ),
                          ),
                        )),
                    // 填充空位
                    ...List.generate(perRow - row.length, (i) => const Expanded(child: SizedBox.shrink())),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTopicCards(String title, List<dynamic> items, int count) {
    final perRow = count.clamp(1, 5);
    final parsed = items.map((e) => Map<String, dynamic>.from(e as Map)).toList();
    final rows = <List<Map<String, dynamic>>>[];
    for (int i = 0; i < parsed.length; i += perRow) {
      rows.add(parsed.sublist(i, (i + perRow > parsed.length) ? parsed.length : i + perRow));
    }
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _sectionTitle(title),
          Column(
            children: rows.map((row) {
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    ...row.map((m) => Expanded(
                          child: GestureDetector(
                            onTap: () {
                              final link = (m['link'] ?? '').toString();
                              if (link.isNotEmpty) {
                                _openThreadFromLink(link, title: (m['topicName'] ?? '').toString());
                              }
                            },
                            child: Container(
                            margin: const EdgeInsets.only(right: 8),
                            height: 84,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(8),
                              boxShadow: [
                                BoxShadow(color: Colors.black.withOpacity(0.04), blurRadius: 6, offset: const Offset(0, 1)),
                              ],
                            ),
                            child: Padding(
                              padding: const EdgeInsets.only(left:0,top: 0,right: 10,bottom: 0),
                              child: Row(
                                children: [
                                  if ((m['cover'] ?? '').toString().isNotEmpty)
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(6),
                                      child: Image.network((m['cover'] ?? '').toString(), width: 40, height: 40, fit: BoxFit.cover,
                                          errorBuilder: (c, e, s) => Container(width: 40, height: 40, color: Colors.grey[200])),
                                    ),
                                  const SizedBox(width: 0),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          '#${(m['topicName'] ?? '').toString()}',
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w600),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.only(top: 2),
                                          child: Text(
                                            _topicStatsText(m),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            ),
                          ),
                        )),
                    ...List.generate(perRow - row.length, (i) => const Expanded(child: SizedBox.shrink())),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  String _topicDataSummary(Map<String, dynamic> data) {
    final threadCount = data['threadCount'] ?? 0;
    final viewCount = (data['view_count'] ?? '').toString();
    final likeCount = (data['like_count'] ?? '').toString();
    return '${viewCount}浏览 · ${likeCount}点赞 · ${threadCount}帖';
  }

  String _topicStatsText(Map<String, dynamic> item) {
    int? readInt(Map<String, dynamic> map, List<String> keys) {
      for (final key in keys) {
        final dynamic value = map[key];
        if (value is int) return value;
        if (value != null) {
          final parsed = int.tryParse(value.toString());
          if (parsed != null) return parsed;
        }
      }
      return null;
    }

    String? readStr(Map<String, dynamic> map, List<String> keys) {
      for (final key in keys) {
        final dynamic value = map[key];
        if (value != null) {
          final s = value.toString();
          if (s.isNotEmpty) return s;
        }
      }
      return null;
    }

    // 顶层优先
    int? threadCount = readInt(item, ['threadCount', 'thread_count']);
    String? viewCount = readStr(item, ['view_count', 'viewCount']);
    String? likeCount = readStr(item, ['like_count', 'likeCount']);

    // 回退 topicData（不再依赖 showTopicData 的取值，只要有数据就用）
    if (item['topicData'] is Map) {
      final data = Map<String, dynamic>.from(item['topicData'] as Map);
      threadCount ??= readInt(data, ['threadCount', 'thread_count']);
      viewCount ??= readStr(data, ['view_count', 'viewCount']);
      likeCount ??= readStr(data, ['like_count', 'likeCount']);
    }

    // 兜底
    threadCount ??= 0;
    viewCount ??= '0';
    likeCount ??= '0';

    return '${viewCount}浏览 · ${likeCount}点赞 · ${threadCount}帖';
  }

  // 添加一个方法来清除缓存（供下拉刷新使用）
  void _clearTabCache() {
    _cachedTabPages.clear();
  }

  // 添加一个方法来清除特定tab的缓存
  void _clearTabCacheAt(int index) {
    _cachedTabPages.remove(index);
  }



  void _navigateToCharacterBinding() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CharacterBindPage(
          phoneNumber: '138****8888', // 替换为实际的手机号
        ),
      ),
    );
  }

  void _navigateToSwitchBindCharacter() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SwitchBindCharacterPage(),
      ),
    );
  }

  void _handleFunctionButtonClick(String buttonName) {
    switch (buttonName) {
      case '必得体验金':
        _showComingSoonDialog('必得体验金');
        break;
      case '代金券':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const VoucherPage(),
          ),
        );
        break;
      case '礼包中心':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const GiftCenterPage(),
          ),
        );
        break;
      case '积分商城':
        _showComingSoonDialog('积分商城');
        break;
    }
  }

  void _showComingSoonDialog(String featureName) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('功能开发中'),
          content: Text('$featureName 功能正在开发中，敬请期待！'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildActivityBanner() {
    print('_buildActivityBanner called, modules count: ${_activityModules.length}'); // 调试日志
    
    if (_activityModules.isEmpty) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        height: 100,
        child: const Center(
          child: Text('暂无活动', style: TextStyle(color: Colors.grey)),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          // 功能图标滑动区域
          SizedBox(
            height: 100,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              controller: _functionScrollController,
              child: Row(
                children: _activityModules.map((module) => 
                  _buildActivityIcon(module)
                ).toList(),
              ),
            ),
          ),
          
          // 滑动指示器
          const SizedBox(height: 12),
          _buildScrollIndicator(),
        ],
      ),
    );
  }

  Widget _buildScrollIndicator() {
    // 始终显示指示器，除非没有数据
    if (_activityModules.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: 50,
      height: 6,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(3),
      ),
      child: Stack(
        children: [
          // 指示器背景
          Container(
            width: 50,
            height: 6,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(3),
            ),
          ),
          // 指示器前景（滑动进度）
          AnimatedAlign(
            duration: const Duration(milliseconds: 150),
            alignment: Alignment(-1.0 + (2.0 * _scrollProgress), 0),
            child: Container(
              width: 20,
              height: 6,
              decoration: BoxDecoration(
                color: Colors.orange,
                borderRadius: BorderRadius.circular(3),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityIcon(ActModule module) {
    return GestureDetector(
      onTap: () => _handleActivityClick(module),
      child: Container(
        width: 80,
        margin: const EdgeInsets.only(right: 8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 图标容器
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: Colors.grey.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: Image.network(
                  module.icon,
                  width: 48,
                  height: 48,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: Colors.grey.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(24),
                      ),
                      child: const Icon(
                        Icons.image_not_supported,
                        color: Colors.grey,
                        size: 24,
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(height: 8),
            // 主标题
            Text(
              module.actName,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void _handleActivityClick(ActModule module) {
    // 提取URL中的threadId
    int? threadId = _extractThreadIdFromUrl(module.url);
    
    if (threadId != null) {
      // 创建一个简化的ForumPost对象，只设置必要的字段
      final post = _createSimpleForumPost(threadId, module.actName);
      
      // 跳转到帖子详情页
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CommunityDetailPage(post: post),
        ),
      );
    } else {
      // 如果无法提取threadId，显示调试信息或处理其他逻辑
      debugPrint('无法从URL中提取threadId: ${module.url}');
      // 可以考虑其他处理方式，比如显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('链接格式不正确: ${module.url}')),
      );
    }
  }

  /// 打开链接中的帖子（通过提取threadId）
  void _openThreadFromLink(String link, {String title = ''}) {
    final threadId = _extractThreadIdFromUrl(link);
    if (threadId != null) {
      final post = _createSimpleForumPost(threadId, title.isEmpty ? '帖子' : title);
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CommunityDetailPage(post: post),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('无法从链接中识别帖子ID')),
      );
    }
  }

  /// 从URL中提取threadId
  int? _extractThreadIdFromUrl(String url) {
    try {
      // 解析URL
      final uri = Uri.parse(url);
      
      // 方法1: 从查询参数中获取id
      if (uri.queryParameters.containsKey('id')) {
        return int.tryParse(uri.queryParameters['id']!);
      }
      
      // 方法2: 从查询参数中获取threadId
      if (uri.queryParameters.containsKey('threadId')) {
        return int.tryParse(uri.queryParameters['threadId']!);
      }
      
      // 方法3: 从路径中提取数字id (例如: /post/123, /thread/456)
      final pathSegments = uri.pathSegments;
      for (int i = 0; i < pathSegments.length; i++) {
        final segment = pathSegments[i];
        // 如果当前段是 'post', 'thread', 'detail' 等关键词，则下一段可能是id
        if ((segment == 'post' || segment == 'thread' || segment == 'detail') && 
            i + 1 < pathSegments.length) {
          return int.tryParse(pathSegments[i + 1]);
        }
        // 如果当前段本身就是数字，也尝试解析
        final id = int.tryParse(segment);
        if (id != null && id > 0) {
          return id;
        }
      }
      
      // 方法4: 使用正则表达式匹配URL中的数字ID
      final regexPatterns = [
        r'[?&]id=(\d+)',           // ?id=123 或 &id=123
        r'[?&]threadId=(\d+)',     // ?threadId=123 或 &threadId=123
        r'/(\d+)(?:[/?]|$)',       // /123/ 或 /123? 或 /123 (结尾)
        r'=(\d+)',                 // 任何 =123 的形式
      ];
      
      for (final pattern in regexPatterns) {
        final regex = RegExp(pattern);
        final match = regex.firstMatch(url);
        if (match != null && match.groupCount > 0) {
          final id = int.tryParse(match.group(1)!);
          if (id != null && id > 0) {
            return id;
          }
        }
      }
      
      return null;
    } catch (e) {
      debugPrint('解析URL时出错: $e');
      return null;
    }
  }

  /// 创建简化的ForumPost对象
  ForumPost _createSimpleForumPost(int threadId, String title) {
    return ForumPost(
      threadId: threadId,
      postId: 0,
      userId: 0,
      categoryId: 0,
      parentCategoryId: 0,
      topicId: 0,
      categoryName: '',
      parentCategoryName: '',
      title: title,
      displayTitle: title,
      viewCount: 0,
      isApproved: 1,
      isStick: false,
      isDraft: false,
      isSite: false,
      isAnonymous: false,
      isFavorite: false,
      price: 0.0,
      attachmentPrice: 0.0,
      payType: 0,
      paid: null,
      isLike: false,
      isReward: false,
      createdAt: '',
      issueAt: '',
      updatedAt: '',
      diffTime: '',
      user: ForumUser(
        userId: 0,
        nickname: '',
        avatar: '',
        badge: '',
        label: '',
        color: '',
        medal: null,
        threadCount: 0,
        followCount: 0,
        fansCount: 0,
        likedCount: 0,
        questionCount: 0,
        isRealName: false,
        joinedAt: '',
        follow: 0,
      ),
      group: null,
      likeReward: ForumLikeReward(
        users: [],
        likePayCount: 0,
        shareCount: 0,
        postCount: 0,
      ),
      displayTag: ForumDisplayTag(
        isPoster: false,
        isEssence: false,
        isRedPack: null,
        isReward: null,
        isVote: false,
      ),
      position: ForumPosition(
        longitude: '',
        latitude: '',
        address: '',
        location: '',
      ),
      ability: ForumAbility(
        canEdit: false,
        canDelete: false,
        canEssence: false,
        canPoster: false,
        canStick: false,
        canReply: false,
        canViewPost: false,
        canFreeViewPost: false,
        canViewVideo: false,
        canViewAttachment: false,
        canDownloadAttachment: false,
      ),
      content: ForumContent(
        text: '',
        indexes: {},
        poster: null,
        images: [],
        videos: [],
        pureText: '',
      ),
      freewords: 0,
      userStickStatus: false,
      favorCount: 0,
      topics: '',
      reportStatus: 0,
      startShowTime: '',
      isBottom: 0,
      aiRank: '',
      aiType: '',
      pid: 0,
      uid: 0,
      gamePay: 0,
      auditedBy: '',
      auditedAt: null,
      updatedBy: '',
      createdBy: '',
      location: '',
      from: 0,
      addAiContent: 0,
      isRecommend: 0,
      voteId: null,
      vote: null,
      isMixThread: 0,
    );
  }


}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double preferredHeight;

  _SliverAppBarDelegate(this.child, {required this.preferredHeight});

  @override
  double get minExtent => preferredHeight;

  @override
  double get maxExtent => preferredHeight;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Colors.grey[50],
      child: child,
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}

/// 使用图片的Tab指示器，图片会绘制在选中Tab的下方并水平居中
class ImageTabIndicator extends Decoration {
  final ImageProvider image;
  final Size imageSize;
  final double bottomPadding;

  const ImageTabIndicator({
    required this.image,
    required this.imageSize,
    this.bottomPadding = 4,
  });

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _ImageTabIndicatorPainter(
      image: image,
      imageSize: imageSize,
      bottomPadding: bottomPadding,
      onChanged: onChanged,
    );
  }
}

class _ImageTabIndicatorPainter extends BoxPainter {
  final ImageProvider image;
  final Size imageSize;
  final double bottomPadding;

  ImageStream? _imageStream;
  ImageInfo? _imageInfo;
  ImageStreamListener? _imageStreamListener;

  _ImageTabIndicatorPainter({
    required this.image,
    required this.imageSize,
    required this.bottomPadding,
    VoidCallback? onChanged,
  }) : super(onChanged);

  void _resolveImage(ImageConfiguration configuration) {
    final ImageStream newStream = image.resolve(configuration);
    if (_imageStream?.key == newStream.key) {
      return;
    }
    if (_imageStream != null && _imageStreamListener != null) {
      _imageStream!.removeListener(_imageStreamListener!);
    }
    _imageStream = newStream;
    _imageStreamListener ??= ImageStreamListener(_handleImage, onError: _handleError);
    _imageStream!.addListener(_imageStreamListener!);
  }

  void _handleImage(ImageInfo imageInfo, bool synchronousCall) {
    _imageInfo = imageInfo;
    onChanged?.call();
  }

  void _handleError(Object exception, StackTrace? stackTrace) {
    // 忽略加载失败，指示器不绘制
  }

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    if (configuration.size == null) return;

    _resolveImage(configuration);

    final Rect rect = offset & configuration.size!;
    final double dx = rect.left + (rect.width - imageSize.width) / 2;
    final double dy = rect.bottom - bottomPadding - imageSize.height;
    final Rect dstRect = Rect.fromLTWH(dx, dy, imageSize.width, imageSize.height);

    final ImageInfo? info = _imageInfo;
    if (info == null) {
      return; // 图片尚未加载完成
    }

    final Size naturalSize = Size(
      info.image.width.toDouble(),
      info.image.height.toDouble(),
    );
    final Rect srcRect = Offset.zero & naturalSize;

    final Paint paint = Paint()..isAntiAlias = true;
    canvas.drawImageRect(info.image, srcRect, dstRect, paint);
  }

  @override
  void dispose() {
    if (_imageStream != null && _imageStreamListener != null) {
      _imageStream!.removeListener(_imageStreamListener!);
    }
    _imageStream = null;
    _imageInfo = null;
    super.dispose();
  }
}