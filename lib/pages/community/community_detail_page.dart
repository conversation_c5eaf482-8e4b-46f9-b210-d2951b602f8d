import 'package:flutter/material.dart';
import '../../components/cache_image.dart';

class GamePostDetailPage extends StatefulWidget {
  const GamePostDetailPage({super.key});

  @override
  State<GamePostDetailPage> createState() => _GamePostDetailPageState();
}

class _GamePostDetailPageState extends State<GamePostDetailPage> {
  // 模拟帖子数据
  final Map<String, dynamic> postData = {
    "title": "斗罗大陆:魂师对决",
    "author": {
      "avatar": "https://example.com/avatar.png",
      "name": "攻略团雨曦",
      "level": "凛冬之主",
      "role": "斗罗氛围官",
      "isOfficial": true,
      "isVerified": true,
    },
    "content": "五速sp唐舞桐:2222，爆伤\n任意速度:sp小白，速度",
    "imageUrl": "https://example.com/post_image.png",
    "timestamp": "2025-05-31 来自四川",
    "replies": [
      {
        "id": 1,
        "author": {
          "avatar": "https://example.com/user1.png",
          "name": "玩家d1e601f5"
        },
        "content": "阵容讲得很细！感觉带敏攻的确挺关键，大家可以多试试不同组合😉",
        "timestamp": "2025-05-31 来自四川",
        "likes": 1,
        "replies": [
          {
            "author": {
              "name": "攻略团雨曦",
              "isAuthor": true,
            },
            "content": "是的是的",
            "timestamp": "2025-05-31 来自四川",
          }
        ]
      },
    ],
    "likes": 18,
    "stars": 1,
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(postData["title"]),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(Icons.visibility),
            onPressed: () {},
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户信息区域
            _buildUserHeader(),

            // 帖子内容
            _buildPostContent(),

            // 时间戳
            _buildTimestamp(),

            const SizedBox(height: 20), // 为底部输入框留出空间
          ],
        ),
      ),
    );
  }

  Widget _buildUserHeader() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          CircleAvatar(
            backgroundImage: NetworkImage(postData["author"]["avatar"]),
            radius: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      postData["author"]["name"],
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child: const Text(
                        'V',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.blue,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        postData["author"]["level"],
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      postData["author"]["role"],
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(width: 4),
                    const Icon(
                      Icons.local_fire_department,
                      size: 12,
                      color: Colors.orange,
                    ),
                  ],
                ),
              ],
            ),
          ),
          ElevatedButton(
            onPressed: () {},
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            child: const Text("关注"),
          ),
        ],
      ),
    );
  }

  Widget _buildPostContent() {
    // 模拟后端返回的富文本HTML字符串
    const String richTextHtml = """<p class="f_center"><img style="width: 100%; height: 100%;" src="https://imgcs.s98s2.com/common/KOCl2u9FfO0Psos5CKz1ccgpAebdGwDqfOp1hYyM.png"/><br/><br/></p> <p id="3PBD2N9J">大家好，我是正在已经肝废了的何二维一。</p> <p id="3PBD2N9K">今天第二个大地图星斗大森林也是正式开启啦，所以维一也第一时间把这个地图中的所有探索要素做了个公测版本的实际踩点，今天也为大家奉上星斗大森林的全收集要素攻略，希望能给大家一些小小的帮助和欢乐。</p> <p class="f_center"><img style="width: 100%; height: 100%;" src="https://imgcs.s98s2.com/common/BUALIyPYSHTH4OvNTxyuvYM67qKKZiWe4zp9s2QN.png"/><br/><br/></p> <p id="3PBD2N9N">总体来讲星斗大森林的收集要素是要多于诺丁城的，不过其在某些元素的收集上却稍微简单一些......然后我们一个一个来具体说明。</p> <p class="f_center"><img style="width: 100%; height: 100%;" src="https://imgcs.s98s2.com/common/XYLGuP4pYJYSrJXC4GZn7xr5RjCQowffd0g25jFO.png"/><br/><br/></p> <p id="3PBD2N9Q">首先还是宝箱的收集，虽然星斗大森林的秘藏总数量为180个，但不用打邪魂师或者是仙草秘藏等能拿到的宝箱总数为108个，上图为全部108个宝箱的位置标注图，其中每一个维一今天都进行了实际踩点。</p> <p class="f_center"><img style="width: 100%; height: 100%;" src="https://imgcs.s98s2.com/common/U84MNIb7cDHbVs46ARdljqvvdzLbugu4gvZKFN6S.png"/><br/><br/></p> <p id="3PBD2N9T">而这些宝箱中有3个需要特别注明一下，那便是上图中标注为94-96号的三个宝箱，它们具体的位置是在上图中这一堆敌人后边的房子的山洞里......当然一个山洞里就有3个宝箱在那放着，稍微有点难找，给大家特别说明一下。</p> <p class="f_center"><img style="width: 100%; height: 100%;" src="https://imgcs.s98s2.com/common/NyQHxcNVLALQhBYFHfl2FMgn1EKH5VbeFOi9nECz.png"/><br/><br/></p> <p id="3PBD2NA0">然后是12个回忆的具体位置，在上图中维一都做了详细的标注和提示，其中胡列娜的回忆是一只狐狸，橘红色样式的，而小舞的回忆这是一只粉色的兔子，建模比较小，大家需要稍微注意一下。</p> <p id="3PBD2NA1">那么在这些回忆里特别要提的一个是小舞的回忆6，这个回忆是在标注位置的一个屋子里，而且还是在屋子里的床上坐着......很多不进屋的朋友可能不太好发现，给大家特别说明一下。</p> <p class="f_center"><img style="width: 100%; height: 100%;" src="https://imgcs.s98s2.com/common/AoVEZpjchXv6jWLUJOczDAcmebyrrfBbz4GeoSPU.png"/><br/><br/></p> <p id="3PBD2NA4">接着则是8个仙草的位置，这些仙草普遍都还算比较好找，其中特别说明下仙草4是需要飞到山崖上才能看到的，大家稍微注意下。</p> <p class="f_center"><img style="width: 100%; height: 100%;" src="https://imgcs.s98s2.com/common/bePJwTgUmYMyATh6RZWAiVHu55QOTlsQqtefRxYq.png"/><br/><br/></p> <p id="3PBD2NA7">接着星斗大森林地图的三个试炼点，其中的凤凰试炼就是打怪兽，我们只需要堵门打即可，其中穿插使用q或者e技能即可，难度并不算高。</p> <p id="3PBD2NA8">然后蓝银草试炼就更简单了，顺时针慢慢走即可，不要太快......而最后的幽冥猫试炼是一个要躲射线的小游戏，维一试了很多次，这个射线的每一关都是随机出现的，所以没有一个固定的走法，而下图中维一标注出来的红色圈圈，都是靠边缘的位置，这些位置是相对比较难好躲射线的，大家可以适当的选择站位，总体来讲过关的难度并不算很大。</p> <p class="f_center"><img style="width: 100%; height: 100%;" src="https://imgcs.s98s2.com/common/FNeWkcojUKjmNUxegb0GfQWfP8CnrC4UF659WVsC.png"/><br/><br/></p> <p id="3PBD2NAB">那么以上便是星斗大森林的全收集要素汇总了，按照进度，前两天每天都买体力超过2次以上的朋友，今天应该是可以达到32级的，大家还请合理运用时间即可，明天我们再来具体说说其它配队方面的内容。</p>""";

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 富文本内容展示
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(0),
            ),
            padding: const EdgeInsets.all(16),
            child: _buildRichTextContent(richTextHtml),
          ),
        ],
      ),
    );
  }

  Widget _buildRichTextContent(String htmlContent) {
    // 简单的HTML解析，提取图片和文字
    final List<Widget> widgets = [];
    
    // 分割HTML内容
    final parts = htmlContent.split('<img');
    
    for (int i = 0; i < parts.length; i++) {
      final part = parts[i];
      
      if (i == 0) {
        // 第一段，只包含文字
        final textContent = _extractTextFromHtml(part);
        if (textContent.isNotEmpty) {
          widgets.add(_buildTextWidget(textContent));
        }
      } else {
        // 后续段落，包含图片和文字
        final imgEndIndex = part.indexOf('/>');
        if (imgEndIndex != -1) {
          // 提取图片URL
          final imgStartIndex = part.indexOf('src="');
          if (imgStartIndex != -1) {
            final imgUrlStart = imgStartIndex + 5;
            final imgUrlEnd = part.indexOf('"', imgUrlStart);
            if (imgUrlEnd != -1) {
              final imgUrl = part.substring(imgUrlStart, imgUrlEnd);
              widgets.add(_buildImageWidget(imgUrl));
            }
          }
          
          // 提取图片后的文字
          final remainingText = part.substring(imgEndIndex + 2);
          final textContent = _extractTextFromHtml(remainingText);
          if (textContent.isNotEmpty) {
            widgets.add(_buildTextWidget(textContent));
          }
        }
      }
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  String _extractTextFromHtml(String html) {
    // 简单的HTML标签清理
    return html
        .replaceAll(RegExp(r'<[^>]*>'), '') // 移除所有HTML标签
        .replaceAll('&nbsp;', ' ') // 替换HTML实体
        .replaceAll('&amp;', '&')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&quot;', '"')
        .trim();
  }

  Widget _buildTextWidget(String text) {
    if (text.isEmpty) return const SizedBox.shrink();
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 16,
          height: 1.6,
          color: Colors.black87,
        ),
      ),
    );
  }

  Widget _buildImageWidget(String imageUrl) {
    return Container(
      width: double.infinity,
      height: 200,
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: CachedImage(
          imageUrl: imageUrl,
          fit: BoxFit.cover,
          placeholder: Container(
            color: Colors.grey[100],
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
          errorWidget: Container(
            color: Colors.grey[200],
            child: const Center(
              child: Icon(Icons.error, color: Colors.grey),
            ),
          ),
        ),
      ),
    );
  }



  Widget _buildTimestamp() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Text(
        postData["timestamp"],
        style: const TextStyle(
          color: Colors.grey,
          fontSize: 12,
        ),
      ),
    );
  }
}