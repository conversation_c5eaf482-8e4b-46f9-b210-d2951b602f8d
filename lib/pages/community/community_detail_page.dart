import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../components/cache_image.dart';
import '../../model/forum_post_list.dart';
import '../../model/posts_list.dart';
import '../../net/api/forum_service.dart';
import '../../net/api/posts_service.dart';
import '../../net/http_base_config.dart';
import '../../utils/log_util.dart';

class CommunityDetailPage extends StatefulWidget {
  final ForumPost post;

  const CommunityDetailPage({
    super.key,
    required this.post,
  });

  @override
  State<CommunityDetailPage> createState() => _CommunityDetailPageState();
}

class _CommunityDetailPageState extends State<CommunityDetailPage> {
  bool _isLoading = true;
  String? _error;
  ForumPost? _detailPost;
  final ForumService _forumService = ForumService();
  final PostsService _postsService = PostsService();
  
  // 评论相关
  bool _isLoadingComments = true;
  String? _commentsError;
  PostsList? _commentsList;
  String _sortType = 'hot'; // hot, latest
  
  // 视频播放器管理
  final Map<String, VideoPlayerController> _videoControllers = {};
  final Map<String, bool> _videoInitialized = {};
  final Map<String, bool> _videoBuffering = {};
  final Map<String, bool> _videoLoading = {};

  @override
  void initState() {
    super.initState();
    _loadPostDetail();
    _loadComments();
  }

  @override
  void dispose() {
    // 销毁所有视频控制器
    for (final controller in _videoControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _loadPostDetail() async {
    if (!mounted) return;
    
    LogUtil.d('开始加载帖子详情', tag: 'PostDetail');
    
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await _forumService.getPostDetail(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        threadId: widget.post.threadId,
      );

      if (!mounted) return;

      LogUtil.d('接口响应: code=${response.code}, message=${response.message}', tag: 'PostDetail');

      if (response.code == 0 && response.data != null) {
        setState(() {
          _detailPost = response.data;
          _isLoading = false;
        });
        LogUtil.d('帖子详情加载成功', tag: 'PostDetail');
      } else {
        setState(() {
          _error = response.message ?? '获取帖子详情失败';
          _isLoading = false;
        });
        LogUtil.w('帖子详情加载失败: ${response.message}', tag: 'PostDetail');
      }
    } catch (e, stackTrace) {
      if (!mounted) return;
      setState(() {
        _error = '网络错误: $e';
        _isLoading = false;
      });
      LogUtil.e('帖子详情加载异常', error: e, stackTrace: stackTrace, tag: 'PostDetail');
    }
  }

  Future<void> _loadComments() async {
    if (!mounted) return;
    
    LogUtil.d('开始加载评论列表', tag: 'Comments');
    
    setState(() {
      _isLoadingComments = true;
      _commentsError = null;
    });

    try {
      final response = await _postsService.getPostsList(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        threadId: widget.post.threadId,
        page: 1,
        pageSize: 20,
        sort: _getApiSortValue(_sortType),
      );

      if (!mounted) return;

      LogUtil.d('评论接口响应: code=${response.code}, message=${response.message}', tag: 'Comments');

      if (response.code == 0 && response.data != null) {
        setState(() {
          _commentsList = response.data;
          _isLoadingComments = false;
        });
        LogUtil.d('评论列表加载成功', tag: 'Comments');
      } else {
        setState(() {
          _commentsError = response.message ?? '获取评论失败';
          _isLoadingComments = false;
        });
        LogUtil.w('评论列表加载失败: ${response.message}', tag: 'Comments');
      }
    } catch (e, stackTrace) {
      if (!mounted) return;
      setState(() {
        _commentsError = '网络错误: $e';
        _isLoadingComments = false;
      });
      LogUtil.e('评论列表加载异常', error: e, stackTrace: stackTrace, tag: 'Comments');
    }
  }

  void _changeSortType(String sortType) {
    if (_sortType != sortType) {
      setState(() {
        _sortType = sortType;
      });
      _loadComments();
    }
  }

  String _getApiSortValue(String sortType) {
    switch (sortType) {
      case 'hot':
        return 'hot';
      case 'latest':
        return '-createdAt';
      default:
        return 'hot';
    }
  }
  @override
  Widget build(BuildContext context) {
    // 显示加载状态
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
          title: const Text('帖子详情'),
          centerTitle: true,
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // 显示错误状态
    if (_error != null) {
      return Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
          title: const Text('帖子详情'),
          centerTitle: true,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                _error!,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadPostDetail,
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      );
    }

    // 使用详情数据，如果没有详情数据则使用传入的数据
    final post = _detailPost ?? widget.post;

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          post.title.isNotEmpty ? post.title : '帖子详情',
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {},
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户信息区域
            _buildUserHeader(post),

            // 帖子标题和内容（白色背景容器）
            _buildPostTitleAndContent(post),

            // 时间戳和统计信息
            _buildPostMeta(post),

            // 评论区域
            _buildCommentsSection(),

            const SizedBox(height: 20), // 为底部留出空间
          ],
        ),
      ),
    );
  }

  Widget _buildUserHeader(ForumPost post) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          CircleAvatar(
            radius: 24,
            backgroundColor: Colors.grey[200],
            backgroundImage: post.user.avatar.isNotEmpty 
                ? NetworkImage(post.user.avatar) 
                : null,
            child: post.user.avatar.isEmpty 
                ? Icon(
                    Icons.person,
                    size: 24,
                    color: Colors.grey[600],
                  )
                : null,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      post.user.nickname.isNotEmpty ? post.user.nickname : '匿名用户',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(width: 8),
                    if (post.user.badge.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: Colors.blue[200]!, width: 0.5),
                        ),
                        child: Text(
                          '官方账号',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.blue[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  '发帖 ${post.user.threadCount} · 粉丝 ${post.user.fansCount}',
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          if (post.user.follow == 0)
            ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              child: const Text("关注", style: TextStyle(fontSize: 12)),
            ),
        ],
      ),
    );
  }

  Widget _buildPostTitleAndContent(ForumPost post) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 0.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 帖子标题
          if (post.title.isNotEmpty)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Text(
                post.title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                  height: 1.3,
                ),
              ),
            ),
          
          // 帖子内容
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
            child: _buildRichTextContent(post.content.text, post.content.images),
          ),
        ],
      ),
    );
  }

  Widget _buildPostTitle(ForumPost post) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Text(
        post.title,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
          height: 1.3,
        ),
      ),
    );
  }

  Widget _buildPostMeta(ForumPost post) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(),
          Row(
            children: [
              Text(
                _formatTimeAgo(post.createdAt),
                style: const TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                ),
              ),
              const Spacer(),
              Row(
                children: [
                  Icon(
                    Icons.visibility_outlined,
                    size: 16,
                    color: Colors.grey[500],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatCount(post.viewCount),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.favorite_outline,
                    size: 16,
                    color: Colors.grey[500],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatCount(post.likeReward.likePayCount),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRichTextContent(String htmlContent, List<dynamic> images) {
    final List<Widget> widgets = [];
    final post = _detailPost ?? widget.post;
    
    // 判断帖子类型
    final bool isMixedContent = post.from == 2 || post.isMixThread == 1;
    
    if (isMixedContent) {
      // 图文混排：HTML内容中包含img标签，图片存储在content.images中
      
      // 检查HTML中是否真的包含img标签
      final hasImgTags = htmlContent.contains('<img');
      
      if (hasImgTags) {
        // 真正的图文混排，HTML中有img标签
        widgets.addAll(_buildMixedContentWithImages(htmlContent, post.content.images));
      } else {
        // 虽然标记为混排，但HTML中没有img标签，按分离处理
        
        // 先显示纯文本内容
        final textContent = _extractTextFromHtml(htmlContent);
        if (textContent.isNotEmpty) {
          final textWidgets = _buildTextParagraphs(textContent);
          widgets.addAll(textWidgets);
        }
        
        // 然后显示所有图片（从content.images获取）
        if (post.content.images.isNotEmpty) {
          for (var i = 0; i < post.content.images.length; i++) {
            var imageData = post.content.images[i];
            String? imageUrl;
            if (imageData is Map<String, dynamic>) {
              imageUrl = imageData['url'];
            } else if (imageData is String) {
              imageUrl = imageData;
            }
            
            if (imageUrl != null && imageUrl.isNotEmpty) {
              final imageWidget = _buildImageWidget(imageUrl, post.content.images);
              widgets.add(imageWidget);
            }
          }
        }
        
        // 检查是否有indexes中的图片和视频
        final separatedImages = _buildSeparatedImages(post);
        final separatedVideos = _buildSeparatedVideos(post);
        widgets.addAll(separatedImages);
        widgets.addAll(separatedVideos);
      }
    } else {
      // 图文分离：HTML只有文本，图片存储在indexes[101]中
      
      // 先显示纯文本内容
      final textContent = _extractTextFromHtml(htmlContent);
      if (textContent.isNotEmpty) {
        final textWidgets = _buildTextParagraphs(textContent);
        widgets.addAll(textWidgets);
      }
      
      // 然后显示图片（从indexes[101]获取）
      final separatedImages = _buildSeparatedImages(post);
      widgets.addAll(separatedImages);
      
      // 最后显示视频（从indexes[108]获取）
      final separatedVideos = _buildSeparatedVideos(post);
      widgets.addAll(separatedVideos);
    }
    
    // 如果没有任何widget，添加一个占位符
    if (widgets.isEmpty) {
      widgets.add(Container(
        height: 100,
        width: double.infinity,
        color: Colors.grey[100],
        child: const Center(
          child: Text('暂无内容', style: TextStyle(color: Colors.grey)),
        ),
      ));
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }
  
  /// 处理图文混排内容（富文本HTML，包含img标签，图片在content.images中）    
  List<Widget> _buildMixedContentWithImages(String htmlContent, List<dynamic> contentImages) {
    final List<Widget> widgets = [];
    
    
    if (contentImages.isEmpty) {
      // 如果没有图片，只显示文字内容
      final textContent = _extractTextFromHtml(htmlContent);
      if (textContent.isNotEmpty) {
        widgets.addAll(_buildTextParagraphs(textContent));
      }
      return widgets;
    }
    
    // 分割HTML内容，处理文本和图片
    final parts = htmlContent.split('<img');
    
    // 图片索引计数器
    int imageIndex = 0;
    
    for (int i = 0; i < parts.length; i++) {
      final part = parts[i];
      
      if (i == 0) {
        // 第一段，只包含文字
        final textContent = _extractTextFromHtml(part);
        if (textContent.isNotEmpty) {
          widgets.addAll(_buildTextParagraphs(textContent));
        }
      } else {
        // 后续段落，包含图片和文字
        final imgEndIndex = part.indexOf('/>');
        if (imgEndIndex != -1) {
          // 从content.images中获取对应的图片URL
          if (imageIndex < contentImages.length) {
            final imageData = contentImages[imageIndex];
            String? imageUrl;
            
            if (imageData is Map<String, dynamic>) {
              imageUrl = imageData['url'];
            } else if (imageData is String) {
              imageUrl = imageData;
            }
            
            if (imageUrl != null && imageUrl.isNotEmpty) {
              widgets.add(_buildImageWidget(imageUrl, contentImages));
            }
            imageIndex++;
          }
          
          // 提取图片后的文字
          final remainingText = part.substring(imgEndIndex + 2);
          final textContent = _extractTextFromHtml(remainingText);
          if (textContent.isNotEmpty) {
            widgets.addAll(_buildTextParagraphs(textContent));
          }
        }
      }
    }
    
    // 如果还有剩余的图片没有显示，在最后添加
    while (imageIndex < contentImages.length) {
      final imageData = contentImages[imageIndex];
      String? imageUrl;
      
      if (imageData is Map<String, dynamic>) {
        imageUrl = imageData['url'];
      } else if (imageData is String) {
        imageUrl = imageData;
      }
      
      if (imageUrl != null && imageUrl.isNotEmpty) {
        widgets.add(_buildImageWidget(imageUrl, contentImages));
      }
      imageIndex++;
    }
    
    return widgets;
  }
  
  /// 将文本按段落分割并生成widget
  List<Widget> _buildTextParagraphs(String textContent) {
    final List<Widget> widgets = [];
    final paragraphs = textContent.split('\n').where((p) => p.trim().isNotEmpty).toList();
    
    for (final paragraph in paragraphs) {
      if (paragraph.trim().isNotEmpty) {
        widgets.add(_buildTextWidget(paragraph.trim()));
      }
    }
    return widgets;
  }
  
  /// 处理图文分离的图片
  List<Widget> _buildSeparatedImages(ForumPost post) {
    final List<Widget> widgets = [];
    final indexes = post.content.indexes;
    
    // 从indexes[101]获取图片
    if (indexes.containsKey('101')) {
      final imageIndex = indexes['101'];
      if (imageIndex is Map<String, dynamic> && imageIndex['body'] is List) {
        final bodyList = imageIndex['body'] as List;
        
        for (var item in bodyList) {
          if (item is Map<String, dynamic>) {
            final imageUrl = item['url'] ?? item['thumbUrl'] ?? item['attachment'];
            if (imageUrl != null && imageUrl.isNotEmpty) {
              widgets.add(_buildImageWidget(imageUrl, bodyList));
            }
          }
        }
      }
    }
    
    return widgets;
  }
  
  /// 处理图文分离的视频
  List<Widget> _buildSeparatedVideos(ForumPost post) {
    final List<Widget> widgets = [];
    final indexes = post.content.indexes;
    
    // 从indexes[108]获取视频
    if (indexes.containsKey('108')) {
      final videoIndex = indexes['108'];
      if (videoIndex is Map<String, dynamic> && videoIndex['body'] is List) {
        final bodyList = videoIndex['body'] as List;
        
        for (var item in bodyList) {
          if (item is Map<String, dynamic>) {
            final videoUrl = item['url'];
            String? coverUrl;
            
            // 安全地获取封面URL
            final cover = item['cover'];
            if (cover is Map<String, dynamic>) {
              coverUrl = cover['url'];
            } else if (cover is List && cover.isNotEmpty) {
              final firstItem = cover.first;
              if (firstItem is Map<String, dynamic>) {
                coverUrl = firstItem['url'];
              }
            }
            
            if (videoUrl != null && videoUrl.isNotEmpty) {
              widgets.add(_buildVideoWidget(videoUrl, coverUrl));
            }
          }
        }
      }
    }
    
    return widgets;
  }
  
  /// 构建视频widget - 支持内联播放和全屏播放
  Widget _buildVideoWidget(String videoUrl, String? coverUrl) {
    final isInitialized = _videoInitialized[videoUrl] ?? false;
    final isBuffering = _videoBuffering[videoUrl] ?? false;
    
    return Container(
      width: double.infinity,
      height: 200,
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[200],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: isInitialized
            ? _buildInlineVideoPlayer(videoUrl)
            : _buildVideoCover(videoUrl, coverUrl),
      ),
    );
  }

  /// 构建视频封面
  Widget _buildVideoCover(String videoUrl, String? coverUrl) {
    final isLoading = _videoLoading[videoUrl] ?? false;
    
    return GestureDetector(
      onTap: () => _initializeInlineVideo(videoUrl),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 视频封面
          if (coverUrl != null && coverUrl.isNotEmpty)
            CachedImage(
              imageUrl: coverUrl,
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
            )
          else
            Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.grey[300],
              child: Icon(
                Icons.videocam,
                size: 48,
                color: Colors.grey[600],
              ),
            ),
          
          // 加载遮罩
          if (isLoading)
            Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.black54,
              child: const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 3,
                  ),
                  SizedBox(height: 8),
                  Text(
                    '视频加载中...',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            )
          else
            // 播放按钮
            Container(
              width: 60,
              height: 60,
              decoration: const BoxDecoration(
                color: Colors.black54,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.play_arrow,
                color: Colors.white,
                size: 30,
              ),
            ),
          
          // 视频标识
          if (!isLoading)
            Positioned(
              bottom: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  '视频',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建内联视频播放器
  Widget _buildInlineVideoPlayer(String videoUrl) {
    final controller = _videoControllers[videoUrl];
    final isBuffering = _videoBuffering[videoUrl] ?? false;
    
    if (controller == null) return const SizedBox.shrink();
    
    return Stack(
      children: [
        AspectRatio(
          aspectRatio: controller.value.aspectRatio,
          child: VideoPlayer(controller),
        ),
        InlineVideoControls(
          controller: controller,
          isBuffering: isBuffering,
          onFullscreen: () => _playVideoFullscreen(videoUrl),
        ),
      ],
    );
  }

  /// 初始化内联视频播放
  Future<void> _initializeInlineVideo(String videoUrl) async {
    if (_videoControllers.containsKey(videoUrl)) return;
    
    
    // 显示加载状态
    setState(() {
      _videoLoading[videoUrl] = true;
    });
    
    try {
      final controller = VideoPlayerController.networkUrl(Uri.parse(videoUrl));
      _videoControllers[videoUrl] = controller;
      
      // 监听缓冲状态
      controller.addListener(() {
        if (!mounted) return;
        final isBuffering = controller.value.isBuffering;
        if (_videoBuffering[videoUrl] != isBuffering) {
          setState(() {
            _videoBuffering[videoUrl] = isBuffering;
          });
        }
      });
      
      await controller.initialize();
      
      if (mounted) {
        setState(() {
          _videoInitialized[videoUrl] = true;
          _videoLoading[videoUrl] = false;
        });
        
        controller.play();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _videoLoading[videoUrl] = false;
        });
      }
      // 如果内联播放失败，直接跳转全屏播放
      _playVideoFullscreen(videoUrl);
    }
  }

  /// 全屏播放视频
  void _playVideoFullscreen(String videoUrl) {
    // 停止内联视频播放
    final inlineController = _videoControllers[videoUrl];
    if (inlineController != null && inlineController.value.isPlaying) {
      inlineController.pause();
    }
    
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoPlayerPage(videoUrl: videoUrl),
      ),
    );
  }

  String _extractTextFromHtml(String html) {
    
    // 处理段落标签，转换成换行
    String result = html
        .replaceAll('<p>', '')
        .replaceAll('</p>', '\n')
        .replaceAll('<br/>', '\n')
        .replaceAll('<br>', '\n');
    
    
    // 移除其他HTML标签
    result = result.replaceAll(RegExp(r'<[^>]*>'), '');
    
    
    // 替换HTML实体
    result = result
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&amp;', '&')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&quot;', '"')
        .replaceAll('&#39;', "'")
        .replaceAll('&mdash;', '—')
        .replaceAll('&ldquo;', '"')
        .replaceAll('&rdquo;', '"');
    
    // 清理多余的空白字符
    result = result
        .replaceAll(RegExp(r'\s+'), ' ')
        .replaceAll(RegExp(r'\n\s*\n'), '\n')
        .trim();
    
    
    return result;
  }

  Widget _buildTextWidget(String text) {
    if (text.isEmpty) return const SizedBox.shrink();
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 16,
          height: 1.6,
          color: Colors.black87,
        ),
      ),
    );
  }

  Widget _buildImageWidget(String imageUrl, [List<dynamic>? allImages]) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: GestureDetector(
          onTap: () => _showFullScreenImage(imageUrl, allImages),
          child: CachedNetworkImage(
            imageUrl: imageUrl,
            width: double.infinity,
            fit: BoxFit.fitWidth,
            placeholder: (context, url) => Container(
              height: 200,
              width: double.infinity,
              color: Colors.grey[100],
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 8),
                    Text('图片加载中...', style: TextStyle(fontSize: 12)),
                  ],
                ),
              ),
            ),
            errorWidget: (context, url, error) => Container(
              height: 200,
              width: double.infinity,
              color: Colors.red[100],
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error, color: Colors.red, size: 32),
                    SizedBox(height: 8),
                    Text('图片加载失败', style: TextStyle(fontSize: 12, color: Colors.red)),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showFullScreenImage(String imageUrl, [List<dynamic>? allImages]) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (BuildContext context) {
        // 如果有多张图片，使用图片库，否则只显示单张图片
        if (allImages != null && allImages.length > 1) {
          int initialIndex = 0;
          for (int i = 0; i < allImages.length; i++) {
            final imageData = allImages[i];
            String? url;
            if (imageData is Map<String, dynamic>) {
              url = imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
            } else if (imageData is String) {
              url = imageData;
            }
            if (url == imageUrl) {
              initialIndex = i;
              break;
            }
          }
          return _ImageGalleryDialog(images: allImages, initialIndex: initialIndex);
        } else {
          return GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Dialog.fullscreen(
              backgroundColor: Colors.transparent,
              child: Stack(
                children: [
                  Center(
                    child: InteractiveViewer(
                      panEnabled: true,
                      scaleEnabled: true,
                      minScale: 0.5,
                      maxScale: 3.0,
                      child: CachedImage(
                        imageUrl: imageUrl,
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                  Positioned(
                    top: 50,
                    right: 20,
                    child: GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: const BoxDecoration(
                          color: Colors.black54,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }
      },
    );
  }

  String _formatTimeAgo(String createdAt) {
    try {
      final DateTime postTime = DateTime.parse(createdAt);
      final DateTime now = DateTime.now();
      final Duration diff = now.difference(postTime);
      
      if (diff.inMinutes < 60) {
        return '${diff.inMinutes}分钟前';
      } else if (diff.inHours < 24) {
        return '${diff.inHours}小时前';
      } else if (diff.inDays < 30) {
        return '${diff.inDays}天前';
      } else {
        return createdAt.substring(0, 10);
      }
    } catch (e) {
      return '刚刚';
    }
  }

  String _formatCount(int count) {
    if (count > 10000) {
      return '${(count / 10000).toStringAsFixed(1)}w';
    } else if (count > 1000) {
      return '${(count / 1000).toStringAsFixed(1)}k';
    } else {
      return count.toString();
    }
  }

  Widget _buildCommentsSection() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 评论标题和排序
          _buildCommentsHeader(),
          
          // 评论列表
          if (_isLoadingComments)
            const Padding(
              padding: EdgeInsets.all(40),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          else if (_commentsError != null)
            Padding(
              padding: const EdgeInsets.all(20),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _commentsError!,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextButton(
                      onPressed: _loadComments,
                      child: const Text('重试'),
                    ),
                  ],
                ),
              ),
            )
          else if (_commentsList?.pageData.isEmpty ?? true)
            Padding(
              padding: const EdgeInsets.all(40),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.message_outlined,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '暂无评论',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            _buildCommentsList(),
        ],
      ),
    );
  }

  Widget _buildCommentsHeader() {
    final commentCount = _commentsList?.totalCount ?? 75;
    
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
      child: Column(
        children: [
          // 评论数量和排序按钮
          Row(
            children: [
              Text(
                '全部回复 (${commentCount}条)',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const Spacer(),
              // 排序选项
              Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  children: [
                    _buildSortTab('最热', 'hot'),
                    _buildSortTab('最新', 'latest'),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Divider(height: 1),
        ],
      ),
    );
  }

  Widget _buildSortTab(String label, String sortType) {
    final isSelected = _sortType == sortType;
    
    return GestureDetector(
      onTap: () => _changeSortType(sortType),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(18),
          boxShadow: isSelected ? [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 0,
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ] : null,
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
            color: isSelected ? Colors.black87 : Colors.grey[600],
          ),
        ),
      ),
    );
  }

  Widget _buildCommentsList() {
    final comments = _commentsList?.pageData ?? [];
    
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: comments.length,
      separatorBuilder: (context, index) => const Divider(height: 1),
      itemBuilder: (context, index) {
        return _buildCommentItem(comments[index]);
      },
    );
  }

  Widget _buildCommentItem(Post comment) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户头像
          CircleAvatar(
            radius: 20,
            backgroundColor: Colors.grey[200],
            backgroundImage: comment.user.avatar.isNotEmpty 
                ? NetworkImage(comment.user.avatar) 
                : null,
            child: comment.user.avatar.isEmpty 
                ? Icon(
                    Icons.person,
                    size: 20,
                    color: Colors.grey[600],
                  )
                : null,
          ),
          
          const SizedBox(width: 12),
          
          // 评论内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 用户名和时间
                Row(
                  children: [
                    Text(
                      comment.user.nickname.isNotEmpty ? comment.user.nickname : '匿名用户',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _formatTimeAgo(comment.createdAt),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 6),
                
                // 评论文本
                if (comment.content.isNotEmpty)
                  Text(
                    _extractTextFromHtml(comment.content),
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                      height: 1.4,
                    ),
                  ),
                
                // 显示图片
                if (comment.images.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: comment.images.map((image) {
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: GestureDetector(
                            onTap: () => _showFullScreenImage(image.url, comment.images.map((img) => {'url': img.url}).toList()),
                            child: CachedNetworkImage(
                              imageUrl: image.url,
                              width: 120,
                              height: 120,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                width: 120,
                                height: 120,
                                color: Colors.grey[200],
                                child: const Center(
                                  child: CircularProgressIndicator(),
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                width: 120,
                                height: 120,
                                color: Colors.grey[200],
                                child: const Icon(Icons.error),
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                
                const SizedBox(height: 8),
                
                // 互动按钮
                Row(
                  children: [
                    _buildCommentActionButton(
                      Icons.thumb_up_outlined,
                      comment.likeCount.toString(),
                      () {},
                    ),
                    const SizedBox(width: 20),
                    _buildCommentActionButton(
                      Icons.chat_bubble_outline,
                      '回复',
                      () {},
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentActionButton(
    IconData icon,
    String text,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: Colors.grey[500],
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }
}

/// 图片画廊弹窗组件
class _ImageGalleryDialog extends StatefulWidget {
  final List<dynamic> images;
  final int initialIndex;

  const _ImageGalleryDialog({
    required this.images,
    required this.initialIndex,
  });

  @override
  State<_ImageGalleryDialog> createState() => _ImageGalleryDialogState();
}

class _ImageGalleryDialogState extends State<_ImageGalleryDialog> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  String? _getImageUrl(dynamic imageData) {
    if (imageData is Map<String, dynamic>) {
      return imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
    } else if (imageData is String) {
      return imageData;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Navigator.of(context).pop(),
      child: Dialog.fullscreen(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            // 背景可点击区域
            Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.transparent,
            ),
            // 图片轮播
            PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemCount: widget.images.length,
              itemBuilder: (context, index) {
                final imageUrl = _getImageUrl(widget.images[index]);
                if (imageUrl == null || imageUrl.isEmpty) {
                  return const Center(
                    child: Icon(
                      Icons.broken_image,
                      size: 64,
                      color: Colors.white54,
                    ),
                  );
                }

                return Center(
                  child: InteractiveViewer(
                    panEnabled: true,
                    scaleEnabled: true,
                    minScale: 0.5,
                    maxScale: 3.0,
                    child: CachedImage(
                      imageUrl: imageUrl,
                      fit: BoxFit.contain,
                    ),
                  ),
                );
              },
            ),
            // 页面指示器
            if (widget.images.length > 1)
              Positioned(
                bottom: 100,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${_currentIndex + 1} / ${widget.images.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            // 关闭按钮
            Positioned(
              top: 50,
              right: 20,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    color: Colors.black54,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 内联视频播放控制器
class InlineVideoControls extends StatefulWidget {
  final VideoPlayerController controller;
  final bool isBuffering;
  final VoidCallback onFullscreen;

  const InlineVideoControls({
    super.key,
    required this.controller,
    required this.isBuffering,
    required this.onFullscreen,
  });

  @override
  State<InlineVideoControls> createState() => _InlineVideoControlsState();
}

class _InlineVideoControlsState extends State<InlineVideoControls> {
  bool _showControls = true;

  @override
  void initState() {
    super.initState();
    _hideControlsAfterDelay();
  }

  void _hideControlsAfterDelay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    
    if (_showControls) {
      _hideControlsAfterDelay();
    }
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleControls,
      child: Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            // 缓冲指示器
            if (widget.isBuffering)
              const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 3,
                    ),
                    SizedBox(height: 8),
                    Text(
                      '缓冲中...',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            
            // 控制器
            AnimatedOpacity(
              opacity: _showControls ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 300),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withOpacity(0.7),
                      Colors.transparent,
                      Colors.transparent,
                      Colors.black.withOpacity(0.7),
                    ],
                  ),
                ),
                child: Column(
                  children: [
                    // 顶部控制栏
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        IconButton(
                          onPressed: widget.onFullscreen,
                          icon: const Icon(
                            Icons.fullscreen,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ],
                    ),
                    
                    const Spacer(),
                    
                    // 播放控制
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton(
                          onPressed: () {
                            setState(() {
                              if (widget.controller.value.isPlaying) {
                                widget.controller.pause();
                              } else {
                                widget.controller.play();
                              }
                            });
                          },
                          icon: Icon(
                            widget.controller.value.isPlaying
                                ? Icons.pause
                                : Icons.play_arrow,
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                      ],
                    ),
                    
                    // 进度条和时间显示
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          Text(
                            _formatDuration(widget.controller.value.position),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: VideoProgressIndicator(
                              widget.controller,
                              allowScrubbing: true,
                              colors: const VideoProgressColors(
                                playedColor: Colors.white,
                                bufferedColor: Colors.grey,
                                backgroundColor: Colors.black54,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _formatDuration(widget.controller.value.duration),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 视频播放器页面
class VideoPlayerPage extends StatefulWidget {
  final String videoUrl;

  const VideoPlayerPage({
    super.key,
    required this.videoUrl,
  });

  @override
  State<VideoPlayerPage> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  late VideoPlayerController _controller;
  bool _isInitialized = false;
  bool _hasError = false;
  String _errorMessage = '';
  bool _isBuffering = false;
  bool _isLandscape = false;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    try {
      _controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl));
      
      // 监听缓冲状态
      _controller.addListener(_videoListener);
      
      await _controller.initialize();
      
      if (mounted) {
        // 检测视频方向
        final aspectRatio = _controller.value.aspectRatio;
        _isLandscape = aspectRatio > 1.0;
        
        
        // 根据视频方向设置屏幕方向
        if (_isLandscape) {
          SystemChrome.setPreferredOrientations([
            DeviceOrientation.landscapeLeft,
            DeviceOrientation.landscapeRight,
          ]);
        } else {
          SystemChrome.setPreferredOrientations([
            DeviceOrientation.portraitUp,
            DeviceOrientation.portraitDown,
          ]);
        }
        
        setState(() {
          _isInitialized = true;
        });
        
        // 自动播放
        _controller.play();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = '视频加载失败: $e';
        });
      }
    }
  }

  void _videoListener() {
    if (!mounted) return;
    
    final bool isBuffering = _controller.value.isBuffering;
    if (_isBuffering != isBuffering) {
      setState(() {
        _isBuffering = isBuffering;
      });
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_videoListener);
    _controller.dispose();
    
    // 恢复屏幕方向为竖屏
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: _hasError
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.white,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _errorMessage,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _hasError = false;
                          _isInitialized = false;
                        });
                        _initializePlayer();
                      },
                      child: const Text('重试'),
                    ),
                  ],
                ),
              )
            : _isInitialized
                ? Stack(
                    children: [
                      // 视频播放器 - 全屏
                      _isLandscape
                          ? SizedBox.expand(
                              child: VideoPlayer(_controller),
                            )
                          : Center(
                              child: AspectRatio(
                                aspectRatio: _controller.value.aspectRatio,
                                child: VideoPlayer(_controller),
                              ),
                            ),
                      // 视频控制器
                      VideoPlayerControls(
                        controller: _controller,
                        isBuffering: _isBuffering,
                      ),
                      // 返回按钮 - 叠加在视频上
                      Positioned(
                        top: 16,
                        left: 16,
                        child: GestureDetector(
                          onTap: () => Navigator.of(context).pop(),
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: const BoxDecoration(
                              color: Colors.black54,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.arrow_back,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        ),
                      ),
                    ],
                  )
                : Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 3,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          '正在加载视频...',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '请稍候',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
      ),
    );
  }
}

/// 视频播放控制器
class VideoPlayerControls extends StatefulWidget {
  final VideoPlayerController controller;
  final bool isBuffering;

  const VideoPlayerControls({
    super.key,
    required this.controller,
    required this.isBuffering,
  });

  @override
  State<VideoPlayerControls> createState() => _VideoPlayerControlsState();
}

class _VideoPlayerControlsState extends State<VideoPlayerControls> {
  bool _showControls = true;

  @override
  void initState() {
    super.initState();
    _hideControlsAfterDelay();
  }

  void _hideControlsAfterDelay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    
    if (_showControls) {
      _hideControlsAfterDelay();
    }
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleControls,
      child: Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            // 缓冲指示器
            if (widget.isBuffering)
              const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 3,
                    ),
                    SizedBox(height: 8),
                    Text(
                      '缓冲中...',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            
            // 控制器
            AnimatedOpacity(
              opacity: _showControls ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 300),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withOpacity(0.7),
                      Colors.transparent,
                      Colors.transparent,
                      Colors.black.withOpacity(0.7),
                    ],
                  ),
                ),
                child: Column(
                  children: [
                    const Spacer(),
                    // 播放控制
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton(
                          onPressed: () {
                            setState(() {
                              if (widget.controller.value.isPlaying) {
                                widget.controller.pause();
                              } else {
                                widget.controller.play();
                              }
                            });
                          },
                          icon: Icon(
                            widget.controller.value.isPlaying
                                ? Icons.pause
                                : Icons.play_arrow,
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                      ],
                    ),
                    // 进度条和时间显示
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          Text(
                            _formatDuration(widget.controller.value.position),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: VideoProgressIndicator(
                              widget.controller,
                              allowScrubbing: true,
                              colors: const VideoProgressColors(
                                playedColor: Colors.white,
                                bufferedColor: Colors.grey,
                                backgroundColor: Colors.black54,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _formatDuration(widget.controller.value.duration),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}