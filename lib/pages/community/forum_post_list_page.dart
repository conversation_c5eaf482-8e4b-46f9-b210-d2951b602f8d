import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../net/api/forum_service.dart';
import '../../net/http_base_config.dart';
import '../../model/forum_post_list.dart';
import '../../model/forum_category.dart';
import '../../utils/log_util.dart';
import '../../components/cache_image.dart';
import 'community_detail_page.dart';

class ForumPostListPage extends StatefulWidget {
  final ForumCategory category;

  const ForumPostListPage({
    super.key,
    required this.category,
  });

  @override
  State<ForumPostListPage> createState() => _ForumPostListPageState();
}

class _ForumPostListPageState extends State<ForumPostListPage>
    with AutomaticKeepAliveClientMixin {
  bool _isLoading = true;
  String? _error;
  ForumPostList? _postList;
  final ForumService _forumService = ForumService();

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // 使用 WidgetsBinding.instance.addPostFrameCallback 确保在组件完全初始化后再加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadPostList();
      }
    });
  }

  Future<void> _loadPostList() async {
    if (!mounted) return;
    
    LogUtil.d('开始加载帖子列表', tag: 'ForumPostList');
    
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await _forumService.getPostList(
        baseUrl: HttpBaseConfig.forumBaseUrl,
        categoryId: widget.category.categoryId,
        categoryName: widget.category.name, // 传入分类名称，用于设置scope参数
        page: 1,
        pageSize: 10,
      );

      if (!mounted) return;

      LogUtil.d('接口响应: code=${response.code}, message=${response.message}', tag: 'ForumPostList');

      if (response.code == 0 && response.data != null) {
        setState(() {
          _postList = response.data;
          _isLoading = false;
        });
        LogUtil.d('帖子列表加载成功', tag: 'ForumPostList');
      } else {
        setState(() {
          _error = response.message ?? '获取帖子列表失败';
          _isLoading = false;
        });
        LogUtil.w('帖子列表加载失败: ${response.message}', tag: 'ForumPostList');
      }
    } catch (e, stackTrace) {
      if (!mounted) return;
      setState(() {
        _error = '网络错误: $e';
        _isLoading = false;
      });
      LogUtil.e('帖子列表加载异常', error: e, stackTrace: stackTrace, tag: 'ForumPostList');
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return _buildErrorWidget();
    }

    if (_postList == null || _postList!.pageData.isEmpty) {
      return _buildEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: _loadPostList,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _postList!.pageData.length,
        itemBuilder: (context, index) {
          final post = _postList!.pageData[index];
          return _buildPostItem(post);
        },
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _error ?? '加载失败',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadPostList,
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.article_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '暂无${widget.category.name}内容',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostItem(ForumPost post) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部用户信息区域
          GestureDetector(
            onTap: () => _navigateToPostDetail(post),
            child: _buildUserHeader(post),
          ),
          
          const SizedBox(height: 12),
          
          // 帖子标题
          if (post.title.isNotEmpty)
            GestureDetector(
              onTap: () => _navigateToPostDetail(post),
              child: Text(
                post.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                  height: 1.3,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          
          const SizedBox(height: 8),
          
          // 帖子内容预览
            _buildContentPreview(post),
          
          const SizedBox(height: 12),
          
          // 底部信息栏
          GestureDetector(
            onTap: () => _navigateToPostDetail(post),
            child: _buildBottomInfo(post),
          ),
        ],
      ),
    );
  }

  Widget _buildUserHeader(ForumPost post) {
    return Row(
      children: [
        // 用户头像
        CircleAvatar(
          radius: 20,
          backgroundColor: Colors.grey[200],
          backgroundImage: post.user.avatar.isNotEmpty 
              ? NetworkImage(post.user.avatar) 
              : null,
          child: post.user.avatar.isEmpty 
              ? Icon(
                  Icons.person,
                  size: 20,
                  color: Colors.grey[600],
                )
              : null,
        ),
        
        const SizedBox(width: 12),
        
        // 用户信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 用户名
              Text(
                post.user.nickname.isNotEmpty ? post.user.nickname : '匿名用户',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 2),
              
              // 官方账号标识或其他标签
              if (post.user.badge.isNotEmpty)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.blue[200]!, width: 0.5),
                  ),
                  child: Text(
                    '官方账号',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.blue[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMediaContent(ForumPost post) {
    // 优先显示视频，如果没有视频则显示图片
    if (post.content.videos.isNotEmpty) {
      return _buildVideoThumbnail(post.content.videos.first);
    } else if (post.content.images.isNotEmpty) {
      return _buildImageGrid(post.content.images, post.content.images.length > 3 ? 3 : post.content.images.length);
    }
    return const SizedBox.shrink();
  }

  Widget _buildVideoThumbnail(dynamic video) {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 视频缩略图
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.videocam,
              size: 48,
              color: Colors.grey[600],
            ),
          ),
          
          // 播放按钮
          Container(
            width: 60,
            height: 60,
            decoration: const BoxDecoration(
              color: Colors.black54,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.play_arrow,
              color: Colors.white,
              size: 30,
            ),
          ),
          
          // 时长显示
          Positioned(
            bottom: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                '02:30',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomInfo(ForumPost post) {
    return Row(
      children: [
        // 发布时间
        Text(
          _formatTimeAgo(post.createdAt),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[500],
          ),
        ),
        
        const Spacer(),
        
        // 浏览量
        Icon(
          Icons.visibility_outlined,
          size: 16,
          color: Colors.grey[500],
        ),
        const SizedBox(width: 4),
        Text(
          _formatCount(post.viewCount),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[500],
          ),
        ),
      ],
    );
  }

  Widget _buildContentPreview(ForumPost post) {
    final String cleanText = _stripHtmlTags(post.content.text);
    final bool needsTruncation = cleanText.length > 102;
    final String displayText = needsTruncation 
        ? '${cleanText.substring(0, 102)}...' 
        : cleanText;

    // 获取所有图片（包括content.images和图文分离的图片）
    List<dynamic> allImages = _getAllImages(post);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 显示文本内容
        if (displayText.isNotEmpty)
          GestureDetector(
            onTap: () => _navigateToPostDetail(post),
            child: Text(
              displayText,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.4,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        
        // 显示全文按钮（只在文本被截断时显示）
        if (needsTruncation) ...[
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () => _navigateToPostDetail(post),
            child: Text(
              '全文',
              style: TextStyle(
                fontSize: 14,
                color: Colors.blue[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
        
        // 如果有图片，显示图片
        if (allImages.isNotEmpty) ...[
          const SizedBox(height: 8),
          _buildContentImages(allImages),
        ],
      ],
    );
  }

  /// 获取帖子中所有图片（包括content.images和图文分离的图片）
  List<dynamic> _getAllImages(ForumPost post) {
    List<dynamic> allImages = [];
    
    // 先添加content.images中的图片
    if (post.content.images.isNotEmpty) {
      allImages.addAll(post.content.images);
    }
    
    // 判断是否是图文分离的帖子
    final bool isSeparatedContent = post.from != 2 && post.isMixThread != 1;
    
    if (isSeparatedContent) {
      // 从indexes[101]获取图文分离的图片
      final indexes = post.content.indexes;
      
      if (indexes.containsKey('101')) {
        final imageIndex = indexes['101'];
        
        if (imageIndex is Map<String, dynamic> && imageIndex['body'] is List) {
          final bodyList = imageIndex['body'] as List;
          
          for (var item in bodyList) {
            if (item is Map<String, dynamic>) {
              final imageUrl = item['url'] ?? item['thumbUrl'] ?? item['attachment'];
              if (imageUrl != null && imageUrl.isNotEmpty) {
                allImages.add(item);
              }
            }
          }
        }
      }
    }
    
    return allImages;
  }

  Widget _buildContentImages(List<dynamic> images) {
    if (images.isEmpty) return const SizedBox.shrink();
    
    // 最多显示3张图片
    final int displayCount = images.length > 3 ? 3 : images.length;
    
    if (displayCount == 1) {
      // 单张图片
      return _buildSingleImage(images[0], allImages: images);
    } else {
      // 多张图片网格
      return _buildImageGrid(images, displayCount);
    }
  }

  Widget _buildSingleImage(dynamic imageData, {List<dynamic>? allImages}) {
    String? imageUrl;
    
    // 尝试从不同字段获取图片URL
    if (imageData is Map<String, dynamic>) {
      imageUrl = imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
    } else if (imageData is String) {
      imageUrl = imageData;
    }
    
    if (imageUrl == null || imageUrl.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[200],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: CachedImage(
          imageUrl: imageUrl,
          width: double.infinity,
          height: 200,
          fit: BoxFit.cover,
          onTap: () => _showImageGallery(context, allImages ?? [imageData], 0),
        ),
      ),
    );
  }

  Widget _buildImageGrid(List<dynamic> images, int displayCount) {
    return SizedBox(
      height: 120,
      child: Row(
        children: List.generate(displayCount, (index) {
          final imageData = images[index];
          String? imageUrl;
          
          if (imageData is Map<String, dynamic>) {
            imageUrl = imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
          } else if (imageData is String) {
            imageUrl = imageData;
          }
          
          return Expanded(
            child: GestureDetector(
              onTap: () => _showImageGallery(context, images, index),
              child: Container(
                margin: EdgeInsets.only(right: index < (displayCount - 1) ? 4 : 0),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  color: Colors.grey[200],
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: imageUrl != null && imageUrl.isNotEmpty
                          ? CachedImage(
                              imageUrl: imageUrl,
                              width: double.infinity,
                              height: double.infinity,
                              fit: BoxFit.cover,
                            )
                          : Container(
                              color: Colors.grey[200],
                              child: Icon(
                                Icons.image,
                                color: Colors.grey[600],
                              ),
                            ),
                    ),
                    
                    // 如果是最后一张图片且还有更多图片，显示"+N"标识
                    if (index == (displayCount - 1) && images.length > displayCount)
                      Container(
                        width: double.infinity,
                        height: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6),
                          color: Colors.black.withValues(alpha: 0.5),
                        ),
                        child: Center(
                          child: Text(
                            '+${images.length - displayCount}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  String _formatTimeAgo(String createdAt) {
    try {
      final DateTime postTime = DateTime.parse(createdAt);
      final DateTime now = DateTime.now();
      final Duration diff = now.difference(postTime);
      
      if (diff.inMinutes < 60) {
        return '${diff.inMinutes}分钟前';
      } else if (diff.inHours < 24) {
        return '${diff.inHours}小时前';
      } else if (diff.inDays < 30) {
        return '${diff.inDays}天前';
      } else {
        return createdAt.substring(0, 10);
      }
    } catch (e) {
      return '刚刚';
    }
  }

  String _formatCount(int count) {
    if (count > 10000) {
      return '${(count / 10000).toStringAsFixed(1)}w';
    } else if (count > 1000) {
      return '${(count / 1000).toStringAsFixed(1)}k';
    } else {
      return count.toString();
    }
  }

  String _stripHtmlTags(String html) {
    if (html.isEmpty) return '';
    
    // 移除HTML标签
    String result = html.replaceAll(RegExp(r'<[^>]*>', multiLine: true, caseSensitive: false), '');
    
    // 替换HTML实体
    result = result
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&amp;', '&')
        .replaceAll('&quot;', '"')
        .replaceAll('&#39;', "'")
        .replaceAll('&mdash;', '—')
        .replaceAll('&ldquo;', '"')
        .replaceAll('&rdquo;', '"');
    
    // 移除多余的空白字符和换行
    result = result
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
    
    return result;
  }

  /// 跳转到帖子详情页
  void _navigateToPostDetail(ForumPost post) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CommunityDetailPage(post: post),
      ),
    );
  }

  /// 显示图片画廊（支持左右滑动）
  void _showImageGallery(BuildContext context, List<dynamic> images, int initialIndex) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (BuildContext context) {
        return _ImageGalleryDialog(images: images, initialIndex: initialIndex);
      },
    );
  }
}

/// 图片画廊弹窗组件
class _ImageGalleryDialog extends StatefulWidget {
  final List<dynamic> images;
  final int initialIndex;

  const _ImageGalleryDialog({
    required this.images,
    required this.initialIndex,
  });

  @override
  State<_ImageGalleryDialog> createState() => _ImageGalleryDialogState();
}

class _ImageGalleryDialogState extends State<_ImageGalleryDialog> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  String? _getImageUrl(dynamic imageData) {
    if (imageData is Map<String, dynamic>) {
      return imageData['url'] ?? imageData['thumbUrl'] ?? imageData['attachment'];
    } else if (imageData is String) {
      return imageData;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Navigator.of(context).pop(),
      child: Dialog.fullscreen(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            // 背景可点击区域
            Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.transparent,
            ),
            // 图片轮播
            PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemCount: widget.images.length,
              itemBuilder: (context, index) {
                final imageUrl = _getImageUrl(widget.images[index]);
                if (imageUrl == null || imageUrl.isEmpty) {
                  return const Center(
                    child: Icon(
                      Icons.broken_image,
                      size: 64,
                      color: Colors.white54,
                    ),
                  );
                }

                return Center(
                  child: InteractiveViewer(
                    panEnabled: true,
                    scaleEnabled: true,
                    minScale: 0.5,
                    maxScale: 3.0,
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      fit: BoxFit.contain,
                      placeholder: (context, url) => const Center(
                        child: CircularProgressIndicator(
                          color: Colors.white,
                        ),
                      ),
                      errorWidget: (context, url, error) => const Center(
                        child: Icon(
                          Icons.broken_image,
                          size: 64,
                          color: Colors.white54,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
            // 页面指示器
            if (widget.images.length > 1)
              Positioned(
                bottom: 100,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${_currentIndex + 1} / ${widget.images.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            // 关闭按钮
            Positioned(
              top: 50,
              right: 20,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    color: Colors.black54,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}