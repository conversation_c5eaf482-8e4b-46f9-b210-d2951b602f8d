import 'dart:io';

import 'package:getuiflut/getuiflut.dart';

class SqPushManager {
  static final SqPushManager _instance = SqPushManager._internal();

  factory SqPushManager() => _instance;

  SqPushManager._internal();

  String _payloadInfo = 'Null';
  String _userMsg = "";
  String _notificationState = "";
  String _getClientId = "";
  String _getDeviceToken = "";
  String _onReceivePayload = "";
  String _onReceiveNotificationResponse = "";
  String _onAppLinkPayLoad = "";

  void init() {
    _initGetuiSdk();
  }

  Future<void> _initGetuiSdk() async {
    try {
      if (Platform.isAndroid) {
        Getuiflut.initGetuiSdk;
      } else if (Platform.isIOS) {
        Getuiflut().startSdk(
          appId: "xwdkpQLlfk8Azm2eErDqq9",
          appKey: "sxa6Dr0Fpq5lTcxQgB5LS1",
          appSecret: "2WEKOnlSSR6TTvPnAbm5N5",
        );
      }
      addEventHandler();
    } catch (e) {
      e.toString();
    }
  }

  void addEventHandler() {
    Getuiflut().addEventHandler(
      onReceiveClientId: (String message) async {
        print("flutter onReceiveClientId: $message");
        _getClientId = "$message";
      },
      onReceiveMessageData: (Map<String, dynamic> msg) async {
        print("flutter onReceiveMessageData: $msg");
        _payloadInfo = msg['payload'];
      },
      onNotificationMessageArrived: (Map<String, dynamic> msg) async {
        print("flutter onNotificationMessageArrived: $msg");
        _notificationState = 'Arrived';
      },
      onNotificationMessageClicked: (Map<String, dynamic> msg) async {
        print("flutter onNotificationMessageClicked: $msg");
        _notificationState = 'Clicked';
      },
      onTransmitUserMessageReceive: (Map<String, dynamic> msg) async {
        print("flutter onTransmitUserMessageReceive:$msg");
        _userMsg = msg["msg"];
      },
      onRegisterDeviceToken: (String message) async {
        print("flutter onRegisterDeviceToken: $message");
        _getDeviceToken = "$message";
      },
      onReceivePayload: (Map<String, dynamic> message) async {
        print("flutter onReceivePayload: $message");
        _onReceivePayload = "$message";
      },
      onReceiveNotificationResponse: (Map<String, dynamic> message) async {
        print("flutter onReceiveNotificationResponse: $message");
        _onReceiveNotificationResponse = "$message";
      },
      onAppLinkPayload: (String message) async {
        print("flutter onAppLinkPayload: $message");
        _onAppLinkPayLoad = "$message";
      },
      onPushModeResult: (Map<String, dynamic> message) async {
        print("flutter onPushModeResult: $message");
      },
      onSetTagResult: (Map<String, dynamic> message) async {
        print("flutter onSetTagResult: $message");
      },
      onAliasResult: (Map<String, dynamic> message) async {
        print("flutter onAliasResult: $message");
      },
      onQueryTagResult: (Map<String, dynamic> message) async {
        print("flutter onQueryTagResult: $message");
      },
      onWillPresentNotification: (Map<String, dynamic> message) async {
        print("flutter onWillPresentNotification: $message");
      },
      onOpenSettingsForNotification: (Map<String, dynamic> message) async {
        print("flutter onOpenSettingsForNotification: $message");
      },
      onGrantAuthorization: (String granted) async {
        print("flutter onGrantAuthorization: $granted");
      },
      onLiveActivityResult: (Map<String, dynamic> message) async {
        print("flutter onLiveActivityResult: $message");
      },
      onRegisterPushToStartTokenResult: (Map<String, dynamic> message) async {
        print("flutter onRegisterPushToStartTokenResult: $message");
      },
      onReceiveOnlineState: (String online) async {
        print("flutter onReceiveOnlineState: $online");
      },
    );
  }
}
