import 'dart:async';
import 'package:dlyz_flutter/manager/network_manager.dart';
import 'package:dlyz_flutter/pages/community/CommunityPage.dart';
import 'package:dlyz_flutter/pages/demo/DemoPage.dart';
import 'package:dlyz_flutter/pages/games/GamesPage.dart';
import 'package:dlyz_flutter/pages/profile/ProfilePage.dart';
import 'package:dlyz_flutter/pages/splash/SplashPage.dart';
import 'package:dlyz_flutter/pages/privacy/privacy_check_page.dart';
import 'package:dlyz_flutter/pages/demo/proxy_config_page.dart';
import 'package:flutter/foundation.dart';
import 'package:dlyz_flutter/services/download/ALDownloader.dart';
import 'package:dlyz_flutter/track/track.dart';
import 'package:dlyz_flutter/utils/log_util.dart';
import 'package:dlyz_flutter/utils/sp_utils.dart';
import 'package:dlyz_flutter/config/app_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'bugless/app_error_handler.dart';
import 'info/forum_info.dart';
import 'providers/login_provider.dart';
import 'providers/user_provider.dart';
import 'providers/download_provider.dart';

void main() async {
  AppErrorHandler.run(const MyApp(), initFunc: () async {
    await initApp();
  });
}

///初始化
Future<void> initApp() async {
  await SqTrackManager.init();
  await SpManager.init();
  
  // 初始化应用配置
  await AppConfig.initialize();

  ALDownloader.initialize();
  ALDownloader.configurePrint(true, frequentEnabled: false);

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown
  ]);
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

  }

  @override
  void dispose() {
    // 销毁网络监听
    NetworkManager().dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 当应用暂停或退出时暂停所有下载任务
    if (state == AppLifecycleState.paused || state == AppLifecycleState.detached) {
      ALDownloader.pauseAll();
    }
  }

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => LoginStateProvider()),
        ChangeNotifierProvider(create: (_) => UserProvider()),
        ChangeNotifierProvider(create: (_) => DownloadProvider()),
      ],
      child: MaterialApp(
        theme: ThemeData(
          // 全局背景色
          scaffoldBackgroundColor: Colors.white,
        ),
        initialRoute: '/',
        routes: {
          '/': (context) => const PrivacyCheckPage(),
          '/splash': (context) => const SplashPage(),
          '/home': (context) => const MyHomePage(),
        },
        onGenerateRoute: (RouteSettings settings) {
          switch (settings.name) {
            case '/proxy-config':
              // 只在Debug模式下允许访问代理配置页面
              if (kDebugMode) {
                return MaterialPageRoute(
                  builder: (context) => const ProxyConfigPage(),
                  settings: settings,
                );
              }
              // 在Release模式下返回null，会fallback到onUnknownRoute
              return null;
            default:
              return null;
          }
        },
      ),
    );
  }

}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _currentIndex = 0;
  DateTime? _lastBackPressTime;
  bool _isLoadingForumConfig = true;

  final List<Widget> _pages = const [
    CommunityPage(),
    GamesPage(),
    ProfilePage(),
    DemoPage(title: 'Demo')
  ];

  @override
  void initState() {
    super.initState();
    _initializeForumConfig();
    _initializeLoginProvider();
  }

  Future<void> _initializeLoginProvider() async {
    try {
      final loginProvider = Provider.of<LoginStateProvider>(context, listen: false);
      await loginProvider.initialize();
    } catch (e) {
      print('LoginProvider 初始化失败: $e');
    }
  }

  Future<void> _initializeForumConfig() async {
    try {
      await ForumInfo().initialize();
    } catch (e) {
      print('论坛配置初始化失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingForumConfig = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoadingForumConfig) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return PopScope(
      onPopInvokedWithResult: _onWillPop,
      child: Scaffold(
        // 移除AppBar
        body: SafeArea(
          child: _pages[_currentIndex],
        ),
      // 底部导航栏
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: SizedBox.shrink(),
            label: '圈子',
          ),
          BottomNavigationBarItem(
            icon: SizedBox.shrink(),
            label: '游戏',
          ),
          BottomNavigationBarItem(
            icon: SizedBox.shrink(),
            label: '我的',
          ),
          BottomNavigationBarItem(
            icon: SizedBox.shrink(),
            label: 'Demo',
          ),
        ],
        // 设置选中文字样式
        selectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.bold,
        ),
        // 设置未选中文字样式
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.normal,
        ),
        // 显示选中和未选中的标签
        showSelectedLabels: true,
        showUnselectedLabels: true,
      ),
      ),
    );
  }

  void _onWillPop(bool didPop, dynamic result) async {
    if (!didPop) {
      final now = DateTime.now();

      if (_lastBackPressTime == null ||
          now.difference(_lastBackPressTime!).inMilliseconds > 2000) {
        // 第一次按返回键或超过2秒
        _lastBackPressTime = now;

        // 显示提示
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('再按一次退出应用'),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.black87,
          ),
        );
      } else {
        // 第二次按返回键，退出应用
        Navigator.of(context).pop();
      }
    }
  }
}
