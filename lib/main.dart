import 'package:dlyz_flutter/pages/community/CommunityPage.dart';
import 'package:dlyz_flutter/pages/demo/DemoPage.dart';
import 'package:dlyz_flutter/pages/games/GamesPage.dart';
import 'package:dlyz_flutter/pages/profile/ProfilePage.dart';
import 'package:dlyz_flutter/pages/splash/SplashPage.dart';
import 'package:dlyz_flutter/services/download/ALDownloader.dart';
import 'package:dlyz_flutter/track/track.dart';
import 'package:dlyz_flutter/utils/sp_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'bugless/app_error_handler.dart';
import 'providers/login_provider.dart';
import 'providers/game_provider.dart';

void main() async {
  AppErrorHandler.run(const MyApp(), initFunc: () async {
    await initApp();
  });
}

///初始化
Future<void> initApp() async {
  await SqTrackManager.init();
  await SpManager.init();

  ALDownloader.initialize();
  ALDownloader.configurePrint(true, frequentEnabled: false);

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown
  ]);
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => LoginProvider()),
        ChangeNotifierProvider(create: (_) => GameProvider()),
      ],
      child: MaterialApp(
        title: 'Flutter Demo',
        theme: ThemeData(
          // This is the theme of your application.
          //
          // TRY THIS: Try running your application with "flutter run". You'll see
          // the application has a purple toolbar. Then, without quitting the app,
          // try changing the seedColor in the colorScheme below to Colors.green
          // and then invoke "hot reload" (save your changes or press the "hot
          // reload" button in a Flutter-supported IDE, or press "r" if you used
          // the command line to start the app).
          //
          // Notice that the counter didn't reset back to zero; the application
          // state is not lost during the reload. To reset the state, use hot
          // restart instead.
          //
          // This works for code too, not just values: Most code changes can be
          // tested with just a hot reload.
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        ),
        initialRoute: '/',
        routes: {
          '/': (context) => const SplashPage(),
          '/home': (context) => const MyHomePage(title: 'Flutter Demo Home Page'),
        },
      ),
    );
  }

}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _currentIndex = 0;
  DateTime? _lastBackPressTime;

  final List<Widget> _pages = const [
    CommunityPage(),
    GamesPage(),
    ProfilePage(),
    DemoPage(title: 'Demo')
  ];

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        // 移除AppBar
        body: SafeArea(
          child: _pages[_currentIndex],
        ),
      // 底部导航栏
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: SizedBox.shrink(),
            label: '圈子',
          ),
          BottomNavigationBarItem(
            icon: SizedBox.shrink(),
            label: '游戏',
          ),
          BottomNavigationBarItem(
            icon: SizedBox.shrink(),
            label: '我的',
          ),
          BottomNavigationBarItem(
            icon: SizedBox.shrink(),
            label: 'Demo',
          ),
        ],
        // 设置选中文字样式
        selectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.bold,
        ),
        // 设置未选中文字样式
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.normal,
        ),
        // 显示选中和未选中的标签
        showSelectedLabels: true,
        showUnselectedLabels: true,
      ),
      ),
    );
  }

  Future<bool> _onWillPop() async {
    final now = DateTime.now();
    
    if (_lastBackPressTime == null || 
        now.difference(_lastBackPressTime!).inMilliseconds > 2000) {
      // 第一次按返回键或超过2秒
      _lastBackPressTime = now;
      
      // 显示提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('再按一次退出应用'),
          duration: Duration(seconds: 2),
          backgroundColor: Colors.black87,
        ),
      );
      
      return false; // 不退出应用
    } else {
      // 第二次按返回键，退出应用
      return true;
    }
  }
}
