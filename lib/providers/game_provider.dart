import 'dart:async';

import 'package:flutter/material.dart';
import '../beans/download_bean.dart';
import '../services/download/ALDownloader.dart';
import '../services/download/ALDownloaderHandlerInterface.dart';
import '../services/download/ALDownloaderStatus.dart';
import '../manager/channel_manager.dart';
import '../services/download/ALDownloaderFileManager.dart';
import '../services/download/internal/ALDownloaderFileManagerDefault.dart';

class GameProvider extends ChangeNotifier {
  final List<DownloadBean> _gameItems = [
    DownloadBean(
      title: '港台云上城之歌',
      subtitle: '这一次，重新定义斗罗世界！',
      imageUrl: 'https://imgcs.s98s2.com/aicc/79_5220/_cN8k8UoR/1920%C3%971080%E5%BB%96%E6%96%B020250626.jpg',
      packageName: 'com.tg.ysczg.tw',
      url: 'https://developer-mt.nofeba.com/media/DiskExtension/android_oversea_pack_output/2025_07_30/1000100_com_tg_ysczg_tw_seasdk_37000000_20250730_095649_1753840621.apk',
      progress: '0',
      downloadStatus: '下载',
      tag: '今日公测开启',
    ),
    DownloadBean(
      title: '斗罗大陆：猎魂世界',
      subtitle: '真正能打的斗罗大陆',
      imageUrl: 'https://imgcs.s98s2.com/image/webSite/article/1752072193000/MMO%E4%BB%A3%E8%A8%80%E4%BA%BA%E6%88%90%E9%BE%99.jpg',
      packageName: 'com.tt.lhgzs.dddllhsj',
      url: 'https://dlcs.37tgy.com/upload/1_1023086_19077/douluodaluliehunshijie-pinzhuanguanggaosibu37zigengxin2_1001.apk',
      progress: '0',
      downloadStatus: '下载',
    ),
    DownloadBean(
      title: '指尖像素城',
      subtitle: '1000万预约达成!',
      imageUrl: 'https://imgcs.s98s2.com/image/webSite/article/1752195478000/%E6%88%90%E5%9B%BE.jpg',
      packageName: 'com.gzqh.zjxsc.huawei',
      url: 'https://developer-mt.nofeba.com/media/android_mainland_pack_output/2025_06_23/huawei_20250618-dcb8ab4f-8196-4948-84f8-8f964f875134_20250623_1750652824.apk',
      progress: '0',
      downloadStatus: '下载',
    ),
  ];

  List<DownloadBean> get gameItems => _gameItems;

  // 初始化下载处理器
  void initializeDownloadHandlers() {
    for (DownloadBean item in _gameItems) {
      _addDownloadHandlerInterface(item);
    }
  }

  Future<void> _addDownloadHandlerInterface(DownloadBean item) async {
    ALDownloader.addHandlerInterface(
        ALDownloaderHandlerInterface(progressHandler: (progress, speed) {
          item.downloadStatusNotifier.value = '下载中';
          item.downloadSpeedNotifier.value = formatSpeed(speed);
          final targetProgress = progress * 100;
          _updateProgressWithAnimation(item, targetProgress);
        }, succeededHandler: () async {
          item.downloadStatusNotifier.value = '安装';
          final physicalFilePath = await ALDownloaderFileManager.getPhysicalFilePathForUrl(item.url);
          if (physicalFilePath != null) {
            // 使用原生方法打开APK
            final channelManager = ChannelManager();
            await channelManager.installApk(physicalFilePath);
          }
        }, failedHandler: () {
          item.downloadStatusNotifier.value = "下载";
          item.cacheProgress = 0.0;
        }, pausedHandler: () {
          item.progressTimer?.cancel();
          item.progressNotifier.value = "${item.cacheProgress?.toStringAsFixed(2)}%";
          item.downloadStatusNotifier.value = "继续";
        }),
        item.url
    );
  }

  Future<void> handleDownload(DownloadBean game) async {
    final uri = Uri.parse(game.url);
    final pathSegments = uri.pathSegments;
    final originalFileName = pathSegments.isNotEmpty ? pathSegments.last : '';
    final fileExtension = originalFileName.contains('.') ? originalFileName.split('.').last : '';
    final newFileName = '${game.title}.$fileExtension';

    // 获取默认目录路径
    final directoryPath = await ALDownloaderFileManagerDefault.getVirtualDirectoryPathForUrl(game.url);

    final status = await ALDownloader.getStatusForUrl(game.url);
    if (status == ALDownloaderStatus.downloading) {
      ALDownloader.pause(game.url);
    } else {
      ALDownloader.download(
          game.url,
          directoryPath: directoryPath,
          fileName: newFileName
      );
    }
    notifyListeners();
  }

  Future<void> handleRemove(DownloadBean game) async {
    ALDownloader.remove(game.url);
    game.cacheProgress = 0.0;
    game.progressNotifier.value = "0";
  }

  String formatSpeed(double speedInBytes) {
    if (speedInBytes < 1024) {
      return "${speedInBytes.toStringAsFixed(1)} B/s";
    } else if (speedInBytes < 1024 * 1024) {
      return "${(speedInBytes / 1024).toStringAsFixed(1)} KB/s";
    } else {
      return "${(speedInBytes / (1024 * 1024)).toStringAsFixed(1)} MB/s";
    }
  }

  /// 处理进度更新动画
  void _updateProgressWithAnimation(DownloadBean item, double targetProgress) {
    final currentProgress = item.cacheProgress ?? 0.0;

    if (targetProgress <= 0.0) {
      item.progressNotifier.value = "${targetProgress.toStringAsFixed(2)}%";
      return;
    }

    if ((targetProgress - currentProgress).abs() > 0.005) {
      item.progressTimer?.cancel();

      final difference = targetProgress - currentProgress;
      final steps = (difference.abs() / 0.005).ceil();
      final stepValue = difference / steps;
      int currentStep = 0;

      // 使用定时器实现平滑过渡
      item.progressTimer = Timer.periodic(const Duration(milliseconds: 1), (timer) {
        currentStep++;
        final stepProgress = stepValue * currentStep;
        final newProgress = currentProgress + stepProgress;

        final clampedProgress = difference > 0
            ? newProgress.clamp(currentProgress, targetProgress)
            : newProgress.clamp(targetProgress, currentProgress);

        item.cacheProgress = clampedProgress;
        item.progressNotifier.value = "${clampedProgress.toStringAsFixed(2)}%";

        if (currentStep >= steps || clampedProgress == targetProgress) {
          timer.cancel();
          item.progressTimer = null;
          item.cacheProgress = targetProgress;
          item.progressNotifier.value = "${targetProgress.toStringAsFixed(2)}%";
        }
      });
    } else {
      // 如果变化很小，直接更新
      item.cacheProgress = targetProgress;
      item.progressNotifier.value = "${targetProgress.toStringAsFixed(2)}%";
    }
  }
}