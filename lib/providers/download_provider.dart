import 'dart:async';
import 'dart:io';

import 'package:dlyz_flutter/manager/game_data_manager.dart';
import 'package:dlyz_flutter/model/connectivity_result.dart';
import 'package:dlyz_flutter/model/game_card_info.dart';
import 'package:dlyz_flutter/model/game_package_info.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../components/common_alert.dart';
import '../manager/network_manager.dart';
import '../model/download_info.dart';
import '../services/download/ALDownloader.dart';
import '../services/download/ALDownloaderHandlerInterface.dart';
import '../services/download/ALDownloaderStatus.dart';
import '../manager/channel_manager.dart';
import '../services/download/ALDownloaderFileManager.dart';
import '../services/download/ALDownloaderTask.dart';
import '../services/download/internal/ALDownloaderFileManagerDefault.dart';

class DownloadProvider extends ChangeNotifier {

  List<ALDownloaderTask> tasks = [];
  final List<GameCardInfo> _gameItems = GameDataManager().gameItems;

  List<GameCardInfo> get gameItems => _gameItems;

  DownloadProvider() {
    initializeDownloadHandlers();
    reloadDownloadTasks();
  }

  // 初始化下载处理器
  void initializeDownloadHandlers() async {
    for (GameCardInfo item in _gameItems) {
      if (item.downloadInfo.url.isNotEmpty) {
        _addDownloadHandlerInterface(item.downloadInfo);
      }
    }
  }

  void reloadDownloadTasks() async {
    var alTasks = await ALDownloader.tasks;
    alTasks = alTasks.where((task) => task.file.fileName.isNotEmpty).toList();
    tasks = alTasks;
    if (tasks.isNotEmpty) {
      for (ALDownloaderTask task in tasks) {
        var gameItem = _gameItems.firstWhere((item) => item.downloadInfo.url == task.url);
        var targetProgress = task.progress * 100;
        gameItem.downloadInfo.cacheProgress = targetProgress;
        gameItem.downloadInfo.progressNotifier.value = "${targetProgress.toStringAsFixed(2)}%";
        if (task.status == ALDownloaderStatus.paused || task.status == ALDownloaderStatus.downloading) {
          gameItem.downloadInfo.downloadStatusNotifier.value = "继续";
        } else if (task.status == ALDownloaderStatus.succeeded) {
          gameItem.downloadInfo.downloadStatusNotifier.value = "安装";
        }
      }
    }
  }

  Future<void> _addDownloadHandlerInterface(DownloadInfo item) async {
    ALDownloader.addHandlerInterface(
        ALDownloaderHandlerInterface(progressHandler: (progress, speed) {
          item.downloadStatusNotifier.value = '下载中';
          item.downloadSpeedNotifier.value = formatSpeed(speed);
          final targetProgress = progress * 100;
          _updateProgressWithAnimation(item, targetProgress);
        }, succeededHandler: () async {
          item.downloadStatusNotifier.value = '安装';
          final physicalFilePath = await ALDownloaderFileManager.getPhysicalFilePathForUrl(item.url);
          if (physicalFilePath != null) {
            // 使用原生方法打开APK
            final channelManager = ChannelManager();
            await channelManager.installApk(physicalFilePath);
          }
        }, failedHandler: () {
          item.downloadStatusNotifier.value = "下载";
          item.cacheProgress = 0.0;
        }, pausedHandler: () {
          item.progressTimer?.cancel();
          item.progressNotifier.value = "${item.cacheProgress?.toStringAsFixed(2)}%";
          item.downloadStatusNotifier.value = "继续";
        }),
        item.url
    );
  }

  Future<void> handleDownload(DownloadInfo info) async {
    if (Platform.isAndroid) {
      if (info.fileName.isEmpty) {
        info.fileName = getDownloadFileName(info);
      }
      if (info.directoryPath.isEmpty) {
        info.directoryPath = await ALDownloaderFileManagerDefault.getVirtualDirectoryPathForUrl(info.url);
      }

      final status = await ALDownloader.getStatusForUrl(info.url);
      if (status == ALDownloaderStatus.downloading) {
        ALDownloader.pause(info.url);
      } else {
        ALDownloader.download(
            info.url,
            directoryPath: info.directoryPath,
            fileName: info.fileName
        );
      }
      notifyListeners();
    } else if (Platform.isIOS) {
      final bool? success = await ChannelManager().openUrl(url: info.url);
      if (success != true) {
        Fluttertoast.showToast(
          msg: '无法打开URL: ${info.url}',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.green,
          textColor: Colors.white,
          fontSize: 16.0,
        );
      }
    }
  }

  Future<DownloadDecision> handleDownloadWithDecision(DownloadInfo info) async {
    if (Platform.isAndroid) {
      if (info.fileName.isEmpty) {
        info.fileName = getDownloadFileName(info);
      }
      if (info.directoryPath.isEmpty) {
        info.directoryPath = await ALDownloaderFileManagerDefault.getVirtualDirectoryPathForUrl(info.url);
      }

      final status = await ALDownloader.getStatusForUrl(info.url);
      if (status == ALDownloaderStatus.downloading) {
        ALDownloader.pause(info.url);
        return DownloadDecision.paused;
      } else {
        final networkStatus = await NetworkManager().checkNetworkConnectivity();
        bool isWifi = networkStatus.contains(ConnectivityResult.wifi);
        bool isMobile = networkStatus.contains(ConnectivityResult.mobile);

        if (isWifi) {
          ALDownloader.download(
            info.url,
            directoryPath: info.directoryPath,
            fileName: info.fileName
          );
          return DownloadDecision.started;
        } else if (isMobile) {
          // 需要二次确认
          return DownloadDecision.requiresConfirmation;
        } else {
          return DownloadDecision.noNetwork;
        }
      }
    } else if (Platform.isIOS) {
      final bool? success = await ChannelManager().openUrl(url: info.url);
      if (success != true) {
        Fluttertoast.showToast(
          msg: '无法打开URL: ${info.url}',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.CENTER,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.green,
          textColor: Colors.white,
          fontSize: 16.0,
        );
      }
    }
    return DownloadDecision.unknown;
  }

  Future<void> scheduleDownloadOnWifi(DownloadInfo info) async {
    // 等候WiFi状态自动下载
    Fluttertoast.showToast(
      msg: '已添加到WiFi下载队列',
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.CENTER,
    );

    NetworkManager().addListener(onConnectivityChanged: (List<ConnectivityResult> result) {
      // 这里可以处理网络状态变化
      if (result.contains(ConnectivityResult.wifi)) {
        ALDownloader.download(
            info.url,
            directoryPath: info.directoryPath,
            fileName: info.fileName
        );
      } else {
        ALDownloader.pause(info.url);
      }
    });
  }

  Future<void> handleRemove(DownloadInfo game) async {
    ALDownloader.remove(game.url);
    game.cacheProgress = 0.0;
    game.progressNotifier.value = "0";
  }

  String formatSpeed(double speedInBytes) {
    if (speedInBytes < 1024) {
      return "${speedInBytes.toStringAsFixed(1)} B/s";
    } else if (speedInBytes < 1024 * 1024) {
      return "${(speedInBytes / 1024).toStringAsFixed(1)} KB/s";
    } else {
      return "${(speedInBytes / (1024 * 1024)).toStringAsFixed(1)} MB/s";
    }
  }

  String getDownloadFileName(DownloadInfo info) {
    final uri = Uri.parse(info.url);
    final pathSegments = uri.pathSegments;
    final originalFileName = pathSegments.isNotEmpty ? pathSegments.last : '';
    final fileExtension = originalFileName.contains('.') ? originalFileName.split('.').last : '';
    return '${info.name}.$fileExtension';
  }

  /// 处理进度更新动画
  void _updateProgressWithAnimation(DownloadInfo item, double targetProgress) {
    final currentProgress = item.cacheProgress ?? 0.0;

    if (targetProgress <= 0.0) {
      item.progressNotifier.value = "${targetProgress.toStringAsFixed(2)}%";
      return;
    }

    if ((targetProgress - currentProgress).abs() > 0.005) {
      item.progressTimer?.cancel();

      final difference = targetProgress - currentProgress;
      final steps = (difference.abs() / 0.005).ceil();
      final stepValue = difference / steps;
      int currentStep = 0;

      // 使用定时器实现平滑过渡
      item.progressTimer = Timer.periodic(const Duration(milliseconds: 1), (timer) {
        currentStep++;
        final stepProgress = stepValue * currentStep;
        final newProgress = currentProgress + stepProgress;

        final clampedProgress = difference > 0
            ? newProgress.clamp(currentProgress, targetProgress)
            : newProgress.clamp(targetProgress, currentProgress);

        item.cacheProgress = clampedProgress;
        item.progressNotifier.value = "${clampedProgress.toStringAsFixed(2)}%";

        if (currentStep >= steps || clampedProgress == targetProgress) {
          timer.cancel();
          item.progressTimer = null;
          item.cacheProgress = targetProgress;
          item.progressNotifier.value = "${targetProgress.toStringAsFixed(2)}%";
        }
      });
    } else {
      // 如果变化很小，直接更新
      item.cacheProgress = targetProgress;
      item.progressNotifier.value = "${targetProgress.toStringAsFixed(2)}%";
    }
  }
}

enum DownloadDecision {
  started,
  paused,
  requiresConfirmation,
  noNetwork,
  iosHandled,
  unknown
}