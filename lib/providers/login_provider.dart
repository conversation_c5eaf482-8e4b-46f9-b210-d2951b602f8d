import 'package:flutter/material.dart';
import '../net/example/mock_api.dart';
import '../model/user_info.dart';
import '../services/user_storage_service.dart';
import '../services/fast_login_service.dart';
import '../manager/channel_manager.dart';

/// 登录Provider类
class LoginProvider extends ChangeNotifier {
  final MockApiService _apiService = MockApiService();
  final UserStorageService _storageService = UserStorageService();
  final FastLoginService _fastLoginService = FastLoginService();
  
  // 数据管理
  String _currentPhoneNumber = '';
  String _errorMessage = '';
  List<UserInfo> _userList = [];
  UserInfo? _selectedUser;
  bool _isInitialized = false;
  bool _isLoading = false;
  bool _isShowingFastLogin = false;
  
  // Getters
  String get errorMessage => _errorMessage;
  String get currentPhoneNumber => _currentPhoneNumber;
  List<UserInfo> get userList => _userList;
  UserInfo? get selectedUser => _selectedUser;
  bool get isInitialized => _isInitialized;
  bool get isLoading => _isLoading;
  bool get isShowingFastLogin => _isShowingFastLogin;
  
  /// 发送验证码
  Future<bool> sendVerifyCode(String phoneNumber) async {
    _currentPhoneNumber = phoneNumber;
    
    try {
      final success = await _apiService.sendVerifyCode(phoneNumber);
      
      if (success) {
        _errorMessage = '';
      } else {
        _errorMessage = '验证码发送失败，请重试';
      }
      
      notifyListeners();
      return success;
    } catch (e) {
      _errorMessage = '网络错误，请检查网络连接';
      notifyListeners();
      return false;
    }
  }
  
  /// 验证验证码
  Future<bool> verifyCode(String phoneNumber, String verifyCode) async {
    try {
      final success = await _apiService.verifyCode(phoneNumber, verifyCode);
      
      if (success) {
        _errorMessage = '';
      } else {
        _errorMessage = '验证码错误';
      }
      
      notifyListeners();
      return success;
    } catch (e) {
      _errorMessage = '网络错误，请检查网络连接';
      notifyListeners();
      return false;
    }
  }
  
  /// 登录
  Future<Map<String, dynamic>> login({
    required String phoneNumber,
    String? password,
    String? verifyCode,
  }) async {
    _currentPhoneNumber = phoneNumber;
    
    try {
      final result = await _apiService.login(
        phoneNumber: phoneNumber,
        password: password,
        verifyCode: verifyCode,
      );
      
      if (result['success']) {
        _errorMessage = '';
      } else {
        _errorMessage = result['message'];
      }
      
      notifyListeners();
      return result;
    } catch (e) {
      _errorMessage = '网络错误，请检查网络连接';
      notifyListeners();
      return {
        'success': false,
        'message': '网络错误，请检查网络连接',
        'data': null,
      };
    }
  }

  /// 验证码登录
  Future<Map<String, dynamic>> loginWithVerifyCode({
    required String phoneNumber,
    required String verifyCode,
  }) async {
    _currentPhoneNumber = phoneNumber;
    
    try {
      final result = await _apiService.loginWithVerifyCode(
        phoneNumber: phoneNumber,
        verifyCode: verifyCode,
      );
      
      if (result['success']) {
        _errorMessage = '';
        
        // 登录成功，保存用户信息到历史账号
        if (result['data'] != null && result['data']['userInfo'] != null) {
          final userInfo = UserInfo.fromJson(result['data']['userInfo']);
          addUser(userInfo);
        }
      } else {
        _errorMessage = result['message'];
      }
      
      notifyListeners();
      return result;
    } catch (e) {
      _errorMessage = '网络错误，请检查网络连接';
      notifyListeners();
      return {
        'success': false,
        'message': '网络错误，请检查网络连接',
        'data': null,
      };
    }
  }
  
  /// 重新发送验证码
  Future<bool> resendVerifyCode() async {
    if (_currentPhoneNumber.isEmpty) return false;
    return await sendVerifyCode(_currentPhoneNumber);
  }
  
  /// 重置状态
  void reset() {
    _errorMessage = '';
    _currentPhoneNumber = '';
    notifyListeners();
  }
  
  /// 清除错误信息
  void clearError() {
    _errorMessage = '';
    notifyListeners();
  }
  
  /// 设置用户列表
  void setUserList(List<UserInfo> userList) {
    _userList = userList;
    notifyListeners();
  }
  
  /// 选择用户
  Future<void> selectUser(UserInfo user) async {
    _selectedUser = user;
    await _storageService.saveSelectedUser(user);
    notifyListeners();
  }
  
  /// 删除用户
  Future<void> deleteUser(UserInfo user) async {
    _userList.remove(user);
    if (_selectedUser?.userId == user.userId) {
      _selectedUser = _userList.isNotEmpty ? _userList.first : null;
      await _storageService.saveSelectedUser(_selectedUser);
    }
    
    // 保存到本地存储
    await _storageService.saveUserList(_userList);
    notifyListeners();
  }
  
  /// 初始化用户数据
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // 从本地存储加载用户列表
      final userList = await _storageService.getUserList();
      _userList = userList;
      
      // 从本地存储加载选中的用户
      final selectedUser = await _storageService.getSelectedUser();
      _selectedUser = selectedUser;
      
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      print('初始化用户数据失败: $e');
    }
  }

  /// 添加用户
  Future<void> addUser(UserInfo user) async {
    // 检查是否已存在相同用户ID的用户
    final existingIndex = _userList.indexWhere((u) => u.userId == user.userId);
    if (existingIndex != -1) {
      // 更新现有用户
      _userList[existingIndex] = user;
    } else {
      // 添加新用户
      _userList.add(user);
    }
    
    // 保存到本地存储
    await _storageService.saveUserList(_userList);
    notifyListeners();
  }

  /// 清除所有用户数据
  Future<void> clearAllUsers() async {
    await _storageService.clearAllUsers();
    _userList.clear();
    _selectedUser = null;
    notifyListeners();
  }

  /// 微信登录
  Future<Map<String, dynamic>> wechatLogin() async {
    try {
      final result = await _apiService.wechatLogin();
      
      if (result['success']) {
        _errorMessage = '';
        
        // 登录成功，保存用户信息到历史账号
        if (result['data'] != null && result['data']['userInfo'] != null) {
          final userInfo = UserInfo.fromJson(result['data']['userInfo']);
          addUser(userInfo);
        }
      } else {
        _errorMessage = result['message'];
      }
      
      notifyListeners();
      return result;
    } catch (e) {
      _errorMessage = '网络错误，请检查网络连接';
      notifyListeners();
      return {
        'success': false,
        'message': '网络错误，请检查网络连接',
        'data': null,
      };
    }
  }

  /// 闪验登录
  Future<Map<String, dynamic>> fastLogin() async {
    try {
      _setLoading(true);
      _setShowingFastLogin(true);
      
      // 1. 检查环境
      final envSupported = await _fastLoginService.checkEnvironment();
      if (!envSupported) {
        throw FastLoginException('ENV_NOT_SUPPORT', '当前环境不支持闪验');
      }
      
      // 2. 初始化SDK
      await _fastLoginService.initialize();
      
      // 3. 执行闪验登录
      final result = await _fastLoginService.doFastLogin();
      
      _setLoading(false);
      _setShowingFastLogin(false);
      
      if (result['success'] == true) {
        _errorMessage = '';
        
        // TODO: 使用token调用后端接口验证用户身份
        // 目前直接获取token，需要后续调用后端验证接口
        final token = result['token'] as String?;
        if (token != null) {
          print('获取到闪验token: $token');
          // TODO: 调用后端验证token并获取用户信息
          // final userInfo = await _apiService.verifyFastLoginToken(token);
          // await addUser(userInfo);
        }
      }
      
      notifyListeners();
      return result;
    } catch (e) {
      _setLoading(false);
      _setShowingFastLogin(false);
      
      if (e is FastLoginCancelException) {
        _errorMessage = '用户取消登录';
      } else if (e is FastLoginException) {
        _errorMessage = e.message;
      } else {
        _errorMessage = '闪验登录失败：\$e';
      }
      
      notifyListeners();
      rethrow;
    }
  }
  

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setShowingFastLogin(bool showing) {
    _isShowingFastLogin = showing;
    notifyListeners();
  }
} 