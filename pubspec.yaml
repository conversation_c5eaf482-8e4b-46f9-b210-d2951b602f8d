name: dlyz_flutter
description: "dlyz_flutter"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  # http请求
  dio: ^5.4.2
  # JSON 序列化注解
  json_annotation: ^4.9.0
  # 数数 Flutter 插件
  thinking_analytics: ^3.1.0-beta.1
  # 键值对持久化
  shared_preferences: ^2.2.2
  # WebView组件
  webview_flutter: ^4.4.2
  # WebView平台实现
  webview_flutter_android: ^3.12.0
  webview_flutter_wkwebview: ^3.9.4
  # toast
  fluttertoast: ^8.2.12
  # 加解密
  encrypt: ^5.0.1
  # md5
  crypto: ^3.0.3
  # package info
  package_info_plus: ^8.3.0
  path_provider: ^2.0.0
  # 权限
  permission_handler: ^11.0.1
  # 状态管理
  provider: ^6.1.2
  # UUID生成
  uuid: ^4.3.3
  # 图片缓存
  cached_network_image: ^3.4.1
  # 设备信息
  device_info_plus: ^9.0.0
  # 已安装应用
  installed_apps: ^1.6.0
  # 微信SDK (不含支付功能)
  fluwx: ^5.7.0

  # 任务队列
  queue: ^3.1.0+2
# fluwx 配置
fluwx:
  # 应用配置
  no_pay: true                  # 排除支付功能
  # 是否启用微信登录功能 (默认启用)
  enable_auth: true


dev_dependencies:
  build_runner: ^2.4.0
  json_serializable: ^6.7.0
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/webview/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
