package com.sq.dlyz_flutter;

import androidx.annotation.NonNull;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;

import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import androidx.core.content.FileProvider;
import java.io.File;
import java.util.HashMap;
import java.util.Map;
import com.sq.auth.FastLoginManager;
import com.sq.auth.AuthResultCallback;

/**
 * Flutter与原生通信管理类
 */
public class DouluoManagerChannel implements MethodCallHandler {
    private static final String TAG = "DouluoManagerChannel";
    private static final String CHANNEL_NAME = "channel.control/dlyz";
    private final Context mContext;

    // 单例实例
    private static volatile DouluoManagerChannel instance;

    // MethodChannel实例，用于回调Flutter
    private MethodChannel methodChannel;

    /**
     * 私有构造函数
     */
    private DouluoManagerChannel(Context context) {
        this.mContext = context;
    }

    /**
     * 获取单例实例
     */
    public static DouluoManagerChannel getInstance(Context context) {
        if (instance == null) {
            synchronized (DouluoManagerChannel.class) {
                if (instance == null) {
                    instance = new DouluoManagerChannel(context);
                }
            }
        }
        return instance;
    }

    /**
     * 注册Flutter通信通道
     */
    public static void registerWith(BinaryMessenger messenger, Context context) {
        // 创建MethodChannel
        MethodChannel channel = new MethodChannel(messenger, CHANNEL_NAME);
        // 获取实例并设置MethodCallHandler
        DouluoManagerChannel instance = DouluoManagerChannel.getInstance(context);
        instance.methodChannel = channel; // 保存channel实例用于回调
        channel.setMethodCallHandler(instance);

        Log.d(TAG, "DouluoManagerChannel registered successfully");
    }

    @Override
    public void onMethodCall(MethodCall call, @NonNull Result result) {
        // 根据方法名处理不同的调用
        switch (call.method) {
            case "getPlatformVersion":
                handleGetPlatformVersion(call, result);
                break;
            case "checkGameInstalled":
                handleCheckGameInstalled(call, result);
                break;
            case "openInstalledGame":
                handleOpenInstalledGame(call, result);
                break;
            case "installApk":
                handleInstallApk(call, result);
                break;
            case "bindingGame":
                handleBindingGame(call, result);
                break;
            // 新增: 闪验相关方法
            case "setFastLoginConfig":
                handleSetFastLoginConfig(call, result);
                break;
            case "checkFastLoginEnvironment":
                handleCheckFastLoginEnvironment(result);
                break;
            case "initializeFastLogin":
                handleInitializeFastLogin(result);
                break;
            case "doFastLogin":
                handleDoFastLogin(result);
                break;

            default:
                // 未实现的方法
                result.notImplemented();
                break;
        }
    }

    /**
     * 获取平台版本
     */
    private void handleGetPlatformVersion(MethodCall call, Result result) {
        try {
            String version = android.os.Build.VERSION.RELEASE;
            result.success("Android " + version);
            Log.d(TAG, "Return platform version: " + version);
        } catch (Exception e) {
            Log.e(TAG, "Failed to get platform version: " + e.getMessage());
            result.error("GET_VERSION_FAILED", "获取平台版本失败", e.getMessage());
        }
    }

    /**
     * 检查游戏是否已安装
     */
    private void handleCheckGameInstalled(MethodCall call, Result result) {
        try {
            String packageName = call.argument("packageName");
            if (packageName == null || packageName.isEmpty()) {
                result.error("INVALID_PARAM", "包名不能为空", null);
                return;
            }

            // 检查游戏是否安装的逻辑
            PackageManager packageManager = mContext.getPackageManager();
            boolean isInstalled = false;
            try {
                // 尝试获取包信息，如果不存在会抛出NameNotFoundException
                packageManager.getPackageInfo(packageName, 0);
                isInstalled = true;
            } catch (PackageManager.NameNotFoundException e) {
                // 应用未安装
                isInstalled = false;
            }

            result.success(isInstalled);
            Log.d(TAG, "Check if game installed: " + packageName + " - " + isInstalled);
        } catch (Exception e) {
            Log.e(TAG, "Failed to check game installation: " + e.getMessage());
            result.error("CHECK_INSTALL_FAILED", "检查安装状态失败", e.getMessage());
        }
    }

    /**
     * 打开已安装的游戏
     */
    private void handleOpenInstalledGame(MethodCall call, Result result) {
        try {
            String packageName = call.argument("packageName");
            if (packageName == null || packageName.isEmpty()) {
                result.error("INVALID_PARAM", "包名不能为空", null);
                return;
            }
    
            // 获取游戏启动意图
            Intent launchIntent = mContext.getPackageManager().getLaunchIntentForPackage(packageName);
            if (launchIntent != null) {
                // 添加新任务标志，确保从后台启动时能正确打开
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                mContext.startActivity(launchIntent);
                Log.d(TAG, "Successfully opened game: " + packageName);
                result.success(true);
            } else {
                Log.e(TAG, "No launch intent found for package: " + packageName);
                result.error("LAUNCH_INTENT_NOT_FOUND", "未找到应用启动入口", null);
            }
        } catch (ActivityNotFoundException e) {
            Log.e(TAG, "Game not found: " + e.getMessage());
            result.error("GAME_NOT_FOUND", "游戏未安装", e.getMessage());
        } catch (SecurityException e) {
            Log.e(TAG, "Permission denied to open game: " + e.getMessage());
            result.error("PERMISSION_DENIED", "没有权限打开应用", e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "Failed to open game: " + e.getMessage());
            result.error("OPEN_GAME_FAILED", "打开游戏失败", e.getMessage());
        }
    }

    private void handleInstallApk(MethodCall call, Result result) {
        String filePath = call.argument("filePath");
        if (filePath == null || filePath.isEmpty()) {
            result.error("INVALID_PARAM", "文件路径不能为空", null);
            return;
        }

        File file = new File(filePath);
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

        Uri uri;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            uri = FileProvider.getUriForFile(mContext, mContext.getPackageName() + ".fileProvider", file);
        } else {
            uri = Uri.fromFile(file);
        }

        intent.setDataAndType(uri, "application/vnd.android.package-archive");
        mContext.startActivity(intent);
    }

    private void handleBindingGame(MethodCall call, Result result) {
        try {
            String packageName = call.argument("packageName");
            if (packageName == null || packageName.isEmpty()) {
                result.error("INVALID_PARAM", "包名不能为空", null);
                return;
            }

            // 获取游戏启动意图
            Intent launchIntent = mContext.getPackageManager().getLaunchIntentForPackage(packageName);
            if (launchIntent != null) {
                String bindingParams = call.argument("bindingParams");
                // 添加新任务标志，确保从后台启动时能正确打开
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                launchIntent.putExtra("bindingParams", bindingParams);
                mContext.startActivity(launchIntent);
                Log.d(TAG, "Successfully opened game: " + packageName);
                result.success(true);
            } else {
                Log.e(TAG, "No launch intent found for package: " + packageName);
                result.error("LAUNCH_INTENT_NOT_FOUND", "未找到应用启动入口", null);
            }
        } catch (ActivityNotFoundException e) {
            Log.e(TAG, "Game not found: " + e.getMessage());
            result.error("GAME_NOT_FOUND", "游戏未安装", e.getMessage());
        } catch (SecurityException e) {
            Log.e(TAG, "Permission denied to open game: " + e.getMessage());
            result.error("PERMISSION_DENIED", "没有权限打开应用", e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "Failed to open game: " + e.getMessage());
            result.error("OPEN_GAME_FAILED", "打开游戏失败", e.getMessage());
        }
    }

    /**
     * 设置闪验配置
     */
    private void handleSetFastLoginConfig(MethodCall call, Result result) {
        try {
            Map<String, Object> config = (Map<String, Object>) call.arguments;
            Log.d(TAG, "设置闪验配置: " + config);

            Object accessKeyObj = config.get("access_key");
            Object sceneCodeObj = config.get("scene_code");
            Object appKeyObj = config.get("app_key");
            Object userAgreementUrlObj = config.get("userAgreementUrl");
            Object privacyPolicyUrlObj = config.get("privacyPolicyUrl");

            String accessKey = accessKeyObj != null ? accessKeyObj.toString() : null;
            String sceneCode = sceneCodeObj != null ? sceneCodeObj.toString() : null;
            String appKey = appKeyObj != null ? appKeyObj.toString() : null;
            String userAgreementUrl = userAgreementUrlObj != null ? userAgreementUrlObj.toString() : null;
            String privacyPolicyUrl = privacyPolicyUrlObj != null ? privacyPolicyUrlObj.toString() : null;

            FastLoginManager fastLoginManager = FastLoginManager.getInstance(mContext);

            // 设置协议地址
            if (userAgreementUrl != null && privacyPolicyUrl != null) {
                fastLoginManager.setAgreementUrls(userAgreementUrl, privacyPolicyUrl);
                Log.d(TAG, "设置协议地址 - 用户协议: " + userAgreementUrl);
                Log.d(TAG, "设置协议地址 - 隐私政策: " + privacyPolicyUrl);
            }

            // 先设置解密密钥
            if (appKey != null && !appKey.isEmpty()) {
                fastLoginManager.setAppKey(appKey);
                Log.d(TAG, "Setting app key for decryption");
            }

            // 再设置accessKey（会使用appKey进行解密）
            if (accessKey != null && !accessKey.isEmpty()) {
                Log.d(TAG, "Setting access key: " + accessKey);
                Log.d(TAG, "Setting scene code: " + sceneCode);

                fastLoginManager.setAccessKey(accessKey);

                result.success(true);
            } else {
                Log.w(TAG, "No access key found in config");
                result.success(false);
            }

        } catch (Exception e) {
            Log.e(TAG, "Failed to set fast login config: " + e.getMessage());
            result.error("SET_CONFIG_FAILED", "设置闪验配置失败", e.getMessage());
        }
    }

    /**
     * 检查闪验环境
     */
    private void handleCheckFastLoginEnvironment(Result result) {
        try {
            Log.d(TAG, "Checking fast login environment");
            FastLoginManager.getInstance(mContext).getFastEnv(isSupported -> {
                Log.d(TAG, "Fast login environment check result: " + isSupported);
                result.success(isSupported);
            });
        } catch (Exception e) {
            Log.e(TAG, "Failed to check fast login environment: " + e.getMessage());
            result.error("CHECK_ENV_FAILED", "检查闪验环境失败", e.getMessage());
        }
    }

    /**
     * 初始化闪验SDK
     */
    private void handleInitializeFastLogin(Result result) {
        try {
            Log.d(TAG, "Initializing fast login SDK");
            FastLoginManager.getInstance(mContext).initFastAccessKey(new AuthResultCallback() {
                @Override
                public void onSuccess() {
                    Log.d(TAG, "Fast login SDK initialized successfully");
                    result.success(true);
                }

                @Override
                public void onFailure(int code, String msg) {
                    Log.e(TAG, "Failed to initialize fast login SDK: " + code + " - " + msg);
                    result.error("INIT_FAILED", msg, String.valueOf(code));
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Exception during fast login initialization: " + e.getMessage());
            result.error("INIT_EXCEPTION", "初始化异常", e.getMessage());
        }
    }

    /**
     * 执行闪验登录
     */
    private void handleDoFastLogin(Result result) {
        try {
            Log.d(TAG, "Starting fast login process");
            FastLoginManager.getInstance(mContext).doFastVerifyLogin(new FastLoginManager.FastLoginListener() {
                @Override
                public void onFastLoginSuccess(String token) {
                    Log.d(TAG, "Fast login successful, token: " + token);
                    Map<String, Object> resultMap = new HashMap<>();
                    resultMap.put("success", true);
                    resultMap.put("token", token);
                    result.success(resultMap);
                }

                @Override
                public void onFastLoginFail(Bundle bundle) {
                    String code = bundle.getString("CODE", "UNKNOWN");
                    String message = bundle.getString("MESSAGE", "Unknown error");
                    Log.e(TAG, "Fast login failed: " + code + " - " + message);
                    result.error(code, message, null);
                }

                @Override
                public void onFastRelease() {
                    Log.d(TAG, "Fast login UI released");
                    // UI释放事件可以在成功或失败回调中一起处理
                }

                @Override
                public void onVerifyAccount(boolean needOpen) {
                    Log.d(TAG, "Verify account callback: needOpen=" + needOpen);
                    // 账号验证回调可以通过其他方式处理，或者合并到结果中
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Exception during fast login: " + e.getMessage());
            result.error("FAST_LOGIN_EXCEPTION", "闪验登录异常", e.getMessage());
        }
    }

    /**
     * 闪验UI关闭按钮点击回调
     */
    public void onFastLoginCloseClicked() {
        try {
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("event", "close_clicked");
                methodChannel.invokeMethod("onFastLoginUIEvent", arguments);
                Log.d(TAG, "Fast login close event sent to Flutter");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send close event to Flutter: " + e.getMessage());
        }
    }

    /**
     * 闪验UI返回按钮点击回调
     */
    public void onFastLoginBackClicked() {
        try {
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("event", "back_clicked");
                methodChannel.invokeMethod("onFastLoginUIEvent", arguments);
                Log.d(TAG, "Fast login back event sent to Flutter");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send back event to Flutter: " + e.getMessage());
        }
    }

    /**
     * 忘记密码点击回调
     */
    public void onFastLoginForgetPasswordClicked() {
        try {
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("event", "forget_password_clicked");
                methodChannel.invokeMethod("onFastLoginUIEvent", arguments);
                Log.d(TAG, "Fast login forget password event sent to Flutter");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send forget password event to Flutter: " + e.getMessage());
        }
    }

    /**
     * 其他登录方式点击回调
     */
    public void onFastLoginSwitchMethodClicked() {
        try {
            if (methodChannel != null) {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("event", "switch_method_clicked");
                methodChannel.invokeMethod("onFastLoginUIEvent", arguments);
                Log.d(TAG, "Fast login switch method event sent to Flutter");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to send switch method event to Flutter: " + e.getMessage());
        }
    }
}
