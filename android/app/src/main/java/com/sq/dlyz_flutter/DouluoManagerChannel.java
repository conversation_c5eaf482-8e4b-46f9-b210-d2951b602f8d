package com.sq.dlyz_flutter;

import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;

import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.util.Log;

import androidx.core.content.FileProvider;

import java.io.File;

/**
 * Flutter与原生通信管理类
 */
public class DouluoManagerChannel implements MethodCallHandler {
    private static final String TAG = "DouluoManagerChannel";
    private static final String CHANNEL_NAME = "channel.control/dlyz";
    private final Context mContext;

    // 单例实例
    private static DouluoManagerChannel instance;

    /**
     * 私有构造函数
     */
    private DouluoManagerChannel(Context context) {
        this.mContext = context;
    }

    /**
     * 获取单例实例
     */
    public static DouluoManagerChannel getInstance(Context context) {
        if (instance == null) {
            synchronized (DouluoManagerChannel.class) {
                if (instance == null) {
                    instance = new DouluoManagerChannel(context);
                }
            }
        }
        return instance;
    }

    /**
     * 注册Flutter通信通道
     */
    public static void registerWith(BinaryMessenger messenger, Context context) {
        // 创建MethodChannel
        MethodChannel channel = new MethodChannel(messenger, CHANNEL_NAME);
        // 获取实例并设置MethodCallHandler
        DouluoManagerChannel instance = DouluoManagerChannel.getInstance(context);
        channel.setMethodCallHandler(instance);
        Log.d(TAG, "DouluoManagerChannel registered successfully");
    }

    @Override
    public void onMethodCall(MethodCall call, Result result) {
        // 根据方法名处理不同的调用
        switch (call.method) {
            case "getPlatformVersion":
                handleGetPlatformVersion(call, result);
                break;
            case "checkGameInstalled":
                handleCheckGameInstalled(call, result);
                break;
            case "openInstalledGame":
                handleOpenInstalledGame(call, result);
                break;
            case "installApk":
                handleInstallApk(call, result);
                break;
            default:
                // 未实现的方法
                result.notImplemented();
                break;
        }
    }

    /**
     * 获取平台版本
     */
    private void handleGetPlatformVersion(MethodCall call, Result result) {
        try {
            String version = android.os.Build.VERSION.RELEASE;
            result.success("Android " + version);
            Log.d(TAG, "Return platform version: " + version);
        } catch (Exception e) {
            Log.e(TAG, "Failed to get platform version: " + e.getMessage());
            result.error("GET_VERSION_FAILED", "获取平台版本失败", e.getMessage());
        }
    }

    /**
     * 检查游戏是否已安装
     */
    private void handleCheckGameInstalled(MethodCall call, Result result) {
        try {
            String packageName = call.argument("packageName");
            if (packageName == null || packageName.isEmpty()) {
                result.error("INVALID_PARAM", "包名不能为空", null);
                return;
            }

            // 检查游戏是否安装的逻辑
            PackageManager packageManager = mContext.getPackageManager();
            boolean isInstalled = false;
            try {
                // 尝试获取包信息，如果不存在会抛出NameNotFoundException
                packageManager.getPackageInfo(packageName, 0);
                isInstalled = true;
            } catch (PackageManager.NameNotFoundException e) {
                // 应用未安装
                isInstalled = false;
            }

            result.success(isInstalled);
            Log.d(TAG, "Check if game installed: " + packageName + " - " + isInstalled);
        } catch (Exception e) {
            Log.e(TAG, "Failed to check game installation: " + e.getMessage());
            result.error("CHECK_INSTALL_FAILED", "检查安装状态失败", e.getMessage());
        }
    }

    /**
     * 打开已安装的游戏
     */
    private void handleOpenInstalledGame(MethodCall call, Result result) {
        try {
            String packageName = call.argument("packageName");
            if (packageName == null || packageName.isEmpty()) {
                result.error("INVALID_PARAM", "包名不能为空", null);
                return;
            }
    
            // 获取游戏启动意图
            Intent launchIntent = mContext.getPackageManager().getLaunchIntentForPackage(packageName);
            if (launchIntent != null) {
                // 添加新任务标志，确保从后台启动时能正确打开
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                mContext.startActivity(launchIntent);
                Log.d(TAG, "Successfully opened game: " + packageName);
                result.success(true);
            } else {
                Log.e(TAG, "No launch intent found for package: " + packageName);
                result.error("LAUNCH_INTENT_NOT_FOUND", "未找到应用启动入口", null);
            }
        } catch (ActivityNotFoundException e) {
            Log.e(TAG, "Game not found: " + e.getMessage());
            result.error("GAME_NOT_FOUND", "游戏未安装", e.getMessage());
        } catch (SecurityException e) {
            Log.e(TAG, "Permission denied to open game: " + e.getMessage());
            result.error("PERMISSION_DENIED", "没有权限打开应用", e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "Failed to open game: " + e.getMessage());
            result.error("OPEN_GAME_FAILED", "打开游戏失败", e.getMessage());
        }
    }

    private void handleInstallApk(MethodCall call, Result result) {
        String filePath = call.argument("filePath");
        if (filePath == null || filePath.isEmpty()) {
            result.error("INVALID_PARAM", "文件路径不能为空", null);
            return;
        }

        File file = new File(filePath);
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

        Uri uri;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            uri = FileProvider.getUriForFile(mContext, mContext.getPackageName() + ".fileProvider", file);
        } else {
            uri = Uri.fromFile(file);
        }

        intent.setDataAndType(uri, "application/vnd.android.package-archive");
        mContext.startActivity(intent);
    }
}
