package com.sq.dlyz_flutter;

import com.sq.dlyz_flutter.flutterdownloader.FlutterDownloaderPlugin;
import com.sq.dlyz_flutter.flutterdownloader.FlutterDownloaderPluginKt;

import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodChannel;

public class MainActivity extends FlutterActivity {
    @Override
    public void configureFlutterEngine(FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);
        // 注册DouluoManagerChannel通信通道
        DouluoManagerChannel.registerWith(flutterEngine.getDartExecutor().getBinaryMessenger(), this);
        // 注册FlutterDownloaderPlugin通信通道
        FlutterDownloaderPlugin.registerWith(flutterEngine.getDartExecutor().getBinaryMessenger(), this);
    }
}
