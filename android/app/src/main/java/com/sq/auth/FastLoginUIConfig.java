package com.sq.auth;
import android.content.pm.ActivityInfo;
import android.content.Context;
import android.graphics.Color;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup.LayoutParams;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import com.mobile.auth.gatewayauth.AuthUIConfig;
import com.mobile.auth.gatewayauth.PhoneNumberAuthHelper;
import com.mobile.auth.gatewayauth.AuthRegisterViewConfig;
import com.mobile.auth.gatewayauth.AuthRegisterXmlConfig;
import com.mobile.auth.gatewayauth.ui.AbstractPnsViewDelegate;
import com.sq.dlyz_flutter.R;
import com.sq.dlyz_flutter.DouluoManagerChannel;

/**
 * 一键登录UI配置管理器
 * 提供自定义的UI样式配置和交互处理
 */
public class FastLoginUIConfig {
    
    private static final String TAG = "FastLoginUIConfig";
    
    /**
     * 创建一键登录UI配置
     * 设置弹窗样式、按钮外观、隐私协议等界面元素
     */
    public static AuthUIConfig createCustomUIConfig(Context context) {
        int unit = 5;
        
        // 从FastLoginManager获取协议地址
        FastLoginManager manager = FastLoginManager.getInstance(context);
        String userAgreementUrl = manager.getUserAgreementUrl();
        String privacyPolicyUrl = manager.getPrivacyPolicyUrl();
        
        return new AuthUIConfig.Builder()
            .setAppPrivacyOne("《用户协议》", userAgreementUrl)
            .setAppPrivacyTwo("《隐私政策》", privacyPolicyUrl)
            .setAppPrivacyColor(Color.GRAY, Color.parseColor("#ff7017"))
            .setPrivacyConectTexts(new String[]{"及"})
            .setPrivacyTextSizeDp(12)
            //设置运营商协议显示位置
            .setPrivacyOperatorIndex(2)
            .setPrivacyState(false)
            .setPrivacyOffsetY_B(10)
            .setNavHidden(true)
            .setDialogBottom(false)
            .setProtocolAction(context.getPackageName() + ".protocolWeb")
            .setLogoHidden(true)
            .setSloganHidden(true)
            .setAuthPageActIn("in_activity", "out_activity")
            .setAuthPageActOut("in_activity", "out_activity")
            .setNumFieldOffsetY(12)
            .setNumberSizeDp(17)
            .setNumberColor(Color.parseColor("#FF212121"))
            .setLogBtnWidth(280)
            .setLogBtnHeight(38)
            .setLogBtnMarginLeftAndRight(20)
            .setLogBtnTextSizeDp(17)
            .setNavReturnImgDrawable(context.getResources().getDrawable(android.R.drawable.ic_menu_close_clear_cancel))
            .setLogBtnText("本机号码一键登录")
            .setLogBtnTextColor(Color.WHITE)
            .setLogBtnBackgroundPath("sysq_dialog_login_btn_bg")
            .setLogBtnOffsetY(unit + 45)
            .setPageBackgroundPath("sysq_dialog_login_bg")
            .setVendorPrivacyPrefix("《")
            .setVendorPrivacySuffix("》")
            .setCheckboxHidden(false)
            .setCheckedImgDrawable(context.getResources().getDrawable(R.drawable.sysq_dialog_login_privacy_check_box))
            .setDialogWidth(320)
            .setDialogHeight(230)
            .setScreenOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
            .setLogBtnToastHidden(true)
            .create();
    }
    
    /**
     * dp转px工具方法
     */
    private static int dip2px(Context context, float dpValue) {
        return (int) TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP, 
            dpValue, 
            context.getResources().getDisplayMetrics()
        );
    }
    
    /**
     * 初始化自定义视图组
     */
    private static View initViewGroup(Context context, int marginTop, DouluoManagerChannel channel) {
        LinearLayout linearLayout = new LinearLayout(context);
        RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(
            LayoutParams.WRAP_CONTENT,
            dip2px(context, 50)
        );
        layoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL, RelativeLayout.TRUE);
        layoutParams.setMargins(0, dip2px(context, marginTop), 0, 0);
        linearLayout.setOrientation(LinearLayout.HORIZONTAL);
        linearLayout.setLayoutParams(layoutParams);
        linearLayout.addView(initForgetPwdView(context, channel));
        linearLayout.addView(initDivide(context));
        linearLayout.addView(initSwitchView(context, channel));
        return linearLayout;
    }
    
    /**
     * 创建忘记密码按钮视图
     */
    private static View initForgetPwdView(Context context, DouluoManagerChannel channel) {
        TextView forgetPwdTV = new TextView(context);
        RelativeLayout.LayoutParams mLayoutParams = new RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.WRAP_CONTENT, 
            dip2px(context, 50)
        );
        forgetPwdTV.setText("找回账号/密码");
        forgetPwdTV.setTextColor(Color.parseColor("#FF212121"));
        forgetPwdTV.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13.0F);
        forgetPwdTV.setLayoutParams(mLayoutParams);
        forgetPwdTV.setOnClickListener(v -> {
            Log.d(TAG, "点击忘记密码");
            if (channel != null) {
                channel.onFastLoginForgetPasswordClicked();
            }
        });
        return forgetPwdTV;
    }
    
    /**
     * 创建分隔线视图
     */
    private static View initDivide(Context context) {
        TextView forgetPwdTV = new TextView(context);
        RelativeLayout.LayoutParams mLayoutParams = new RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.WRAP_CONTENT, 
            dip2px(context, 50)
        );
        forgetPwdTV.setText("   |   ");
        forgetPwdTV.setTextColor(Color.parseColor("#FF212121"));
        forgetPwdTV.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13.0F);
        forgetPwdTV.setLayoutParams(mLayoutParams);
        return forgetPwdTV;
    }
    
    /**
     * 创建切换登录方式按钮视图
     */
    private static View initSwitchView(Context context, DouluoManagerChannel channel) {
        TextView switchTV = new TextView(context);
        RelativeLayout.LayoutParams mLayoutParams = new RelativeLayout.LayoutParams(
            RelativeLayout.LayoutParams.WRAP_CONTENT, 
            dip2px(context, 50)
        );
        switchTV.setText("其他登录方式");
        switchTV.setTextColor(Color.parseColor("#FF212121"));
        switchTV.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13.0F);
        switchTV.setLayoutParams(mLayoutParams);
        switchTV.setOnClickListener(v -> {
            Log.d(TAG, "点击其他登录方式");
            if (channel != null) {
                channel.onFastLoginSwitchMethodClicked();
            }
        });
        return switchTV;
    }
    
    /**
     * 配置一键登录界面样式
     * 设置自定义视图和XML布局配置
     */
    public static void configAuthPage(PhoneNumberAuthHelper authHelper, Context context, DouluoManagerChannel channel) {
        try {
            // 清理之前的配置
            authHelper.removeAuthRegisterXmlConfig();
            authHelper.removeAuthRegisterViewConfig();

            // 添加自定义视图配置
            int unit = 5;
            authHelper.addAuthRegistViewConfig("view_group", new AuthRegisterViewConfig.Builder()
                .setView(initViewGroup(context, unit + 95, channel))
                .setRootViewId(AuthRegisterViewConfig.RootViewId.ROOT_VIEW_ID_BODY)
                .build());
            Log.d(TAG, "添加自定义视图配置成功");
            
            // 添加XML布局配置
            authHelper.addAuthRegisterXmlConfig(new AuthRegisterXmlConfig.Builder()
                .setLayout(R.layout.sysq_custom_port_dialog_action_bar, new AbstractPnsViewDelegate() {
                    @Override
                    public void onViewCreated(View view) {
                        Log.i(TAG, "addAuthRegisterXmlConfig onViewCreated");
                        
                        // 关闭按钮处理
                        view.findViewById(R.id.view_close).setOnClickListener(v -> {
                            Log.d(TAG, "点击关闭按钮");
                            FastLoginManager.getInstance(context).quitLoginPage();
                            if (channel != null) {
                                channel.onFastLoginCloseClicked();
                            }
                        });
                        
                        // 返回按钮处理
                        view.findViewById(R.id.view_back).setOnClickListener(v -> {
                            Log.d(TAG, "点击返回按钮");
                            FastLoginManager.getInstance(context).quitLoginPage();
                            if (channel != null) {
                                channel.onFastLoginBackClicked();
                            }
                        });
                    }
                })
                .build());

            // 设置基础AuthUIConfig
            AuthUIConfig uiConfig = createCustomUIConfig(context);
            authHelper.setAuthUIConfig(uiConfig);
        } catch (Exception e) {
            Log.e(TAG, "配置一键登录UI失败", e);
        }
    }
}