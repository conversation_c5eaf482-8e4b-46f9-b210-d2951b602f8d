<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:background="@android:color/transparent">

    <!-- 返回按钮区域 -->
    <LinearLayout
        android:id="@+id/view_back"
        android:layout_width="50dp"
        android:layout_height="match_parent"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:gravity="center">

        <ImageView
            android:id="@+id/btn_back"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_back_arrow" />

    </LinearLayout>

    <!-- 关闭按钮区域 -->
    <LinearLayout
        android:id="@+id/view_close"
        android:layout_width="50dp"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:gravity="center">

        <ImageView
            android:id="@+id/btn_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_close_x" />

    </LinearLayout>

    <!-- 标题文本 -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:text="手机号登录"
        android:textColor="#333333"
        android:textSize="18sp"
        android:textStyle="bold"
        android:visibility="gone" />

    <!-- Logo图片 -->
    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="120dp"
        android:layout_height="30dp"
        android:layout_centerInParent="true"
        android:scaleType="centerInside"
        android:src="@drawable/sysq_ic_logo"
        android:visibility="visible" />

</RelativeLayout>