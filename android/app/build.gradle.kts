import java.util.Properties
import java.io.FileInputStream

plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.sy.yxjun"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        applicationId = "com.sy.yxjun"
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    /**
     * 签名设置
     */
    signingConfigs {
        create("release") {
            val propFile = file("signing.properties")
            if (propFile.exists()) {
                val props = Properties()
                props.load(FileInputStream(propFile))
                if (props.containsKey("STORE_FILE") && props.containsKey("STORE_PASSWORD") &&
                    props.containsKey("KEY_ALIAS") && props.containsKey("KEY_PASSWORD")) {
                    storeFile = file(props["STORE_FILE"] as String)
                    storePassword = props["STORE_PASSWORD"] as String
                    keyAlias = props["KEY_ALIAS"] as String
                    keyPassword = props["KEY_PASSWORD"] as String
                }
            }
        }
        
        create("customDebug") {
            // debug版本也使用相同的签名配置，但使用不同的名称
            val propFile = file("signing.properties")
            if (propFile.exists()) {
                val props = Properties()
                props.load(FileInputStream(propFile))
                if (props.containsKey("STORE_FILE") && props.containsKey("STORE_PASSWORD") &&
                    props.containsKey("KEY_ALIAS") && props.containsKey("KEY_PASSWORD")) {
                    storeFile = file(props["STORE_FILE"] as String)
                    storePassword = props["STORE_PASSWORD"] as String
                    keyAlias = props["KEY_ALIAS"] as String
                    keyPassword = props["KEY_PASSWORD"] as String
                }
            }
        }
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.getByName("release")
            isMinifyEnabled = false
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
            isShrinkResources = false // Kotlin DSL 正确写法
        }
        debug {
            signingConfig = signingConfigs.getByName("customDebug") // 使用自定义debug签名
            isMinifyEnabled = false
        }
    }
}

dependencies {
    compileOnly("androidx.annotation:annotation:1.6.0")
    implementation("androidx.core:core-ktx:1.13.1")
    implementation("androidx.work:work-runtime:2.9.0")
}

flutter {
    source = "../.."
}
