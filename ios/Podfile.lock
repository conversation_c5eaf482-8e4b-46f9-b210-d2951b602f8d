PODS:
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - fluttertoast (0.0.2):
    - Flutter
  - fluwx (0.0.1):
    - Flutter
    - fluwx/pay (= 0.0.1)
  - fluwx/pay (0.0.1):
    - Flutter
    - WechatOpenSDK-XCFramework (~> 2.0.5)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - TAThirdParty (0.3.5)
  - thinking_analytics (3.2.0):
    - Flutter
    - TAThirdParty (= 0.3.5)
    - ThinkingSDK (= 3.1.0)
  - ThinkingDataCore (1.2.1):
    - ThinkingDataCore/Main (= 1.2.1)
  - ThinkingDataCore/iOS (1.2.1)
  - ThinkingDataCore/Main (1.2.1):
    - ThinkingDataCore/iOS
    - ThinkingDataCore/OSX
    - ThinkingDataCore/tvOS
    - ThinkingDataCore/versionOS
    - ThinkingDataCore/watchOS
  - ThinkingSDK (3.1.0):
    - ThinkingSDK/Main (= 3.1.0)
  - ThinkingSDK/iOS (3.1.0):
    - ThinkingDataCore (= 1.2.1)
  - ThinkingSDK/Main (3.1.0):
    - ThinkingSDK/iOS
    - ThinkingSDK/OSX
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - WechatOpenSDK-XCFramework (2.0.5)

DEPENDENCIES:
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - fluwx (from `.symlinks/plugins/fluwx/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - thinking_analytics (from `.symlinks/plugins/thinking_analytics/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - TAThirdParty
    - ThinkingDataCore
    - ThinkingSDK
    - WechatOpenSDK-XCFramework

EXTERNAL SOURCES:
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  fluwx:
    :path: ".symlinks/plugins/fluwx/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  thinking_analytics:
    :path: ".symlinks/plugins/thinking_analytics/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  device_info_plus: c6fb39579d0f423935b0c9ce7ee2f44b71b9fce6
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  fluttertoast: 21eecd6935e7064cc1fcb733a4c5a428f3f24f0f
  fluwx: 95a2274c23418c1098940bd00dcfe7c975bb0550
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqflite_darwin: 5a7236e3b501866c1c9befc6771dfd73ffb8702d
  TAThirdParty: 65db0235cd209237781535f19f9d0eef706156e5
  thinking_analytics: 782567244b508490b2ec4433d548ca8998251885
  ThinkingDataCore: a9af2a3c645c928f6555dbfb447f9c89381976d9
  ThinkingSDK: 55612adb5d90fac6c1ea0a9c8651b619cb86f5fe
  webview_flutter_wkwebview: a4af96a051138e28e29f60101d094683b9f82188
  WechatOpenSDK-XCFramework: b072030c9eeee91dfff1856a7846f70f7b9a88ed

PODFILE CHECKSUM: 4305caec6b40dde0ae97be1573c53de1882a07e5

COCOAPODS: 1.16.2
